<template>
  <div class="qr-type-inputs">
    <!-- 랜딩 페이지 선택 (QR 코드 타입이 랜딩페이지일 때만 표시) -->
    <div class="form-group" v-if="formData.qrType === 'LANDING_PAGE'">
      <label for="landingPageId">랜딩 페이지 선택 <span class="required">*</span></label>
      <div v-if="isLoadingLandingPages" class="loading-indicator">
        랜딩 페이지 목록을 불러오는 중...
      </div>
      <select
        v-else
        id="landingPageId"
        v-model="selectedLandingPageId"
        required
        :disabled="isEditMode"
        @change="handleLandingPageChange"
      >
        <option value="">선택하세요</option>
        <option v-for="page in landingPages" :key="page.landingPageId || page.id" :value="page.landingPageId || page.id">
          {{ page.pageTitle || page.title || page.name || '랜딩 페이지 ' + (page.landingPageId || page.id) }}
        </option>
      </select>
      <div v-if="landingPages.length === 0 && !isLoadingLandingPages" class="field-note error">
        사용 가능한 랜딩 페이지가 없습니다. 먼저 랜딩 페이지를 생성해주세요.
      </div>
    </div>

    <!-- 이벤트 선택 (QR 코드 타입이 이벤트일 때만 표시) -->
    <div class="form-group" v-if="formData.qrType === 'EVENT_ATTENDANCE'">
      <label for="eventId">이벤트 선택 <span class="required">*</span></label>
      <div v-if="isLoadingEvents" class="loading-indicator">이벤트 목록을 불러오는 중...</div>
      <select 
        v-else 
        id="eventId" 
        v-model="selectedEventId" 
        required
        @change="handleEventChange"
      >
        <option value="">선택하세요</option>
        <option v-for="eventItem in events" :key="eventItem.eventId" :value="eventItem.eventId">
          {{ eventItem.eventName }}
        </option>
      </select>
      <div v-if="events.length === 0 && !isLoadingEvents" class="field-note error">
        사용 가능한 이벤트가 없습니다. 먼저 이벤트를 생성해주세요.
      </div>
    </div>

    <!-- 위치 타입일 때 위치 선택 UI -->
    <div class="form-group" v-if="formData.qrType === 'LOCATION'">
      <label for="locationTargetContent">위치 정보 (QR 코드 내용) <span class="required">*</span></label>
      <div class="location-target-inputs">
        <div class="location-input-group">
          <label for="targetLatitude">위도</label>
          <input
            type="text"
            id="targetLatitude"
            v-model="targetLocationData.latitude"
            placeholder="위도 (예: 37.5665)"
            required
            :disabled="isEditMode"
            @input="updateLocationTargetContent"
          />
        </div>
        <div class="location-input-group">
          <label for="targetLongitude">경도</label>
          <input
            type="text"
            id="targetLongitude"
            v-model="targetLocationData.longitude"
            placeholder="경도 (예: 126.9780)"
            required
            :disabled="isEditMode"
            @input="updateLocationTargetContent"
          />
        </div>
      </div>
      <div class="location-address">
        <label for="targetLocationAddress">주소</label>
        <input
          type="text"
          id="targetLocationAddress"
          v-model="targetLocationData.address"
          placeholder="주소 정보"
          readonly
          :disabled="isEditMode"
        />
      </div>
      <button
        type="button"
        class="map-select-button location-target-button"
        @click="openTargetLocationMap"
        :disabled="isEditMode"
      >
        카카오맵에서 위치 선택
      </button>
      <div class="field-note">
        QR 코드에 스캔시 보여질 위치 정보입니다. 위도와 경도를 직접 입력하거나 카카오맵에서 위치를 선택할 수 있습니다.
      </div>
      <div class="target-content-preview" v-if="formData.targetContent">
        <label>타겟 콘텐츠 미리보기:</label>
        <div class="preview-content">{{ formData.targetContent }}</div>
      </div>
    </div>

    <!-- Wi-Fi 타입일 때 Wi-Fi 정보 입력 UI -->
    <div class="form-group" v-if="formData.qrType === 'WIFI'">
      <label for="wifiInfo">Wi-Fi 정보 (QR 코드 내용) <span class="required">*</span></label>
      <div class="wifi-inputs">
        <div class="wifi-input-group">
          <label for="wifiSsid">네트워크 이름 (SSID) <span class="required">*</span></label>
          <input
            type="text"
            id="wifiSsid"
            v-model="wifiData.ssid"
            placeholder="Wi-Fi 네트워크 이름"
            required
            @input="updateWifiTargetContent"
          />
        </div>

        <div class="wifi-input-group">
          <label for="wifiSecurityType">보안 유형 <span class="required">*</span></label>
          <select 
            id="wifiSecurityType" 
            v-model="wifiData.securityType"
            @change="updateWifiTargetContent"
          >
            <option value="WPA">WPA/WPA2/WPA3</option>
            <option value="WEP">WEP</option>
            <option value="">보안 없음</option>
          </select>
        </div>

        <div class="wifi-input-group" v-if="wifiData.securityType">
          <label for="wifiPassword">비밀번호 <span class="required">*</span></label>
          <div class="password-input-container">
            <input
              :type="showPassword ? 'text' : 'password'"
              id="wifiPassword"
              v-model="wifiData.password"
              placeholder="Wi-Fi 비밀번호"
              required
              @input="updateWifiTargetContent"
            />
            <button
              type="button"
              class="toggle-password-button"
              @click="togglePasswordVisibility"
            >
              {{ showPassword ? '숨기기' : '보기' }}
            </button>
          </div>
        </div>

        <div class="wifi-checkbox-group">
          <input
            type="checkbox"
            id="wifiHidden"
            v-model="wifiData.hidden"
            @change="updateWifiTargetContent"
          />
          <label for="wifiHidden">숨겨진 네트워크</label>
        </div>
      </div>
      <div class="field-note">
        QR 코드를 스캔하면 자동으로 Wi-Fi에 연결됩니다. 정확한 정보를 입력해주세요.
      </div>
      <div class="target-content-preview" v-if="formData.targetContent">
        <label>타겟 콘텐츠 미리보기:</label>
        <div class="preview-content">{{ formData.targetContent }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { getLandingPages, getAllLandingPages } from '@/api/landing';
import { getEvents, getAllEvents } from '@/api/events';

// Props 정의
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  currentProject: {
    type: Object,
    default: null
  }
});

// Emits 정의
const emit = defineEmits([
  'landing-page-change',
  'event-change', 
  'target-location-map-open',
  'target-content-update'
]);

// 랜딩 페이지 관련 상태
const landingPages = ref([]);
const isLoadingLandingPages = ref(false);
const selectedLandingPageId = ref('');

// 이벤트 관련 상태
const events = ref([]);
const isLoadingEvents = ref(false);
const selectedEventId = ref('');

// QR 코드 타겟 위치 관련 상태
const targetLocationData = ref({
  latitude: '',
  longitude: '',
  address: ''
});

// QR 코드 타입이 'WIFI'일 때 Wi-Fi 정보 상태
const wifiData = ref({
  ssid: '',
  securityType: 'WPA', // 기본값: WPA/WPA2/WPA3
  password: '',
  hidden: false
});
const showPassword = ref(false); // 비밀번호 표시 여부

// 랜딩 페이지 목록 가져오기
const fetchLandingPages = async () => {
  isLoadingLandingPages.value = true;
  try {
    let response;

    // SUPER_ADMIN이고 전체 프로젝트 모드인 경우
    if (props.currentProject && props.currentProject.isAllProjectsMode) {
      response = await getAllLandingPages();
    }
    // 일반 프로젝트 모드인 경우
    else if (props.currentProject && props.currentProject.projectId) {
      response = await getLandingPages(props.currentProject.projectId);
    }
    // 프로젝트 정보가 없는 경우
    else {
      console.error('프로젝트 정보가 없습니다. 랜딩 페이지 목록을 불러올 수 없습니다.');
      landingPages.value = [];
      return;
    }

    // 응답 구조에 따라 데이터 추출
    if (response.data && response.data.content) {
      landingPages.value = response.data.content;
    } else if (response.data && Array.isArray(response.data)) {
      landingPages.value = response.data;
    } else {
      landingPages.value = [];
    }
  } catch (error) {
    console.error('랜딩 페이지 목록 로드 실패:', error);
    landingPages.value = [];
  } finally {
    isLoadingLandingPages.value = false;
  }
};

// 이벤트 목록 가져오기
const fetchEvents = async () => {
  isLoadingEvents.value = true;
  try {
    let response;

    // SUPER_ADMIN이고 전체 프로젝트 모드인 경우
    if (props.currentProject && props.currentProject.isAllProjectsMode) {
      const apiResponse = await getAllEvents();
      // getAllEvents는 페이지네이션 응답을 반환하므로 content 추출
      if (apiResponse.data && apiResponse.data.content) {
        response = apiResponse.data.content;
      } else {
        response = [];
      }
    }
    // 일반 프로젝트 모드인 경우
    else if (props.currentProject && props.currentProject.projectId) {
      response = await getEvents(props.currentProject.projectId);
    }
    // 프로젝트 정보가 없는 경우
    else {
      console.error('프로젝트 정보가 없습니다. 이벤트 목록을 불러올 수 없습니다.');
      events.value = [];
      return;
    }

    // getEvents 함수는 이미 배열을 반환하도록 처리되어 있음
    events.value = response || [];
  } catch (error) {
    console.error('이벤트 목록 로드 실패:', error);
    events.value = [];
  } finally {
    isLoadingEvents.value = false;
  }
};

// 이벤트 핸들러들
const handleLandingPageChange = () => {
  emit('landing-page-change', selectedLandingPageId.value);
};

const handleEventChange = () => {
  emit('event-change', selectedEventId.value);
};

const openTargetLocationMap = () => {
  emit('target-location-map-open');
};

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 위치 정보 업데이트
const updateLocationTargetContent = () => {
  if (targetLocationData.value.latitude && targetLocationData.value.longitude) {
    const targetContent = `geo:${targetLocationData.value.latitude},${targetLocationData.value.longitude}`;
    emit('target-content-update', targetContent);
  }
};

// Wi-Fi 정보 업데이트
const updateWifiTargetContent = () => {
  const { ssid, securityType, password, hidden } = wifiData.value;
  if (ssid) {
    let wifiString = `WIFI:T:${securityType || 'nopass'};S:${ssid};`;
    if (securityType && password) {
      wifiString += `P:${password};`;
    }
    if (hidden) {
      wifiString += 'H:true;';
    }
    wifiString += ';';
    emit('target-content-update', wifiString);
  }
};

// 외부에서 접근 가능한 함수들과 데이터 expose
defineExpose({
  fetchLandingPages,
  fetchEvents,
  selectedLandingPageId,
  selectedEventId,
  targetLocationData,
  wifiData
});
</script>
