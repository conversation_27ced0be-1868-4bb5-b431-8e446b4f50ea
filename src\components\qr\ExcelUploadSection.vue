<template>
  <div class="excel-upload-section">
    <button type="button" @click="downloadExcelTemplate">엑셀 템플릿 다운로드</button>
    <div style="margin-top: 10px;">
      <input 
        type="file" 
        @change="handleExcelFileSelected" 
        accept=".xlsx,.xls,.csv" 
        id="excelFileInput" 
      />
    </div>
    <div v-if="excelFileName" style="margin-top: 10px;" class="excel-file-info">
      <div>선택된 파일: {{ excelFileName }}</div>
      <button type="button" class="delete-excel-btn" @click="removeExcelFile">삭제</button>
    </div>
    <div v-if="excelValidationError" class="error-message" style="margin-top: 10px; color: red;">
      {{ excelValidationError }}
    </div>
    <p>* 한번에 최대 100개까지 등록 가능합니다.</p>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import * as XLSX from 'xlsx';

// Props 정의
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
});

// Emits 정의
const emit = defineEmits([
  'excel-file-change',
  'excel-sample-data-change',
  'generate-qr-code'
]);

// 엑셀 관련 상태
const excelFile = ref(null);
const excelFileName = ref('');
const excelValidationError = ref('');

// 엑셀에서 추출한 QR 코드 샘플 데이터 (미리보기용)
const excelSampleQrData = ref({
  isActive: false,
  qrType: '',
  targetContent: ''
});

// 엑셀 템플릿 다운로드 함수
const downloadExcelTemplate = () => {
  // 엑셀 템플릿 생성
  const worksheet = XLSX.utils.aoa_to_sheet([
    ['QR코드 타입(URL, TEXT, SNS_LINL 만 입력)', '타겟 콘텐츠'], // 컬럼 헤더
    ['URL', 'https://www.example.com'], // 예시 데이터 1
    ['TEXT', '내용을 텍스트로 입력해주세요.'], // 예시 데이터 2
    ['SNS_LINK', 'https://www.instagram.com/example'] // 예시 데이터 3
  ]);
  
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, '템플릿');
  
  // 파일 다운로드
  XLSX.writeFile(workbook, 'qr_code_template.xlsx');
};

// 엑셀 파일 삭제 함수
const removeExcelFile = () => {
  // 엑셀 파일 관련 변수 초기화
  excelFile.value = null;
  excelFileName.value = '';
  excelValidationError.value = '';
  
  // 엑셀 샘플 데이터 초기화
  excelSampleQrData.value = {
    isActive: false,
    qrType: '',
    targetContent: ''
  };
  
  // 파일 입력 요소 초기화
  const fileInput = document.getElementById('excelFileInput');
  if (fileInput) {
    fileInput.value = '';
  }
  
  // 부모 컴포넌트에 변경 사항 알림
  emit('excel-file-change', null);
  emit('excel-sample-data-change', excelSampleQrData.value);
};

// 엑셀 파일 선택 핸들러
const handleExcelFileSelected = (event) => {
  const file = event.target.files[0];
  if (!file) {
    excelFile.value = null;
    excelFileName.value = '';
    emit('excel-file-change', null);
    return;
  }
  
  // 파일 확장자 검사
  const fileExt = file.name.split('.').pop().toLowerCase();
  if (!['xlsx', 'xls', 'csv'].includes(fileExt)) {
    alert('지원되는 파일 형식은 .xlsx, .xls, .csv 입니다.');
    event.target.value = ''; // 파일 선택 초기화
    excelFile.value = null;
    excelFileName.value = '';
    emit('excel-file-change', null);
    return;
  }
  
  excelFile.value = file;
  excelFileName.value = file.name;
  
  // 부모 컴포넌트에 파일 변경 알림
  emit('excel-file-change', file);
  
  // 파일 선택 후 미리보기를 위해 파일 파싱 시작
  parseExcelForPreview(file);
};

// 엑셀 파일 미리보기용 파싱
const parseExcelForPreview = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    excelValidationError.value = ''; // 유효성 검사 메시지 초기화
    try {
      const data = e.target.result;
      const workbook = XLSX.read(data, { type: 'binary' });
      
      // 첫 번째 시트 사용
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 배열로 변환
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 1. 데이터 유무 확인 (헤더 포함 최소 2줄)
      if (jsonData.length < 2) {
        excelValidationError.value = '엑셀 파일에 데이터가 없습니다. (헤더 제외 최소 1줄의 데이터 필요)';
        removeExcelFile(); // 파일 정보 초기화
        return;
      }

      // 2. 엑셀 양식(헤더) 확인
      const headers = jsonData[0].map(h => typeof h === 'string' ? h.trim().toLowerCase() : '');
      
      const qrTypeHeaderActual = headers.find(h => h.includes('qr코드 타입'));
      const targetContentHeaderActual = headers.find(h => h.includes('타겟 콘텐츠'));

      if (!qrTypeHeaderActual || !targetContentHeaderActual) {
        excelValidationError.value = '엑셀 파일의 양식이 올바르지 않습니다. 템플릿 파일을 확인해주세요. (필수 컬럼: QR코드 타입, 타겟 콘텐츠)';
        removeExcelFile(); // 파일 정보 초기화
        return;
      }
      
      // 데이터가 충분한지 확인 (헤더 제외하고 실제 데이터가 있는지)
      const actualDataRows = jsonData.slice(1).filter(row => row.some(cell => cell !== null && cell !== undefined && String(cell).trim() !== ''));
      if (actualDataRows.length === 0) {
        excelValidationError.value = '엑셀 파일에 실제 데이터가 없습니다. 내용을 채워주세요.';
        removeExcelFile(); // 파일 정보 초기화
        return;
      }

      // 헤더 인덱스 찾기 (좀 더 유연하게)
      const qrTypeColumnIndex = headers.findIndex(header => header.includes('qr코드 타입'));
      const targetContentColumnIndex = headers.findIndex(header => header.includes('타겟 콘텐츠'));
        
      // 첫 번째 데이터 행을 사용하여 QR 코드 미리보기 생성 (실제 데이터가 있는 첫 번째 행 사용)
      const firstValidDataRow = actualDataRows[0];
      if (firstValidDataRow && firstValidDataRow[qrTypeColumnIndex] !== undefined && firstValidDataRow[targetContentColumnIndex] !== undefined) {
        // 엑셀 샘플 데이터 저장
        excelSampleQrData.value.isActive = true;
        excelSampleQrData.value.qrType = String(firstValidDataRow[qrTypeColumnIndex]);
        excelSampleQrData.value.targetContent = String(firstValidDataRow[targetContentColumnIndex]);
        
        // 부모 컴포넌트에 샘플 데이터 변경 알림
        emit('excel-sample-data-change', excelSampleQrData.value);
        
        // QR 코드 미리보기 생성 요청
        emit('generate-qr-code');
      } else {
        // 미리보기 생성에 필요한 데이터가 없는 경우
        excelSampleQrData.value.isActive = false;
        excelValidationError.value = '미리보기를 생성할 첫 번째 데이터 행에 QR코드 타입 또는 타겟 콘텐츠 정보가 부족합니다.';
        emit('excel-sample-data-change', excelSampleQrData.value);
      }

    } catch (error) {
      console.error('엑셀 파일 파싱 오류:', error);
      excelValidationError.value = '엑셀 파일을 처리하는 중 오류가 발생했습니다. 파일 형식을 확인해주세요.';
      removeExcelFile(); // 파일 정보 초기화
    }
  };
  
  reader.onerror = (error) => {
    console.error('파일 읽기 오류:', error);
  };
  
  reader.readAsBinaryString(file);
};

// 외부에서 접근 가능한 함수들 expose
defineExpose({
  removeExcelFile,
  excelFile,
  excelFileName,
  excelValidationError
});
</script>
