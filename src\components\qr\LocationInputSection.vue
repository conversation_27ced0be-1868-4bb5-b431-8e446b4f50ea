<template>
  <div class="location-input-section">
    <!-- QR 코드 설치 위치 입력 -->
    <div class="form-group">
      <label for="locationInfo">QR 코드 설치 위치</label>
      <div class="location-inputs">
        <div class="location-input-group">
          <label for="latitude">위도</label>
          <input
            type="text"
            id="latitude"
            v-model="locationData.latitude"
            placeholder="위도 (예: 37.5665)"
          />
        </div>
        <div class="location-input-group">
          <label for="longitude">경도</label>
          <input
            type="text"
            id="longitude"
            v-model="locationData.longitude"
            placeholder="경도 (예: 126.9780)"
          />
        </div>
      </div>
      <div class="location-address">
        <label for="locationAddress">주소</label>
        <input
          type="text"
          id="locationAddress"
          v-model="locationData.address"
          placeholder="주소 정보"
        />
      </div>
      <button
        type="button"
        class="map-select-button"
        @click="openKakaoMap"
      >
        카카오맵에서 위치 선택
      </button>
      <div class="field-note">
        QR 코드가 설치될 위치의 위도와 경도를 직접 입력하거나 카카오맵에서 위치를 선택할 수 있습니다.
      </div>
      
      <!-- QR 코드 설치 위치 사진 업로드 -->
      <div class="location-image-upload">
        <h4>QR 코드 설치 사진</h4>
        <p class="field-note">설치 위치의 사진을 업로드해주세요. (최대 크기: 5MB)</p>
        
        <div class="location-image-preview-container">
          <!-- 이미지 미리보기 -->
          <div v-if="locationImagePreview" class="location-image-preview">
            <img :src="locationImagePreview" alt="Location Image Preview" class="location-image" @error="handleImageError" />
            <button type="button" @click="removeLocationImage" class="remove-image-btn">삭제</button>
          </div>
          
          <!-- 파일 업로드 버튼 -->
          <div v-if="showFileInput" class="file-upload-container">
            <input
              type="file"
              ref="locationImageInput"
              :key="fileInputKey"
              accept="image/*"
              @change="handleLocationImageUpload"
              style="display: none;"
            />
            <button
              type="button"
              @click="triggerLocationImageUpload"
              class="upload-btn"
            >
              사진 업로드
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 카카오맵 모달 -->
    <KakaoMapModal
      :is-visible="showKakaoMapModal"
      :initial-location="locationData.latitude && locationData.longitude ? {
        lat: parseFloat(locationData.latitude),
        lng: parseFloat(locationData.longitude),
        address: locationData.address
      } : null"
      @close="showKakaoMapModal = false"
      @select-location="handleLocationSelect"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import KakaoMapModal from '@/components/map/KakaoMapModal.vue';

// Props 정의
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      latitude: '',
      longitude: '',
      address: ''
    })
  },
  locationImageFile: {
    type: File,
    default: null
  },
  locationImagePreview: {
    type: String,
    default: ''
  }
});

// Emits 정의
const emit = defineEmits([
  'update:modelValue',
  'update:locationImageFile',
  'update:locationImagePreview',
  'image-error'
]);

// 위치 데이터 상태
const locationData = ref({ ...props.modelValue });

// 이미지 관련 상태
const locationImageInput = ref(null);
const fileInputKey = ref(0);
const showFileInput = ref(true);
const showKakaoMapModal = ref(false);

// 위치 데이터 변경 감지 및 부모 컴포넌트로 전달
watch(locationData, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });

// Props 변경 감지
watch(() => props.modelValue, (newValue) => {
  locationData.value = { ...newValue };
}, { deep: true });

// 카카오맵 모달 열기
const openKakaoMap = () => {
  showKakaoMapModal.value = true;
};

// 위치 선택 처리
const handleLocationSelect = (location) => {
  locationData.value = {
    latitude: location.lat,
    longitude: location.lng,
    address: location.address
  };
};

// 위치 이미지 업로드 트리거
const triggerLocationImageUpload = () => {
  locationImageInput.value?.click();
};

// 위치 이미지 업로드 처리
const handleLocationImageUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 파일 크기 검사 (5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    alert('파일 크기가 5MB를 초과합니다. 더 작은 파일을 선택해주세요.');
    return;
  }

  // 이미지 파일 검사
  if (!file.type.startsWith('image/')) {
    alert('이미지 파일만 업로드할 수 있습니다.');
    return;
  }

  // 파일과 미리보기 URL 생성
  const reader = new FileReader();
  reader.onload = (e) => {
    emit('update:locationImageFile', file);
    emit('update:locationImagePreview', e.target.result);
  };
  reader.readAsDataURL(file);
};

// 위치 이미지 삭제
const removeLocationImage = () => {
  emit('update:locationImageFile', null);
  emit('update:locationImagePreview', '');
  
  // 파일 입력 요소 리셋
  fileInputKey.value += 1;
  showFileInput.value = false;
  setTimeout(() => {
    showFileInput.value = true;
  }, 10);
};

// 이미지 에러 처리
const handleImageError = (event) => {
  // no-image 이미지로 대체
  event.target.src = '/src/assets/image/no-image.png';
  emit('image-error', event);
};

// 위치 정보 변경 감지 (디버깅용)
watch([() => locationData.value.latitude, () => locationData.value.longitude], ([newLat, newLng]) => {
  if (newLat && newLng) {
    // 필요시 추가 로직
  }
});
</script>

<style scoped>
.location-input-section {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.location-inputs {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.location-input-group {
  flex: 1;
}

.location-input-group label {
  font-size: 14px;
  margin-bottom: 5px;
}

.location-input-group input,
.location-address input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.location-address {
  margin-bottom: 15px;
}

.map-select-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 10px;
}

.map-select-button:hover {
  background-color: #0056b3;
}

.field-note {
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
}

.location-image-upload h4 {
  margin: 20px 0 10px 0;
  font-size: 16px;
  color: #333;
}

.location-image-preview-container {
  margin-top: 10px;
}

.location-image-preview {
  position: relative;
  display: inline-block;
  margin-bottom: 10px;
}

.location-image {
  max-width: 200px;
  max-height: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 12px;
}

.remove-image-btn:hover {
  background-color: #c82333;
}

.file-upload-container {
  margin-top: 10px;
}

.upload-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-btn:hover {
  background-color: #218838;
}
</style>
