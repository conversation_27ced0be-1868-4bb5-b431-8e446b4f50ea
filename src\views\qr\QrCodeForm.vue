<template>
  <div class="qr-code-form">
    <h1>{{ isEditMode ? 'QR 코드 수정' : 'QR 코드 생성' }}</h1>

    <div v-if="isLoading" class="loading">
      데이터를 불러오는 중...
    </div>

    <form v-else @submit.prevent="handleSubmit" class="form-container">
      <!-- 기본 폼 필드 컴포넌트 -->
      <BasicFormFields
        :form-data="formData"
        :is-edit-mode="isEditMode"
        :excel-file="excelFile"
        @qr-type-change="handleQrTypeChange"
      />

      <!-- 엑셀 파일 업로드 영역 (수정 모드가 아닐 때만 표시) -->
      <ExcelUploadSection
        v-if="!isEditMode"
        ref="excelUploadRef"
        :form-data="formData"
        @excel-file-change="handleExcelFileChange"
        @excel-sample-data-change="handleExcelSampleDataChange"
        @generate-qr-code="generateClientSideQrCode"
      />

      <!-- QR 타입별 특화 입력 필드 -->
      <QrTypeInputs
        ref="qrTypeInputsRef"
        :form-data="formData"
        :is-edit-mode="isEditMode"
        :current-project="currentProject"
        @landing-page-change="handleLandingPageChange"
        @event-change="handleEventChange"
        @target-location-map-open="openTargetLocationMap"
        @target-content-update="handleTargetContentUpdate"
      />





      <!-- QR 코드 설치 위치 입력 -->
      <LocationInputSection
        v-model="locationData"
        v-model:location-image-file="locationImageFile"
        v-model:location-image-preview="locationImagePreview"
        @image-error="handleImageError"
      />

      <!-- 문제 연결 섹션 (수정 모드에서만 표시) -->
      <div v-if="isEditMode" class="form-group quiz-link-section">
        <div v-if="route.params.qrCodeId" class="mt-3">
          <QuizLinkStatus :qr-code-id="route.params.qrCodeId" @open-quiz-select-modal="showQuizSelectModal = true" />
        </div>
        <div class="field-note">QR 스캔 시 지정된 문제 페이지로 연결됩니다.</div>
      </div>
      
      <div class="form-group" v-if="!isEditMode">
        <label>QR 코드 디자인 옵션</label>
        <div class="design-options-container">
          <!-- QR 코드 디자인 옵션 -->
          <DesignOptionsSection
            :is-edit-mode="isEditMode"
            v-model:qr-color="qrColor"
            v-model:qr-bg-color="qrBgColor"
            v-model:qr-eye-color="qrEyeColor"
            v-model:qr-eye-style="qrEyeStyle"
            v-model:qr-error-correction-level="qrErrorCorrectionLevel"
            v-model:logo-preview="logoPreview"
            v-model:logo-size="logoSize"
            v-model:logo-file="logoFile"
            @logo-upload="handleLogoUpload"
            @logo-remove="handleLogoRemove"
            @image-error="handleImageError"
          />

          <!-- 캔버스 박스 사용 토글 -->
            <div class="form-group design-group">
              <div class="toggle-container">
                <label for="useA4Canvas">캔버스 박스 사용</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="useA4Canvas" v-model="useA4Canvas" :disabled="isEditMode" />
                  <label for="useA4Canvas"></label>
                </div>
              </div>
              <div class="field-note">
                자유롭게 크기를 조절할 수 있는 캔버스 박스를 사용하여 QR코드와 배경 이미지 위치를 정확히 설정할 수 있습니다.
              </div>
            </div>

            <!-- 배경 이미지 추가 토글 (A4 박스 사용 시에만 표시) -->
            <div class="form-group design-group" v-if="useA4Canvas">
              <div class="toggle-container">
                <label for="useBackgroundImage">배경 이미지 추가</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="useBackgroundImage" v-model="useBackgroundImage" :disabled="isEditMode" />
                  <label for="useBackgroundImage"></label>
                </div>
              </div>
              <div v-if="useBackgroundImage" class="background-image-container">
                <div class="background-image-controls">
                  <div v-if="backgroundImagePreview" class="bg-image-info">
                    <span class="bg-image-name">이미지: {{ backgroundImageFile ? backgroundImageFile.name : '배경 이미지' }}</span>
                    <button type="button" class="secondary-btn remove-bg-btn-outside" @click="removeBackgroundImage" :disabled="isEditMode">배경 제거</button>
                  </div>
                  <div v-else class="bg-upload-outside">
                    <label for="backgroundFile" class="background-upload-btn">
                      <span class="upload-icon">+</span>
                      <span>배경 이미지 선택</span>
                    </label>
                    <input
                      type="file"
                      id="backgroundFile"
                      ref="backgroundInput"
                      @change="handleBackgroundUpload"
                      accept="image/png,image/jpeg,image/svg+xml"
                      class="background-file-input"
                      :disabled="isEditMode"
                    />
                  </div>

                  <div v-if="backgroundImagePreview" class="canvas-toolbar">
                    <span>이미지 조절:</span>
                    <div class="image-controls">
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'fill'}"
                        @click="setBackgroundFitMode('fill')"
                      >
                        채우기
                      </button>
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'fit'}"
                        @click="setBackgroundFitMode('fit')"
                      >
                        맞추기
                      </button>
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'original'}"
                        @click="setBackgroundFitMode('original')"
                      >
                        원본
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 캔버스 박스 (A4 박스 사용 시에만 표시) -->
            <div v-if="useA4Canvas" class="form-group a4-canvas-section">
              <h4>캔버스 박스</h4>
              <div class="canvas-size-display">
                크기: {{ Math.round(a4CanvasWidth) }} x {{ Math.round(a4CanvasHeight) }} px
                ({{ cmA4CanvasWidth }} x {{ cmA4CanvasHeight }} cm)
              </div>
              <div class="a4-canvas">
                <!-- A4 캔버스 부모 컨테이너 -->
                <div class="a4-canvas-parent-container" data-a4-canvas-parent-container>
                  <!-- A4 캔버스 (크기 조절만 가능) -->
                  <DraggableResizableVue3
                    :parent="true"
                    v-model:x="a4CanvasX"
                    v-model:y="a4CanvasY"
                    v-model:w="a4CanvasWidth"
                    v-model:h="a4CanvasHeight"
                    :lockAspectRatio="false"
                    :draggable="false"
                    :resizable="true"
                    class="a4-canvas-draggable-container"
                  >
                  <div class="canvas-container" ref="a4CanvasContainer">
                    <!-- 배경 이미지 (크기 조절 가능) -->
                    <div v-if="useBackgroundImage && backgroundImagePreview" class="background-container-wrapper">
                      <DraggableResizableVue3
                        :parent="true"
                        v-model:x="backgroundPositionX"
                        v-model:y="backgroundPositionY"
                        v-model:w="backgroundWidth"
                        v-model:h="backgroundHeight"
                        :minW="100"
                        :minH="100"
                        :class="{ 'inactive-draggable': !isBackgroundImageActive }"
                        @activated="isBackgroundImageActive = true; isQrCodeActive = false"
                        @deactivated="isBackgroundImageActive = false"
                      >
                        <div class="background-image-preview">
                          <img
                            :src="backgroundImagePreview"
                            class="background-preview"
                            :class="[`bg-${backgroundFitMode}`]"
                          />
                        </div>
                      </DraggableResizableVue3>
                    </div>

                    <!-- QR 코드 (크기 조절 가능) -->
                    <div class="qr-container-wrapper" v-if="formData && (formData.generatedQrDataUrl || formData.imageUrl || (formData.targetContent && formData.targetContent.trim() !== '')) && !imageLoadError">
                      <DraggableResizableVue3
                        v-model:x="qrPositionX"
                        v-model:y="qrPositionY"
                        v-model:w="qrWidth"
                        v-model:h="qrHeight"
                        :minW="50"
                        :minH="50"
                        :maxW="200"
                        :maxH="200"
                        :lockAspectRatio="true"
                        :class="{ 'inactive-draggable': !isQrCodeActive }"
                        @activated="isQrCodeActive = true; isBackgroundImageActive = false"
                        @deactivated="isQrCodeActive = false"
                      >
                        <div class="qr-on-canvas">
                          <img
                            v-if="formData.generatedQrDataUrl || formData.imageUrl"
                            :src="formData.generatedQrDataUrl || formData.imageUrl"
                            alt="QR Code on Canvas"
                            class="qr-image-on-canvas"
                          />
                          <div v-else class="qr-placeholder-on-canvas">
                            QR 코드
                          </div>
                        </div>
                      </DraggableResizableVue3>
                    </div>
                    </div>
                  </DraggableResizableVue3>
                </div>
              </div>
              <div class="field-note">
                캔버스 박스의 크기를 자유롭게 조절할 수 있습니다. 상하좌우 선 가운데의 핸들을 드래그하여 각 방향별로 독립적으로 크기를 조절하세요.
                <br>• 좌측/우측 핸들: 너비 조절
                <br>배경 이미지 위치: X={{ mmBackgroundPositionX }}mm, Y={{ mmBackgroundPositionY }}mm (크기: {{ mmBackgroundWidth }}mm × {{ mmBackgroundHeight }}mm)
              </div>
            </div>
            <button type="button" @click="resetDesignOptions" class="btn btn-secondary btn-sm reset-btn" :disabled="isEditMode">스타일 초기화</button>


        </div>
      </div>


      <!-- QR 코드 미리보기 섹션 -->
      <QrPreviewSection
        :is-edit-mode="isEditMode"
        :qr-image-url="formData.imageUrl"
        :has-target-content="!!(formData.targetContent || excelSampleQrData.isActive)"
        :target-content="formData.targetContent"
        :qr-type="formData.qrType"
        :qr-color="qrColor"
        :qr-bg-color="qrBgColor"
        :qr-eye-color="qrEyeColor"
        :qr-eye-style="qrEyeStyle"
        :qr-dots-style="qrDotsStyle"
        :qr-error-correction-level="qrErrorCorrectionLevel"
        :logo-preview="logoPreview"
        :logo-size="logoSize"
        :use-a4-canvas="useA4Canvas"
        :is-design-update-only="isDesignUpdateOnly"
        :excel-sample-data="excelSampleQrData"
        :selected-event-id="selectedEventId"
        :initial-target-content="initialTargetContent"
        :qr-version="qrVersion"
        @qr-generated="handleQrGenerated"
        @qr-download="handleQrDownload"
        @image-error="handleImageError"
        @scan-reliability-calculated="handleScanReliabilityCalculated"
      />



      <div v-if="error" class="error-message form-error">
        {{ error }}
      </div>

      <div class="form-actions" data-form-actions>
        <button type="button" @click="goBack" class="cancel-btn">취소</button>
        <button type="submit" class="submit-btn" :disabled="isSubmitting">
          {{ isSubmitting ? '처리 중...' : (isEditMode ? '수정' : '생성') }}
        </button>
      </div>
      
      <!-- 배치 생성 결과 섹션 -->
      <BatchResultsSection
        :batch-creation-list="qrBatchCreationList"
        :is-batch-processing="isBatchProcessing"
        :batch-progress="batchProgress"
        :batch-global-error="batchGlobalError"
      />
    </form>
    


    <!-- QR 코드 타겟 위치 선택용 카카오맵 모달 -->
    <KakaoMapModal
      :is-visible="showTargetLocationMapModal"
      :initial-location="qrTypeInputsRef?.targetLocationData?.latitude && qrTypeInputsRef?.targetLocationData?.longitude ? {
        lat: parseFloat(qrTypeInputsRef.targetLocationData.latitude),
        lng: parseFloat(qrTypeInputsRef.targetLocationData.longitude),
        address: qrTypeInputsRef.targetLocationData.address
      } : null"
      @close="showTargetLocationMapModal = false"
      @select-location="handleTargetLocationSelect"
    />

    <!-- 문제 선택 모달 -->
    <QuizSelectModal
      v-if="showQuizSelectModal"
      :is-visible="showQuizSelectModal"
      :qr-code-id="route.params.qrCodeId"
      @close="showQuizSelectModal = false"
      @link-success="handleLinkSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick, defineAsyncComponent } from 'vue';

import { useRouter, useRoute } from 'vue-router';
import { getQrCodeById, createQrCode, updateQrCode } from '@/api/qrcode';
import QRCodeStyling from 'qr-code-styling'; // 새 라이브러리 import
import { useAuthStore } from '@/stores/auth';
import { formatISO, parseISO } from 'date-fns';
import { getLandingPages } from '@/api/landing';
import { getEvents } from '@/api/events';
import DraggableResizableVue3 from 'draggable-resizable-vue3';
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css';
import { useImageError } from '@/composables/useImageError';
import { useQrForm } from '@/composables/useQrForm';
import { useQrDesign } from '@/composables/useQrDesign';
import { useQrGeneration } from '@/composables/useQrGeneration';
import { useQrBatch } from '@/composables/useQrBatch';
import {
  formatDateForInput,
  formatDateForServer,
  calculateScanReliability,
  constrainA4CanvasPosition,
  pixelToMm,
  mmToPixel,
  parseExcelFile,
  scrollToBottom,
  validateQrFormData,
  A4_WIDTH_MM,
  A4_HEIGHT_MM
} from '@/utils/qrFormUtils';
import QuizLinkStatus from '@/components/qr/QuizLinkStatus.vue';
import BasicFormFields from '@/components/qr/BasicFormFields.vue';
import ExcelUploadSection from '@/components/qr/ExcelUploadSection.vue';
import QrTypeInputs from '@/components/qr/QrTypeInputs.vue';
import LocationInputSection from '@/components/qr/LocationInputSection.vue';
import DesignOptionsSection from '@/components/qr/DesignOptionsSection.vue';
import QrPreviewSection from '@/components/qr/QrPreviewSection.vue';
import BatchResultsSection from '@/components/qr/BatchResultsSection.vue';
import KakaoMapModal from '@/components/map/KakaoMapModal.vue';

const QuizSelectModal = defineAsyncComponent(() => import('@/components/common/QuizSelectModal.vue'));

const qrCode = ref(null); // QR 코드 데이터 저장용 변수 추가

// 이미지 에러 핸들링
const { handleImageError } = useImageError();

// 라우터 및 라우트 객체
const router = useRouter();
const route = useRoute();

// 폼 상태 관리 컴포저블 (디자인 상태를 매개변수로 전달)
const {
  // 상태
  isLoading,
  error,
  isSubmitting,
  imageLoadError,
  formData,
  locationData,
  locationImageFile,
  locationImagePreview,
  excelFile,
  excelSampleQrData,
  showQuizSelectModal,
  showTargetLocationMapModal,
  excelUploadRef,
  qrTypeInputsRef,
  isQrCodeGenerated,
  initialTargetContent,
  isDesignUpdateOnly,
  // 계산된 속성
  currentProject,
  isEditMode,
  selectedEventId,
  events,
  // 메서드
  getFrontendDomain,
  handleExcelFileChange,
  handleExcelSampleDataChange,
  handleLandingPageChange,
  handleEventChange,
  handleTargetContentUpdate,
  openTargetLocationMap,
  handleTargetLocationSelect,
  handleQrTypeChange,
  handleLinkSuccess,
  goBack
} = useQrForm();

// 디자인 상태 관리 컴포저블
const {
  // 상태
  qrColor,
  qrBgColor,
  qrEyeColor,
  qrEyeStyle,
  qrDotsStyle,
  qrErrorCorrectionLevel,
  qrVersion,
  scanReliability,
  logoPreview,
  logoSize,
  originalLogoSize,
  logoFile,
  qrCodeInstance,
  qrCodePreviewContainer,
  useA4Canvas,
  useBackgroundImage,
  a4CanvasX,
  a4CanvasY,
  a4CanvasWidth,
  a4CanvasHeight,
  backgroundInput,
  backgroundImageFile,
  backgroundImagePreview,
  backgroundFitMode,
  qrPositionX,
  qrPositionY,
  qrWidth,
  qrHeight,
  backgroundPositionX,
  backgroundPositionY,
  backgroundWidth,
  backgroundHeight,
  isQrCodeActive,
  isBackgroundImageActive,
  a4CanvasContainer,
  designOptions,
  // 계산된 속성
  cmA4CanvasWidth,
  cmA4CanvasHeight,
  mmPositionX,
  mmPositionY,
  mmWidth,
  mmHeight,
  mmA4CanvasWidth,
  mmA4CanvasHeight,
  mmBackgroundPositionX,
  mmBackgroundPositionY,
  mmBackgroundWidth,
  mmBackgroundHeight,
  // 메서드
  calculateQrVersion,
  calculateLogoSize,
  onA4CanvasDrag,
  onA4CanvasResize,
  handleLogoUpload,
  handleLogoRemove,
  handleBackgroundUpload,
  removeBackgroundImage,
  setBackgroundFitMode,
  resetDesignOptions,
  generateA4CanvasImage,
  initializeA4CanvasPosition
} = useQrDesign();



// QR 생성 관리 컴포저블
const {
  generateClientSideQrCode: generateQrCode,
  createGenerateQrCodeDebounced,
  forceQrContainerSize
} = useQrGeneration();

// 배치 생성 관리 컴포저블
const {
  // 상태 (기존 변수들을 대체)
  qrBatchCreationList,
  isBatchProcessing,
  batchProgress,
  batchGlobalError,
  parsedExcelRows,
  // 메서드
  createSingleQrCode,
  startBatchCreation,
  stopBatchCreation
} = useQrBatch();

// QR 코드 생성 함수 래퍼
const generateClientSideQrCode = async () => {
  await generateQrCode(
    qrCodePreviewContainer,
    formData,
    excelSampleQrData,
    selectedEventId,
    isEditMode,
    isDesignUpdateOnly,
    initialTargetContent,
    getFrontendDomain,
    qrColor,
    qrBgColor,
    qrEyeColor,
    qrEyeStyle,
    qrDotsStyle,
    qrErrorCorrectionLevel,
    logoPreview,
    logoSize,
    qrCodeInstance,
    qrVersion,
    scanReliability,
    isQrCodeGenerated,
    useA4Canvas,
    calculateQrVersion,
    calculateLogoSize,
    forceQrContainerSize
  );
};


// 디바운스된 QR 코드 생성 함수 (디자인 변경 시 사용)
const generateQrCodeDebounced = createGenerateQrCodeDebounced(() => {
  isDesignUpdateOnly.value = true;
  generateClientSideQrCode().finally(() => {
    isDesignUpdateOnly.value = false;
  });
});

// 디자인 옵션 변경 시 QR 코드 재생성 (디바운스 적용)
watch([qrColor, qrBgColor, qrEyeColor, qrDotsStyle, qrEyeStyle, logoPreview, logoSize, qrErrorCorrectionLevel], () => {
  // 수정 모드에서는 디자인 옵션 변경에 반응하지 않음
  if (isEditMode.value) {
    return;
  }

  // 생성 모드에서 QR코드가 생성되었거나, 타겟 콘텐츠가 있을 때만 디자인 변경 시 업데이트
  if (isQrCodeGenerated.value || (formData.value.targetContent && formData.value.targetContent.trim() !== '')) {
    generateQrCodeDebounced();
  }
}, { deep: true });

// A4 캔버스 사용 여부 변경 시 QR 코드 미리보기 업데이트
watch(useA4Canvas, (newValue) => {
  if (newValue) {
    // A4 캔버스 활성화 시 QR 코드 위치 및 크기 강제 초기화
    nextTick(() => {
      // QR 코드 크기와 위치 강제 설정
      qrPositionX.value = 50;
      qrPositionY.value = 50;
      qrWidth.value = 100;
      qrHeight.value = 100;

      // DOM 업데이트 후 QR 코드 생성
      if (formData.value.targetContent || excelSampleQrData.value.isActive || (formData.value.qrType === 'EVENT_ATTENDANCE' && selectedEventId.value)) {
        generateQrCodeDebounced();
      }
    });
  }
});

// 배경 이미지 사용 여부 변경 시 처리
watch(useBackgroundImage, (newValue) => {
  if (!newValue) {
    // 배경 이미지 비활성화 시 관련 데이터 초기화
    backgroundImagePreview.value = null;
    backgroundImageFile.value = null;
    backgroundFitMode.value = 'fit';
    if (backgroundInput.value) {
      backgroundInput.value.value = '';
    }
  }
});

// QR 코드 위치와 크기 변경 감지하여 formData에 저장
watch([qrPositionX, qrPositionY, qrWidth, qrHeight], () => {
  if (formData.value) {
    if (useA4Canvas.value) {
      // A4 캔버스 사용 시 mm 단위로 저장
      formData.value.qrPositionX = mmPositionX.value;
      formData.value.qrPositionY = mmPositionY.value;
      formData.value.qrWidth = mmWidth.value;
      formData.value.qrHeight = mmHeight.value;
    } else {
      // A4 캔버스 미사용 시 픽셀 단위로 저장
      formData.value.qrPositionX = qrPositionX.value;
      formData.value.qrPositionY = qrPositionY.value;
      formData.value.qrWidth = qrWidth.value;
      formData.value.qrHeight = qrHeight.value;
    }
  }
});

// 수정 모드일 경우 QR 코드 데이터 로드
const fetchQrCodeData = async () => {
  isLoading.value = true;
  error.value = '';
  try {
    const response = await getQrCodeById(route.params.qrCodeId);
    // 서버 응답 구조 확인 및 처리
    let qrData; // qrData 변수 선언 추가
    if (response && response.success === true && response.data) {
      // { success: true, data: {...} } 형태로 반환된 경우
      qrData = response.data;
    } else if (response && typeof response === 'object') {
      // 직접 데이터 객체가 반환된 경우
      qrData = response;
    } else {
      throw new Error('서버 응답 형식이 유효하지 않습니다.');
    }

    if (qrData && typeof qrData === 'object') {
      // 폼 데이터 설정 (날짜 변환 포함)
      formData.value = {
        ...(qrData || {}),
        validFromDate: qrData.validFromDate ? formatDateForInput(qrData.validFromDate) : '',
        validToDate: qrData.validToDate ? formatDateForInput(qrData.validToDate) : '',
        quizInfo: qrData.quizInfo || null, // 연결된 문제 정보 추가
      };

      // 디자인 옵션 파싱 및 적용
      if (qrData.designOptions) {
        try {
          const parsedOptions = JSON.parse(qrData.designOptions);
          qrColor.value = parsedOptions.dotsOptions?.color || '#000000';
          qrDotsStyle.value = parsedOptions.dotsOptions?.type === 'dots' ? 1 : 0;
          qrEyeColor.value = parsedOptions.dotsOptions?.eyeColor || qrColor.value; // 눈 색상 없으면 모듈 색상 따름
          qrEyeStyle.value = parsedOptions.dotsOptions?.eyeType === 'rounded' ? 1 : 0;
          qrBgColor.value = parsedOptions.backgroundOptions?.color || '#ffffff';
          qrErrorCorrectionLevel.value = parsedOptions.errorCorrectionLevel || 'M';

          // 로고 처리 (logoUrl 필드가 있는지 확인)
          if (qrData.logoUrl) {
              logoPreview.value = qrData.logoUrl; // 서버에서 제공된 로고 URL 사용
              // 로고 비율 적용 (옵션에 저장되어 있을 경우)
              if (parsedOptions.logoRatio) {
                 logoSize.value = parsedOptions.logoRatio * 100;
              }
          } else {
              logoPreview.value = ''; // 로고 없음
              logoSize.value = 10; // 기본 크기로 리셋
          }

        } catch (e) {
          console.error('기존 디자인 옵션 파싱 오류:', e);
          error.value = '기존 디자인 옵션을 불러오는 중 오류가 발생했습니다. 기본 디자인으로 표시됩니다.';
          // 파싱 실패 시 기본 디자인 값 유지
          resetDesignOptions(); // 오류 시 디자인 초기화
        }
      } else {
         // 디자인 옵션이 없는 경우 기본값 사용
         resetDesignOptions(); // 기본값으로 초기화
      }

      // 수정 모드에서는 초기 타겟 콘텐츠 저장
      initialTargetContent.value = formData.value.targetContent;

      // QR 코드 설치 위치 정보 파싱 (서버에서 위치 정보가 있는 경우)
      if (qrData.installationLocationLat && qrData.installationLocationLng) {
        locationData.value.latitude = qrData.installationLocationLat;
        locationData.value.longitude = qrData.installationLocationLng;
        locationData.value.address = qrData.installationLocation || '선택한 위치';
      }

      // QR 코드 설치 사진 경로 파싱 (서버에서 설치 사진 정보가 있는 경우)
      if (qrData.qrInstalledImagePath) {
        locationImagePreview.value = qrData.qrInstalledImagePath;
      }

      // QR 코드 타입이 '위치'일 때 타겟 위치 정보 파싱
      if (formData.value.qrType === 'LOCATION' && formData.value.targetContent) {
        // Google Maps URL 형식 파싱 시도 (https://maps.google.com/?q=위도,경도)
        const googleMapsMatch = formData.value.targetContent.match(/maps\.google\.com\/\?q=(-?\d+\.?\d*),(-?\d+\.?\d*)/);

        // geo: 형식 파싱 시도 (geo:위도,경도)
        const geoMatch = formData.value.targetContent.match(/geo:(-?\d+\.?\d*),(-?\d+\.?\d*)/);

        if (googleMapsMatch && googleMapsMatch.length >= 3) {
          // Google Maps URL 형식 파싱 성공
          if (qrTypeInputsRef.value) {
            qrTypeInputsRef.value.targetLocationData.latitude = googleMapsMatch[1];
            qrTypeInputsRef.value.targetLocationData.longitude = googleMapsMatch[2];
            qrTypeInputsRef.value.targetLocationData.address = '선택한 위치';
          }
        } else if (geoMatch && geoMatch.length >= 3) {
          // geo: 형식 파싱 성공 (이전 형식 지원)
          if (qrTypeInputsRef.value) {
            qrTypeInputsRef.value.targetLocationData.latitude = geoMatch[1];
            qrTypeInputsRef.value.targetLocationData.longitude = geoMatch[2];
            qrTypeInputsRef.value.targetLocationData.address = '선택한 위치';
          }

          // 이전 형식을 새 형식으로 변환
          formData.value.targetContent = `https://maps.google.com/?q=${geoMatch[1]},${geoMatch[2]}`;
        } else {
          console.warn('타겟 위치 정보 파싱 실패:', formData.value.targetContent);
          // 파싱 실패 시 기본값 설정 (수정 모드에서 오류 방지)
          if (isEditMode.value) {
            // 타겟 콘텐츠가 있지만 파싱 실패한 경우, 임시 위치 정보 설정
            const defaultLat = '37.5665';
            const defaultLng = '126.9780';
            if (qrTypeInputsRef.value) {
              qrTypeInputsRef.value.targetLocationData.latitude = defaultLat;
              qrTypeInputsRef.value.targetLocationData.longitude = defaultLng;
              qrTypeInputsRef.value.targetLocationData.address = '서울시청 (기본값)';
            }

            // 타겟 콘텐츠 재설정 (Google Maps URL 형식)
            formData.value.targetContent = `https://maps.google.com/?q=${defaultLat},${defaultLng}`;
          }
        }
      }
      // QR 코드 타입이 'WIFI'일 때 Wi-Fi 정보 파싱
      else if (formData.value.qrType === 'WIFI' && formData.value.targetContent) {
        // Wi-Fi QR 코드 형식 파싱 시도 (WIFI:S:<SSID>;T:<WPA|WEP|>;P:<PASSWORD>;H:<true|false>;;)
        try {
          const content = formData.value.targetContent;

          if (qrTypeInputsRef.value) {
            // SSID 추출
            const ssidMatch = content.match(/WIFI:S:([^;]*);/);
            if (ssidMatch && ssidMatch.length >= 2) {
              // 이스케이프된 문자 처리
              qrTypeInputsRef.value.wifiData.ssid = ssidMatch[1].replace(/\\([;:,"\\])/g, '$1');
            }

            // 보안 유형 추출
            const typeMatch = content.match(/T:([^;]*);/);
            if (typeMatch && typeMatch.length >= 2) {
              qrTypeInputsRef.value.wifiData.securityType = typeMatch[1];
            } else {
              qrTypeInputsRef.value.wifiData.securityType = ''; // 보안 없음
            }

            // 비밀번호 추출
            const passwordMatch = content.match(/P:([^;]*);/);
            if (passwordMatch && passwordMatch.length >= 2) {
              // 이스케이프된 문자 처리
              qrTypeInputsRef.value.wifiData.password = passwordMatch[1].replace(/\\([;:,"\\])/g, '$1');
            } else {
              qrTypeInputsRef.value.wifiData.password = '';
            }

            // 숨겨진 네트워크 여부 추출
            const hiddenMatch = content.match(/H:([^;]*);/);
            if (hiddenMatch && hiddenMatch.length >= 2) {
              qrTypeInputsRef.value.wifiData.hidden = hiddenMatch[1].toLowerCase() === 'true';
            } else {
              qrTypeInputsRef.value.wifiData.hidden = false;
            }
          }

        } catch (err) {
          console.warn('Wi-Fi 정보 파싱 실패:', err, formData.value.targetContent);
          // 파싱 실패 시 기본값 설정 (수정 모드에서 오류 방지)
          if (isEditMode.value) {
            wifiData.value = {
              ssid: 'Wi-Fi 네트워크',
              securityType: 'WPA',
              password: '',
              hidden: false
            };

            // 타겟 콘텐츠 재설정
            formData.value.targetContent = generateWifiQrContent();
          }
        }
      }
      // 랜딩 페이지 타입인 경우 처리
      else if (formData.value.qrType === 'LANDING_PAGE') {
        // 타겟 콘텐츠에서 랜딩 페이지 ID 추출
        // URL 형식이 서버주소/landing/ID 형태이뮼로 마지막 부분만 추출
        const match = formData.value.targetContent.match(/\/landing\/(\d+)/);
        if (match && match[1]) {
          if (qrTypeInputsRef.value) {
            qrTypeInputsRef.value.selectedLandingPageId = match[1];
            // 랜딩 페이지 목록 가져오기
            await qrTypeInputsRef.value.fetchLandingPages();
          }

          // 수정 모드에서는 타겟 콘텐츠를 서버 주소가 포함된 형태로 유지
          // 이미 저장된 타겟 콘텐츠를 그대로 사용
          initialTargetContent.value = formData.value.targetContent;
        } else {
          // URL 형식이 아닌 경우 프론트엔드 서버 주소를 포함한 전체 URL 형식으로 설정 (예: http://localhost:9999/landing/{landingPageId})
          const frontendDomain = getFrontendDomain();
          formData.value.targetContent = `${frontendDomain}/landing/${formData.value.targetContent}`;
          initialTargetContent.value = formData.value.targetContent;

          // 타겟 콘텐츠에서 ID만 추출 시도
          const idMatch = formData.value.targetContent.match(/(\d+)$/);
          if (idMatch && idMatch[1] && qrTypeInputsRef.value) {
            qrTypeInputsRef.value.selectedLandingPageId = idMatch[1];
            await qrTypeInputsRef.value.fetchLandingPages();
          }
        }
      }

      // 이벤트 타입일 경우 이벤트 선택 초기화 및 목록 로드
      if (formData.value.qrType === 'EVENT_ATTENDANCE') {
        // 서버 응답에서 연결된 이벤트 ID 추출 (다양한 필드명 처리)
        let eventId = '';

        // 가능한 모든 필드명 확인
        if (qrData.linkedEventId) {
          eventId = qrData.linkedEventId;
        } else if (qrData.linked_event_id) {
          eventId = qrData.linked_event_id;
        } 

        // 타겟 콘텐츠에서 이벤트 ID 추출 시도 (URL 형식인 경우)
        if ((!eventId || eventId === '') && qrData.targetContent) {
          const match = qrData.targetContent.match(/\/event\/(\d+)/);
          if (match && match[1]) {
            eventId = match[1];
          }
        }

        // 이벤트 목록 먼저 가져오기
        if (qrTypeInputsRef.value) {
          await qrTypeInputsRef.value.fetchEvents();
        }

        // 이벤트 목록을 가져온 후 이벤트 ID 설정
        if (eventId && eventId !== '') {
          // 문자열로 변환하여 비교 (숫자형과 문자열형 ID 모두 처리)
          const eventIdStr = String(eventId);

          // 이벤트 목록에 해당 ID가 있는지 확인
          const eventExists = events.value.some(event => {
            const eventIdFromList = String(event.eventId);
            const matches = eventIdFromList === eventIdStr;
            return matches;
          });

          if (eventExists) {
            if (qrTypeInputsRef.value) {
              qrTypeInputsRef.value.selectedEventId = eventIdStr;
            }
          } else {
            console.warn('이벤트 목록에 일치하는 ID가 없음:', eventIdStr);
            // 이벤트 목록에 없는 경우 빈 값으로 설정
            if (qrTypeInputsRef.value) {
              qrTypeInputsRef.value.selectedEventId = '';
            }
          }
        } else {
          console.warn('이벤트 ID가 없음');
          // 이벤트 ID가 없는 경우 빈 값으로 설정
          if (qrTypeInputsRef.value) {
            qrTypeInputsRef.value.selectedEventId = '';
          }
        }
      }

      // 데이터 로드 후 즉시 QR 코드 생성 시도
      await nextTick();
      await generateClientSideQrCode(); // await 추가하여 비동기 완료 기다림

      // isQrCodeGenerated는 generateClientSideQrCode 내부에서 설정됨

    } else {
      console.error('API 응답 데이터가 유효하지 않음:', response);
      error.value = '서버로부터 유효한 QR 코드 데이터를 받지 못했습니다.';
      isLoading.value = false; // 로딩 완료 처리
    }

  } catch (err) {
    console.error('QR 코드 데이터 로딩 또는 처리 중 오류:', err);
    error.value = `QR 코드 정보를 불러오는 데 실패했습니다: ${err.message}. 다시 시도해주세요.`;
    if (err.response?.status === 404) {
      error.value = '존재하지 않는 QR 코드입니다.';
    }
    isLoading.value = false; // 로딩 완료 처리
  } finally {
     // fetchQrCodeData 함수 내 로딩 상태 관리는 이 블록에서 하지 않음
     // generateClientSideQrCode 호출 후 로딩 상태 변경
      // 단, 에러 발생 시 isLoading을 false로 설정해야 함 (위 catch 블록에서 처리)
      // 성공적으로 generateClientSideQrCode가 끝나면 로딩 완료
      if (!error.value) {
          isLoading.value = false;
      }
  }
};

onMounted(() => {
  if (isEditMode.value) {
    fetchQrCodeData();
  } else {
    // 생성 모드
    isLoading.value = false;
    nextTick(() => {
      // 생성 모드에서는 처음에 빈 미리보기 또는 플레이스홀더 표시
      // 사용자가 타겟 콘텐츠를 입력하면 watch 콜백에서 생성됨
      // generateClientSideQrCode(); // 초기에는 호출 안 함
    });
  }
});

// 배치 생성 래퍼 함수
const handleBatchCreation = async () => {
  // 현재 UI에 설정된 디자인 옵션들을 가져와 생성
  const currentDesignOptions = {
    dotsOptions: {
      color: qrColor.value || '#000000',
      type: 'square',
      cornersSquareOptions: {
        color: qrEyeColor.value || '#000000',
        type: qrEyeStyle.value === 0 ? 'square' : 'dot'
      },
      cornersDotOptions: {
        color: qrEyeColor.value || '#000000',
        type: qrEyeStyle.value === 0 ? 'square' : 'dot'
      }
    },
    backgroundOptions: {
      color: qrBgColor.value || '#FFFFFF'
    },
    ...(logoPreview.value && { logoRatio: logoSize.value ? (logoSize.value / 100) : 0.4 }),
    errorCorrectionLevel: qrErrorCorrectionLevel.value,
    layoutOptions: null
  };

  await startBatchCreation(
    excelFile,
    parseExcelFile,
    formData,
    locationData,
    locationImageFile,
    { value: currentDesignOptions }
  );
};


// 폼 제출 처리
const handleSubmit = async () => {
  isSubmitting.value = true;
  error.value = '';

  // 엑셀 파일이 업로드된 경우 연속 생성 실행
  if (excelFile.value && !isEditMode.value) {
    // QR코드 이름만 필수로 검사
    if (!formData.value.qrName) {
      error.value = 'QR 코드 이름은 필수입니다.';
      isSubmitting.value = false;
      return;
    }
    // 스크롤 함수 바로 호출 (배치 생성 완료를 기다리지 않음)
    scrollToBottom();
    
    // 배치 생성을 비동기로 시작
    handleBatchCreation().finally(() => {
      isSubmitting.value = false;
    });

    return;
  }

  // 엑셀 파일이 업로드되지 않은 경우 단일 생성 처리 진행 (기존 부분)
  // 유효성 검사 (예: 필수 필드)
  if (!formData.value.qrName || !formData.value.qrType || !formData.value.status) {
      error.value = '필수 필드를 모두 입력해주세요.';
      isSubmitting.value = false;
      return;
  }

  // 랜딩 페이지 타입일 때 유효성 검사
  if (formData.value.qrType === 'LANDING_PAGE') {
    if (!selectedLandingPageId.value) {
      error.value = '랜딩 페이지를 선택해주세요.';
      isSubmitting.value = false;
      return;
    }
  } else if (formData.value.qrType === 'EVENT_ATTENDANCE') {
    if (!selectedEventId.value) {
      error.value = '이벤트를 선택해주세요.';
      isSubmitting.value = false;
      return;
    }
    // 이벤트 타입 targetContent URL 설정
    formData.value.targetContent = `${getFrontendDomain()}/event/${selectedEventId.value}`;

    // 이벤트 ID 설정 (formData 객체에는 linkedEventId만 설정)
    formData.value.linkedEventId = selectedEventId.value;

  } else if (formData.value.qrType === 'LOCATION') {
    // 위치 타입일 때 유효성 검사
    if (!formData.value.targetContent) {
      // 위치 정보가 없는 경우, 타겟 위치 정보로 타겟 콘텐츠 설정
      const locationData = qrTypeInputsRef.value?.targetLocationData;
      if (locationData?.latitude && locationData?.longitude) {
        formData.value.targetContent = `https://maps.google.com/?q=${locationData.latitude},${locationData.longitude}`;
      } else {
        // 수정 모드에서는 기본값 설정
        if (isEditMode.value) {
          const defaultLat = '33.499686';
          const defaultLng = '126.531328';
          if (qrTypeInputsRef.value) {
            qrTypeInputsRef.value.targetLocationData.latitude = defaultLat;
            qrTypeInputsRef.value.targetLocationData.longitude = defaultLng;
            qrTypeInputsRef.value.targetLocationData.address = '제주시청 (기본값)';
          }

          formData.value.targetContent = `https://maps.google.com/?q=${defaultLat},${defaultLng}`;
        } else {
          error.value = '위치 정보를 선택해주세요.';
          isSubmitting.value = false;
          return;
        }
      }
    }

    // 위치 정보 유효성 검사
    if (!formData.value.targetContent) {
      error.value = '위치 정보를 입력해주세요.';
      isSubmitting.value = false;
      return;
    }
  } else if (formData.value.qrType === 'WIFI') {
    // Wi-Fi 타입일 때 유효성 검사
    if (!formData.value.targetContent) {
      error.value = 'Wi-Fi 정보를 입력해주세요.';
      isSubmitting.value = false;
      return;
    }

    // Wi-Fi 형식 확인
    if (!formData.value.targetContent.startsWith('WIFI:')) {
      error.value = 'Wi-Fi 정보 형식이 올바르지 않습니다.';
      isSubmitting.value = false;
      return;
    }
  } else if (!formData.value.targetContent) {
    // 랜딩 페이지, 이벤트, 위치가 아닌 다른 타입의 경우 타겟 콘텐츠 필수
    error.value = '타겟 콘텐츠를 입력해주세요.';
    isSubmitting.value = false;
    return;
  }



   // 날짜 유효성 검사 (선택적) - 종료일이 시작일보다 빠른 경우 등
   if (formData.value.validFromDate && formData.value.validToDate) {
       const fromDate = new Date(formData.value.validFromDate);
       const toDate = new Date(formData.value.validToDate);
       if (toDate < fromDate) {
           error.value = '유효 종료일은 시작일보다 빠를 수 없습니다.';
           isSubmitting.value = false;
           return;
       }
   }

  // A4 캔버스 고화질 이미지 생성 (A4 박스 사용 시에만)
  let a4CanvasImageBase64 = null;
  if (useA4Canvas.value) {
    try {
      const a4CanvasImage = await generateA4CanvasImage();
      if (a4CanvasImage) {
        // File 객체를 Base64로 변환
        a4CanvasImageBase64 = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target.result);
          reader.readAsDataURL(a4CanvasImage);
        });
      }
    } catch (canvasError) {
      console.warn('A4 캔버스 이미지 생성 실패:', canvasError);
      // 이미지 생성 실패해도 QR 코드 생성은 계속 진행
    }
  }

  // 현재 디자인 옵션을 JSON 문자열로 조합 (서버 요구 형식)
  const currentDesignOptions = JSON.stringify({
    dotsOptions: {
      color: qrColor.value, // 사용자가 선택한 QR 코드 색상 사용
      type: 'square',
      cornersSquareOptions: {
        color: qrEyeColor.value, // 사용자가 선택한 눈 색상 사용
        type: qrEyeStyle.value === 0 ? 'square' : 'dot' // 사용자가 선택한 눈 모양 사용
      },
      cornersDotOptions: {
        color: qrEyeColor.value, // 사용자가 선택한 눈 색상 사용
        type: qrEyeStyle.value === 0 ? 'square' : 'dot' // 사용자가 선택한 눈 모양 사용
      }
    },
    backgroundOptions: {
      color: qrBgColor.value // 사용자가 선택한 배경 색상 사용
    },
    // 로고 비율은 로고가 있을 때만 포함
    ...(logoPreview.value && { logoRatio: logoSize.value / 100 }),
    // A4 캔버스 및 배경 이미지 사용 여부 추가
    useA4Canvas: useA4Canvas.value,
    useBackgroundImage: useBackgroundImage.value,
    // A4 사이즈 정보 추가 (HTML A4 캔버스의 실제 픽셀 크기)
    a4Size: useA4Canvas.value ? (() => {
      const containerRect = a4CanvasContainer.value?.getBoundingClientRect();
      return {
        width: containerRect?.width || 0,
        height: containerRect?.height || 0
      };
    })() : null,
    // QR 코드 위치 및 크기 정보 (A4 캔버스 사용 시 mm 단위, 미사용 시 픽셀 단위)
    qrLayout: useA4Canvas.value ? {
      positionX: mmPositionX.value,
      positionY: mmPositionY.value,
      width: mmWidth.value,
      height: mmHeight.value,
      unit: 'mm'
    } : {
      positionX: qrPositionX.value,
      positionY: qrPositionY.value,
      width: qrWidth.value,
      height: qrHeight.value,
      unit: 'px'
    },
    // 배경 이미지 맞춤 모드
    backgroundFitMode: useBackgroundImage.value ? backgroundFitMode.value : null,
    // A4 캔버스 이미지 (Base64 형태로 포함)
    ...(a4CanvasImageBase64 && { a4CanvasImage: a4CanvasImageBase64 }),
    errorCorrectionLevel: qrErrorCorrectionLevel.value, // 사용자가 선택한 오류 복원 수준 사용
    layoutOptions: null
  });

  // 현재 프로젝트 ID 확인
  if (!currentProject.value || !currentProject.value.projectId) {
    error.value = '프로젝트 ID를 찾을 수 없습니다. 프로젝트가 선택되어 있는지 확인해주세요.';
    isSubmitting.value = false;
    return;
  }

  // FormData 생성 (파일 업로드 고려)
  const submissionData = new FormData();

  // 폼 데이터 추가
  Object.keys(formData.value).forEach(key => {
    if (key === 'validFromDate' || key === 'validToDate') {
      // 날짜 형식 변환 후 추가
      const serverDate = formatDateForServer(formData.value[key]);
      if (serverDate) { // null이 아닐 경우에만 추가
         submissionData.append(key, serverDate);
      }
    } else if (key === 'qrType' && formData.value[key] === 'EVENT_ATTENDANCE') {
      // QR 코드 타입이 '이벤트'일 경우 'EVENT'로 변경하여 전송
      submissionData.append(key, 'EVENT');
    } else if (formData.value[key] !== null && formData.value[key] !== undefined) {
      // null 이나 undefined 아닌 값만 추가
      submissionData.append(key, formData.value[key]);
    }
  });

  // 프로젝트 ID 추가
  submissionData.append('projectId', currentProject.value.projectId);

  // 기존 FormData 객체를 유지하면서 위치 정보 필드 제거 및 추가

  // 위치 정보 관련 필드 목록
  const locationFields = [
    'installationLocationLat', 'installationLocationLng', 'installationLocation',
    'installation_location_lat', 'installation_location_lng', 'installation_location',
    'latitude', 'longitude', 'locationAddress'
  ];

  // 위치 정보 필드 제거를 위해 현재 FormData의 모든 키 수집
  const allKeys = [];
  for (let pair of submissionData.entries()) {
    allKeys.push(pair[0]);
  }

  // 위치 정보 관련 필드 제거
  for (let key of allKeys) {
    if (locationFields.includes(key)) {
      // FormData에서는 delete 메서드가 없으므로 같은 키로 빈 값을 설정하여 "제거"
      // 실제로는 서버에서 빈 문자열로 처리됨
      submissionData.set(key, '');
    }
  }

  // 위치 정보 추가 (있는 경우)
  if (locationData.value.latitude && locationData.value.longitude) {
    // 단일 필드명으로만 위치 정보 추가
    submissionData.set('installationLocationLat', locationData.value.latitude);
    submissionData.set('installationLocationLng', locationData.value.longitude);
    submissionData.set('installationLocation', locationData.value.address || '');
    
  }
  
  // 위치 정보와 상관없이 이미지 파일 처리
  if (locationImageFile.value) {
    submissionData.set('qrInstalledImageFile', locationImageFile.value);
  } 

  // 디자인 옵션 JSON 문자열 추가 (수정 모드에서도 포함)
  // set 메서드를 사용하여 중복 방지
  submissionData.set('designOptions', currentDesignOptions);

  // 로고 파일 처리 (생성 모드와 수정 모드 모두)
  if (logoFile.value) {
    submissionData.set('logoImageFile', logoFile.value);
  }
  
  // 배경 이미지 파일 처리 (배경 이미지 토글이 활성화된 경우에만)
  if (useBackgroundImage.value && backgroundImageFile.value) {
    submissionData.set('backgroundImageFile', backgroundImageFile.value);
    // 배경 이미지 맞춤 모드 정보 추가
    submissionData.set('backgroundFitMode', backgroundFitMode.value);
  }

  // A4 캔버스 이미지는 이제 designOptions 안에 포함됨 

  // 이벤트 타입일 때 이벤트 ID 추가
  if (formData.value.qrType === 'EVENT_ATTENDANCE') {
    // 이벤트 ID 관련 필드 제거 (중복 방지)
    const eventIdFields = ['linkedEventId', 'linked_event_id', 'eventId', 'event_id'];

    // 이벤트 ID 관련 필드 제거
    for (let key of eventIdFields) {
      // 기존 값을 빈 문자열로 설정하여 "제거"
      submissionData.set(key, '');
    }

    // 서버에서 사용하는 필드명으로 이벤트 ID 추가 (단일 필드명으로만 추가)
    submissionData.set('linkedEventId', selectedEventId.value);

    // formData 객체에도 linkedEventId 설정
    formData.value.linkedEventId = selectedEventId.value;

  }

  try {
    let response;
    if (isEditMode.value) {
      // 업데이트 API 호출
      response = await updateQrCode(route.params.qrCodeId, submissionData);
      // 성공 알림 또는 목록 페이지로 리디렉션 등
      alert('QR 코드가 성공적으로 수정되었습니다.'); // 임시 알림
    } else {
      // 생성 API 호출
      response = await createQrCode(submissionData);
      // 성공 알림 또는 목록 페이지로 리디렉션 등
       alert('QR 코드가 성공적으로 생성되었습니다.'); // 임시 알림
    }
    router.push({ name: 'qr-list' }); // 성공 시 목록 페이지로 이동
  } catch (err) {
    console.error(`QR 코드 ${isEditMode.value ? '수정' : '생성'} 실패:`, err);
    // 서버 응답에서 오류 메시지 추출 시도
    const errorMessage = err.response?.data?.message || err.message || '알 수 없는 오류가 발생했습니다.';
    error.value = `QR 코드 처리 중 오류 발생: ${errorMessage}`;
  } finally {
    isSubmitting.value = false;
  }
};

</script>

<style scoped>
.qr-code-form {
  padding: 20px;
  max-width: 900px; /* 폼 너비 증가 */
  margin: 0 auto; /* 가운데 정렬 */
}

/* 위치 입력 폼 스타일 */
.location-inputs, .location-target-inputs {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.location-input-group {
  flex: 1;
}

.location-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.location-address {
  margin-bottom: 15px;
}

.drv {
  border: 1px dashed #ccc;
}

.inactive-draggable.drv {
  border-color: transparent;
}

.location-address label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.map-select-button {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 10px;
  min-height: 52px;
}

.map-select-button:hover {
  background-color: #3367D6;
}

.map-select-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* QR 코드 설치 위치와 타겟 위치 구분 스타일 */
.location-target-inputs {
  background-color: #f0f7ff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #d0e3ff;
}

.location-target-button {
  background-color: #34A853;
}

.location-target-button:hover {
  background-color: #2E8B57;
}

/* Wi-Fi 입력 폼 스타일 */
.wifi-inputs {
  background-color: #f0fff0;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #d0ffd0;
  margin-bottom: 15px;
}

.wifi-input-group {
  margin-bottom: 15px;
}

.wifi-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.wifi-input-group input,
.wifi-input-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* 폰트 크기 */
  box-sizing: border-box;
}

.password-input-container {
  position: relative;
  display: flex;
}

.password-input-container input {
  flex: 1;
  border-radius: 4px 0 0 4px;
}

.toggle-password-button {
  padding: 0 15px;
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 10px; /* 아이콘 크기 조정 */
  line-height: 18px; /* 아이콘 수직 정렬 */
  transition: background-color 0.2s;
}
.toggle-password-button:hover {
  background-color: #e0e0e0;
}

.wifi-checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.wifi-checkbox-group input[type="checkbox"] {
  margin-right: 10px;
}

.target-content-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.target-content-preview label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.preview-content {
  font-family: monospace;
  word-break: break-all;
}

.form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px; /* 패딩 증가 */
  margin-top: 20px;
  display: grid; /* Grid 레이아웃 사용 */
  gap: 20px 30px;
}
/* 모바일 화면 등 작은 화면에서는 1열로 변경 */
@media (max-width: 768px) {
  .form-container {
    grid-template-columns: 1fr;
  }
}

/* 폼 그룹 스타일 */
.form-group {
  margin-bottom: 0; /* Grid gap으로 간격 조절하므로 제거 */
  /* 특정 그룹이 2열을 차지하도록 설정 */
}
.form-group:nth-child(3), /* 타겟 콘텐츠 */
.form-group:nth-child(7), /* 설명 */
.form-group:nth-child(8), /* 디자인 옵션 */
.qr-code-preview, /* QR 코드 미리보기 */
.form-actions, /* 액션 버튼 */
.error-message /* 오류 메시지 */
{
  grid-column: 1 / -1; /* 1열부터 마지막 열까지 차지 */
}


.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.required {
  color: #f44336; /* Red color for required fields */
  margin-left: 4px;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea,
.form-group input[type="datetime-local"] {
  width: 100%;
  padding: 12px; /* 입력 필드 패딩 증가 */
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* 폰트 크기 */
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical; /* Allow vertical resizing */
}

/* Date group styling */
.date-group {
  width: 100%;
}

/* Form actions alignment */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px; /* 위쪽 간격 조정 */
  padding-top: 20px;
  border-top: 1px solid #eee; /* 구분선 추가 */
}

.cancel-btn, .submit-btn {
  padding: 12px 24px; /* 버튼 크기 증가 */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px; /* 버튼 폰트 크기 */
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}
.cancel-btn:hover {
  background-color: #d0d0d0;
}

.submit-btn {
  background-color: #4CAF50;
  color: white;
}
.submit-btn:hover:not(:disabled) {
  background-color: #45a049;
}
.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Loading and Error Messages */
.loading, .error-message {
  padding: 15px; /* 패딩 조정 */
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
  grid-column: 1 / -1; /* 전체 너비 차지 */
}

.error-message {
  color: #f44336;
  background-color: #ffebee;
  border: 1px solid #f44336;
  font-weight: bold;
}

.loading-indicator {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 10px;
}

.field-note.error {
  color: #f44336;
  font-weight: bold;
}

.field-note {
  margin-top: 6px;
  font-size: 13px; /* 약간 작게 */
  color: #555; /* 기본 색상 */
  font-style: italic;
}
/* 오류 관련 노트는 다른 색상 */
.form-group .field-note:has(+ .input-error), /* URL 오류 등 */
.field-note.error { /* 명시적 에러 클래스 */
  color: #f44336;
}
.form-group:has(.input-error) .field-note { /* 입력 필드 오류 시 노트 색상 */
   color: #f44336;
}

.input-error {
  border-color: #f44336 !important;
  background-color: #fff0f0; /* 약간의 배경색 */
}

/* 이벤트 선택 관련 스타일 */

.selected-event-info {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #e8f5e9;
  border-left: 3px solid #4CAF50;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.selected-event-label {
  font-weight: bold;
  margin-right: 8px;
  color: #2e7d32;
}

.selected-event-value {
  color: #1b5e20;
  font-weight: 500;
}


/* QR Code Preview Styles */
.qr-code-preview {
  background-color: #ffffff; /* 흰색 배경 */
  border: 1px solid #e0e0e0; /* 경계선 추가 */
  border-radius: 8px;
  padding: 25px; /* 패딩 증가 */
  margin-top: 10px; /* 위쪽 간격 조정 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-preview h3 {
  margin-top: 0;
  color: #333;
  margin-bottom: 20px; /* 제목 아래 간격 */
}

.qr-preview-container {
  width: 300px;
  height: 300px;
  margin: 0 auto 20px;
  position: relative;
  border: 1px dashed #ccc; /* 테두리가 확대 시 잘릴 수 있으므로, 필요 없다면 제거하거나 조정 */
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 중요: overflow: hidden 으로 설정되어 있는지 확인 */
  overflow: hidden;
  padding: 0;
  background-color: #f8f8f8;
}

/* [수정] 생성된 SVG/Canvas 요소가 컨테이너를 꽉 채우도록 강제 */
.qr-preview-container > canvas,
.qr-preview-container > svg {
  display: block; /* 인라인 공백 제거 */
  width: 100% !important; /* 컨테이너 너비에 맞춤 */
  height: 100% !important; /* 컨테이너 높이에 맞춤 */
  margin: 0 !important; /* 라이브러리 기본 마진 무시 */
  padding: 0 !important; /* 라이브러리 기본 패딩 무시 */
  object-fit: contain; /* 비율 유지하며 채우기 */
  transform: scale(1.04); /* 예시 값, 1.0 ~ 1.1 사이에서 조정해보세요 */
  transform-origin: center center; /* 확대 기준점을 중앙으로 설정 */
}

.image-placeholder {
  width: 100%; /* 부모 너비 채우기 */
  display: flex;
  justify-content: center; /* 가운데 정렬 */
  align-items: center;
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  box-sizing: border-box;
}

/* QR Code Scan Reliability Styles */
.scan-reliability-container {
  margin-top: 20px; /* 위쪽 간격 */
  padding: 15px; /* 내부 패딩 */
  border-radius: 6px; /* 둥근 모서리 */
  background-color: #f0f0f0; /* 배경색 */
  width: 100%; /* 너비 100% */
  max-width: 350px; /* 최대 너비 제한 */
  box-sizing: border-box;
}

.scan-reliability-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px; /* 아래 간격 */
  font-weight: bold;
  font-size: 14px; /* 폰트 크기 */
}
/* 신뢰도별 색상 클래스 */
.reliability-high { color: #4CAF50; }
.reliability-medium { color: #FFC107; }
.reliability-low { color: #F44336; }
.reliability-critical { color: #9C27B0; font-weight: bold; } /* 강조 */

.scan-reliability-progress-container {
  width: 100%;
  height: 12px; /* 높이 증가 */
  background-color: #e0e0e0;
  border-radius: 6px; /* 둥근 모서리 */
  overflow: hidden;
  margin-bottom: 8px; /* 아래 간격 */
}

.scan-reliability-progress {
  height: 100%;
  border-radius: 6px; /* 둥근 모서리 */
  transition: width 0.5s ease, background-color 0.5s ease; /* 부드러운 전환 */
}

.scan-reliability-note {
  font-size: 13px; /* 폰트 크기 */
  color: #666;
  font-style: italic;
  text-align: center; /* 가운데 정렬 */
}


/* Design Options/* 디자인 옵션 스타일 */
.design-options-container {
  background-color: #f0f0f5; /* 약간 다른 배경색 */
  border-radius: 6px;
  padding: 20px;
  margin-top: 10px; /* 위쪽 간격 */
}

.design-group {
  margin-bottom: 15px;
}

/* 토글 스위치 스타일 */
.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 1px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #4285F4;
}

.toggle-switch input:checked + label:before {
  transform: translateX(26px);
}

/* 배경 이미지 업로드 스타일 */
.background-image-container {
  margin-top: 10px;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 4px;
}

/* A4 캔버스 섹션 스타일 */
.a4-canvas-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
  overflow: hidden; /* 자식 요소가 벗어나지 않도록 */
}

.a4-canvas-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

/* A4 캔버스 스타일 */
.a4-canvas {
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  margin-bottom: 15px;
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
}

/* A4 캔버스 부모 컨테이너 */
.a4-canvas-parent-container {
  position: relative;
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  min-height: 600px; /* 최소 높이 설정 */
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden; /* 자식 요소가 벗어나지 않도록 */
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
  margin: 0; /* 마진 제거 */
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.canvas-toolbar span {
  font-size: 14px;
  margin-right: 10px;
  font-weight: 500;
}

.image-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  padding: 5px 10px;
  margin: 0 5px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-btn:hover {
  background-color: #f0f0f0;
}

.control-btn.active {
  background-color: #4285F4;
  color: white;
  border-color: #4285F4;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.background-image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.background-preview {
  /* 기본 스타일 */
  max-width: 100%;
  max-height: 100%;
  border: none;
  transition: all 0.3s ease;
}

/* 배경 이미지 맞춤 모드 */
.bg-fill {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bg-fit {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.bg-original {
  max-width: none;
  max-height: none;
  width: auto;
  height: auto;
}

.remove-bg-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  z-index: 10;
}

.background-upload {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #ccc;
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
}

.background-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 20px;
}

.background-file-input {
  display: none;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
  .design-options-container {
    grid-template-columns: 1fr;
  }
}

.logo-upload-section, .advanced-options {
  background-color: #fff; /* 각 섹션 배경 흰색 */
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.logo-upload-section h4, .advanced-options h4 {
  margin-top: 0;
  margin-bottom: 15px; /* 제목 아래 간격 */
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.logo-preview-container {
  margin: 15px 0;
  display: flex;
  justify-content: center; /* 가운데 정렬 */
}

.logo-preview {
  position: relative;
  width: 100px; /* 로고 미리보기 크기 조정 */
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  max-width: 90%; /* 내부 여백 확보 */
  max-height: 90%;
  object-fit: contain;
}

.remove-logo-btn {
  position: absolute;
  top: 3px; /* 위치 조정 */
  right: 3px;
  background-color: rgba(211, 47, 47, 0.8); /* 약간 투명한 빨간색 */
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px; /* 버튼 크기 조정 */
  height: 20px;
  font-size: 10px; /* 아이콘 크기 조정 */
  line-height: 18px; /* 아이콘 수직 정렬 */
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s;
}
.remove-logo-btn:hover {
  background-color: rgba(198, 40, 40, 1); /* 호버 시 진하게 */
}

.logo-upload {
  width: 100px; /* 로고 업로드 영역 크기 조정 */
  height: 100px;
  border: 2px dashed #ccc;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.2s;
}
.logo-upload:hover {
  border-color: #aaa;
}

.logo-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  color: #666;
  font-size: 13px; /* 텍스트 크기 */
}

.upload-icon {
  font-size: 20px; /* 아이콘 크기 */
  margin-bottom: 5px;
}

.logo-file-input {
  display: none; /* 숨김 처리 */
}

.logo-options {
  margin-top: 15px; /* 위쪽 간격 */
}

.logo-size-option {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.logo-size-option label {
  margin-bottom: 8px; /* 레이블 아래 간격 */
  font-size: 14px;
  color: #555;
}

.logo-size-option input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
  cursor: pointer;
}
.logo-size-option span {
   font-size: 13px;
   color: #333;
   text-align: right;
}
.logo-size-option .field-note {
  font-size: 0.8em;
  color: #666;
  margin-top: 4px;
}


.design-group {
  display: grid; /* 내부 요소 정렬 위해 grid */
  grid-template-columns: auto 1fr; /* 레이블 자동, 입력필드 나머지 */
  align-items: center;
  gap: 10px; /* 요소 간 간격 */
  margin-bottom: 15px;
}
/* 오류 복원 수준 그룹은 레이블과 셀렉트만 */
.design-group:has(.error-correction-select) {
  grid-template-columns: auto 1fr;
}
/* 슬라이더 그룹은 레이블, 슬라이더, 값 표시 */
.design-group.slider-group {
    grid-template-columns: auto 1fr auto; /* 레이블, 슬라이더, 값 */
}


.design-group label {
  /* min-width 제거 */
  margin-right: 0; /* grid gap 사용 */
  font-weight: normal;
  color: #555;
  font-size: 14px; /* 폰트 크기 */
  text-align: left; /* 왼쪽 정렬 */
  white-space: nowrap; /* 줄바꿈 방지 */
}

.design-group input[type="color"] {
  width: 40px; /* 색상 피커 크기 조정 */
  height: 30px;
  padding: 0;
  border: 1px solid #ccc;
  cursor: pointer;
  justify-self: start; /* 왼쪽 정렬 */
}

.slider-group input[type="range"] {
  width: 100%; /* 슬라이더 너비 꽉 채움 */
  cursor: pointer;
}

.slider-group span {
  /* width 제거 */
  text-align: right;
  font-size: 14px;
  color: #555;
  white-space: nowrap;
}

.reset-btn {
  margin-top: 15px;
  padding: 8px 16px;
  font-size: 14px;
  background-color: #6c757d; /* 회색 계열 */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  justify-self: start; /* 버튼 왼쪽 정렬 */
}
.reset-btn:hover {
   background-color: #5a6268;
}

.error-correction-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  grid-column: 2 / 3; /* 입력 필드 위치 */
}

/* QR 코드 버전 정보 스타일 */
.qr-version-info {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: #e7f3fe; /* 하늘색 배경 */
  margin-bottom: 15px; /* 아래 간격 */
  width: 100%;
  max-width: 350px;
  box-sizing: border-box;
}

.version-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
}

.version-value {
  color: #0056b3; /* 진한 파란색 */
  font-size: 16px;
}

.size-info {
  color: #555;
  font-size: 13px;
  font-weight: normal;
  margin-left: auto; /* 오른쪽으로 밀기 */
}

.version-note {
  font-size: 13px;
  color: #555;
  font-style: italic;
  text-align: center;
}

/* 반응형 레이아웃 조정 */
@media (max-width: 600px) {
  .form-container {
    grid-template-columns: 1fr; /* 작은 화면에서는 1열로 */
    padding: 20px;
  }
  /* 모든 그룹이 1열을 차지하도록 */
  .form-group,
  .qr-code-preview,
  .form-actions,
  .error-message,
  .design-options-container {
      grid-column: 1 / -1;
  }
  .design-options-container {
     grid-template-columns: 1fr; /* 디자인 옵션 내부도 1열 */
  }
   .date-group {
      width: 100%;
      margin-right: 0;
      margin-bottom: 20px; /* 날짜 그룹 간 간격 */
   }
   .date-group:last-of-type {
       margin-bottom: 0;
   }
   .form-actions {
      justify-content: center; /* 버튼 가운데 정렬 */
   }
}

/* 수정 모드 관련 스타일 */
.disabled-container {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.edit-mode-notice {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  color: #0d6efd;
  text-align: center;
}
/* 위치 사진 업로드 관련 스타일 */
.location-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.location-container {
  margin-top: 10px;
}

.location-inputs {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.location-map-button {
  margin-bottom: 10px;
}

.map-select-button {
  padding: 8px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.map-select-button:hover:not(:disabled) {
  background-color: #45a049;
}

.map-select-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.selected-location {
  margin-top: 8px;
  padding: 8px;
  background-color: #e9f7ef;
  border-radius: 4px;
  font-size: 14px;
}

.location-image-upload {
  margin-top: 15px;
}

.location-image-preview-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  overflow: hidden;
}

.location-image-preview {
  position: relative;
  max-width: 100%;
  max-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.location-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.location-image-upload-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.location-image-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #555;
}

.location-image-file-input {
  display: none;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

/* 배경 이미지 컨트롤 스타일 */
.background-image-controls {
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.bg-image-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #e8f5e9;
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
}

.bg-image-name {
  font-weight: 500;
  color: #2e7d32;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.remove-bg-btn-outside {
  background-color: #f8d7da;
  border: 1px solid #f5c2c7;
  color: #842029;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.remove-bg-btn-outside:hover {
  background-color: #f5c2c7;
}

.bg-upload-outside {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.background-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 25px;
  border: 2px dashed #6c757d;
  border-radius: 6px;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
}

.background-upload-btn:hover {
  background-color: #e9ecef;
  border-color: #495057;
}

/* A4 캔버스의 배경 이미지 및 QR 코드 이미지 스타일 */
/* 배경 이미지 컨테이너 래퍼 */
.background-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.background-container-wrapper > * {
  pointer-events: auto;
}

/* QR 코드 컨테이너 래퍼 */
.qr-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 50;
  pointer-events: none;
}

.qr-container-wrapper > * {
  pointer-events: auto;
}

/* 크기 조절 가능한 A4 캔버스 컨테이너 스타일 */
.a4-canvas-draggable-container {
  cursor: default;
  z-index: 5 !important;
  border-radius: 8px;
  padding: 0;
  overflow: visible;
  position: relative;
}

/* A4 캔버스 가이드라인 표시 */
.a4-canvas-draggable-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

/* A4 캔버스용 vue-draggable-resizable 핸들 스타일 */
.a4-canvas-draggable-container :deep(.vdr-handle) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: #FF9800 !important;
  border: 2px solid #fff !important;
  z-index: 999 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 상하 핸들 (tm, bm) - 가로로 긴 직사각형 */
.a4-canvas-draggable-container :deep(.vdr-handle-tm),
.a4-canvas-draggable-container :deep(.vdr-handle-bm) {
  width: 40px !important;
  height: 8px !important;
  border-radius: 4px !important;
  cursor: ns-resize !important;
}

/* 좌우 핸들 (ml, mr) - 세로로 긴 직사각형 */
.a4-canvas-draggable-container :deep(.vdr-handle-ml),
.a4-canvas-draggable-container :deep(.vdr-handle-mr) {
  width: 8px !important;
  height: 40px !important;
  border-radius: 4px !important;
  cursor: ew-resize !important;
}

.a4-canvas-draggable-container :deep(.vdr-handle:hover) {
  background-color: #F57C00 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3) !important;
}

/* 상하 핸들 호버 효과 */
.a4-canvas-draggable-container :deep(.vdr-handle-tm:hover),
.a4-canvas-draggable-container :deep(.vdr-handle-bm:hover) {
  transform: scaleY(1.2) scaleX(1.1) !important;
}

/* 좌우 핸들 호버 효과 */
.a4-canvas-draggable-container :deep(.vdr-handle-ml:hover),
.a4-canvas-draggable-container :deep(.vdr-handle-mr:hover) {
  transform: scaleX(1.2) scaleY(1.1) !important;
}

.a4-canvas-draggable-container :deep(.vdr-stick) {
  z-index: 999 !important;
}


/* 드래그 가능한 배경 이미지 컨테이너 스타일 */
.background-draggable-container {
  cursor: move;
  z-index: 20 !important;
  border: 2px dashed #4285F4;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
  background-color: rgba(66, 133, 244, 0.1);
}

.background-draggable-container:hover {
  border-color: #1a73e8;
  background-color: rgba(66, 133, 244, 0.2);
}

/* 드래그 가능한 QR 코드 컨테이너 스타일 */
.qr-draggable-container {
  cursor: move;
  z-index: 100 !important;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
  min-width: 50px !important;
  min-height: 50px !important;
}

/* 오버라이드가 필요한 일부 vue3-draggable-resizable 스타일 */
:deep(.vdr-handle) {
  background-color: #4285F4 !important;
  border: 1px solid white !important;
  width: 12px !important;
  height: 12px !important;
  z-index: 200 !important;
}

:deep(.vdr-handle:hover) {
  background-color: #1a73e8 !important;
}

:deep(.vdr-stick) {
  z-index: 200 !important;
}

/* QR 코드 컨테이너의 vue-draggable-resizable 강제 크기 설정 */
.qr-draggable-container:deep(.vdr-container) {
  min-width: 50px !important;
  min-height: 50px !important;
}

.qr-draggable-container:deep(.vdr-container.active) {
  min-width: 50px !important;
  min-height: 50px !important;
}

.qr-on-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 5px; */
  overflow: hidden;
  box-sizing: border-box;
}

.qr-image-on-canvas {
  /* max-width: 100%; */
  /* max-height: 100%; */
  width: 100%;
  height: 100%;
  /* object-fit: contain; */
  /* box-sizing: border-box; */
}

/* QR 코드 플레이스홀더 스타일 */
.qr-placeholder-on-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  box-sizing: border-box;
}

/* 엑셀 파일 업로드 및 배치 생성 관련 스타일 */
.excel-upload-container {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  grid-column: 1 / -1; /* 전체 너비 차지 */
}

.excel-upload-title {
  font-size: 16px;
  font-weight: bold;
  color: #2e7d32;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.excel-upload-icon {
  font-size: 20px;
}

.excel-upload-box {
  border: 2px dashed #81c784;
  border-radius: 4px;
  padding: 25px;
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
  background-color: rgba(129, 199, 132, 0.1);
}

.excel-upload-box:hover {
  background-color: rgba(129, 199, 132, 0.2);
  border-color: #4caf50;
}

.excel-input {
  display: none;
}

.excel-upload-info {
  margin-top: 15px;
  font-size: 14px;
  color: #555;
  text-align: center;
}

.excel-file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.delete-excel-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-excel-btn:hover {
  background-color: #ff5252;
}

.excel-filename-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #c8e6c9;
  border-radius: 4px;
  gap: 8px;
  width: 100%;
  max-width: 450px;
}

.excel-file-icon {
  color: #2e7d32;
  font-size: 18px;
}

.excel-filename {
  flex-grow: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-remove-btn {
  background-color: transparent;
  border: none;
  color: #e53935;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.excel-remove-btn:hover {
  color: #c62828;
}

/* QR 코드 배치 생성 결과 스타일 */
.qr-batch-results {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  grid-column: 1 / -1;
}

.qr-batch-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.batch-progress {
  margin-bottom: 20px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.progress-bar-container {
  height: 16px;
  background-color: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  border-radius: 8px;
  transition: width 0.3s ease;
}

.batch-error-message {
  margin-top: 15px;
  padding: 12px;
  background-color: #ffebee;
  border-left: 4px solid #f44336;
  color: #d32f2f;
  border-radius: 4px;
  font-weight: bold;
}

.batch-results-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #e8f5e9;
  border-radius: 4px;
}

.batch-item-list {
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.batch-list-header {
  display: grid;
  grid-template-columns: 50px 1fr 1fr 120px;
  background-color: #f5f5f5;
  padding: 10px;
  font-weight: bold;
  border-bottom: 1px solid #e0e0e0;
}

.batch-list-item {
  display: grid;
  grid-template-columns: 50px 1fr 1fr 120px;
  padding: 12px 10px;
  border-bottom: 1px solid #e0e0e0;
  align-items: center;
}

.batch-list-item:last-child {
  border-bottom: none;
}

.batch-item-number {
  text-align: center;
  font-weight: bold;
  color: #666;
}

.batch-item-name,
.batch-item-target {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 10px;
}

.batch-item-status {
  text-align: center;
  font-weight: bold;
}

.batch-status-success {
  color: #4caf50;
}

.batch-status-error {
  color: #f44336;
}


.batch-item-details {
  grid-column: 1 / -1;
  background-color: #f9f9f9;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.batch-error-details {
  color: #f44336;
  font-style: italic;
  margin-top: 5px;
}

.batch-toggle-details-btn {
  background-color: transparent;
  border: none;
  color: #2196f3;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  text-decoration: underline;
}

.batch-expand-all {
  margin-bottom: 10px;
  text-align: right;
}

@media (max-width: 768px) {
  .batch-list-header,
  .batch-list-item {
    grid-template-columns: 40px 1fr 100px;
  }
  
  .batch-item-target {
    display: none;
  }
}
</style>
