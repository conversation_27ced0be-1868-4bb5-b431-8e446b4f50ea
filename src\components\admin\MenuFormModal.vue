<template>
  <div v-if="show" class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ isEditMode ? '메뉴 수정' : '새 메뉴 추가' }}</h3>
        <button @click="$emit('close')" class="close-btn">×</button>
      </div>

      <form @submit.prevent="handleSubmit" class="menu-form">
        <div class="form-group">
          <label for="menuName">메뉴 이름 *</label>
          <input
            id="menuName"
            v-model="formData.menuName"
            type="text"
            required
            placeholder="메뉴 이름을 입력하세요"
          />
        </div>

        <div class="form-group">
          <label for="menuCode">메뉴 코드 *</label>
          <input
            id="menuCode"
            v-model="formData.menuCode"
            type="text"
            required
            placeholder="MENU_CODE (영문 대문자, 숫자, 언더스코어)"
            pattern="[A-Z0-9_]+"
            :disabled="isEditMode"
          />
        </div>

        <div class="form-group">
          <label for="menuUrl">메뉴 URL *</label>
          <input
            id="menuUrl"
            v-model="formData.menuUrl"
            type="text"
            required
            placeholder="/admin/example"
          />
        </div>

        <div class="form-group">
          <label for="menuIcon">아이콘 클래스</label>
          <input
            id="menuIcon"
            v-model="formData.menuIcon"
            type="text"
            placeholder="fas fa-circle"
          />
        </div>

        <div class="form-group">
          <label for="parentMenu">상위 메뉴</label>
          <select id="parentMenu" v-model="formData.parentMenuId">
            <option :value="null">최상위 메뉴</option>
            <option
              v-for="menu in flatMenuList"
              :key="menu.id"
              :value="menu.id"
              :disabled="isEditMode && menu.id === editingMenuId"
            >
              {{ '　'.repeat(menu.level) }}{{ menu.label }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="menuLevel">메뉴 레벨 *</label>
          <input
            id="menuLevel"
            v-model.number="formData.menuLevel"
            type="number"
            min="1"
            max="5"
            required
          />
        </div>

        <div class="form-group">
          <label for="displayOrder">표시 순서</label>
          <input
            id="displayOrder"
            v-model.number="formData.displayOrder"
            type="number"
            min="1"
          />
        </div>

        <div class="form-group">
          <label for="status">상태</label>
          <select id="status" v-model="formData.status">
            <option value="ACTIVE">활성</option>
            <option value="INACTIVE">비활성</option>
          </select>
        </div>

        <div class="modal-actions">
          <button type="button" @click="$emit('close')" class="cancel-btn">
            취소
          </button>
          <button type="submit" class="submit-btn" :disabled="isSubmitting">
            {{ isSubmitting ? '처리중...' : (isEditMode ? '수정' : '생성') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  editingMenuId: {
    type: [String, Number],
    default: null
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  flatMenuList: {
    type: Array,
    default: () => []
  },
  isSubmitting: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'submit']);

// 폼 데이터
const formData = ref({
  menuName: '',
  menuCode: '',
  menuUrl: '',
  menuIcon: 'fas fa-circle',
  parentMenuId: null,
  menuLevel: 1,
  displayOrder: 1,
  status: 'ACTIVE'
});

// 초기 데이터 설정 감시
watch(() => props.initialData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    formData.value = { ...formData.value, ...newData };
  }
}, { immediate: true, deep: true });

// 모달이 닫힐 때 폼 초기화
watch(() => props.show, (newShow) => {
  if (!newShow) {
    resetForm();
  }
});

// 폼 초기화
const resetForm = () => {
  formData.value = {
    menuName: '',
    menuCode: '',
    menuUrl: '',
    menuIcon: 'fas fa-circle',
    parentMenuId: null,
    menuLevel: 1,
    displayOrder: 1,
    status: 'ACTIVE'
  };
};

// 폼 제출 처리
const handleSubmit = () => {
  emit('submit', { ...formData.value });
};
</script>

<style scoped>
/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.menu-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input:disabled {
  background: #f8f9fa;
  color: #6c757d;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  margin-top: 20px;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}

.submit-btn {
  background: #007bff;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
