<template>
  <div v-if="isVisible" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h2>문제 선택</h2>
        <button @click="closeModal" class="close-button">&times;</button>
      </div>
      <div class="modal-body">
        <div class="search-filter-container">
          <div class="search-container">
            <select v-model="selectedSearchField" class="search-select">
              <option v-for="(value, key) in searchFields" :key="value" :value="value">{{ key }}</option>
            </select>
            <input type="text" v-model="searchKeyword" placeholder="검색어를 입력하세요" class="search-input" @keyup.enter="handleSearch">
          </div>
          <div class="filter-container">
            <select v-model="selectedSortField" class="search-select">
              <option v-for="(value, key) in sortFields" :key="value" :value="value">{{ key }}</option>
            </select>
            <select v-model="selectedSortDirection" class="search-select">
              <option v-for="(value, key) in sortDirections" :key="value" :value="value">{{ key }}</option>
            </select>
            <select v-model="selectedPageSize" class="search-select">
              <option v-for="size in pageSizes" :key="size" :value="size">{{ size }}개씩 보기</option>
            </select>
            <button @click="handleSearch" class="btn-search" :disabled="isLoading">검색</button>
          </div>
        </div>
        <div v-if="isLoading" class="loading-indicator">문제 목록을 불러오는 중...</div>
        <div v-else-if="error" class="error-message">{{ error }}</div>
        <div v-else>
          <div v-if="quizzes.length > 0">
            <table class="quiz-table">
              <thead>
                <tr>
                  <th>번호</th>
                  <th>문제 제목</th>
                  <th>액션</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(quiz, index) in quizzes" :key="quiz.quizId">
                  <td>{{ totalCount - (currentPage * selectedPageSize) - index }}</td>
                  <td>{{ quiz.title }}</td>
                  <td>
                    <button @click="selectQuiz(quiz)" class="btn-primary" :disabled="isLinking">선택</button>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="pagination">
              <button @click="fetchQuizzes(currentPage - 1)" :disabled="currentPage === 0">이전</button>
              <span>페이지 {{ currentPage + 1 }} / {{ totalPages }}</span>
              <button @click="fetchQuizzes(currentPage + 1)" :disabled="currentPage >= totalPages - 1">다음</button>
            </div>
          </div>
          <div v-else class="no-results">
            검색 결과가 없습니다.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import apiClient from '@/api/index.js';
import { linkQuizToQrCode } from '@/api/qrQuizMapping';

const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  qrCodeId: {
    type: [String, Number],
    required: true,
  },
});

const emit = defineEmits(['close', 'link-success']);

const quizzes = ref([]);
const isLoading = ref(false);
const isLinking = ref(false);
const error = ref(null);
const currentPage = ref(0);
const totalPages = ref(0);
const totalCount = ref(0);
const searchKeyword = ref('');

// State for dynamic options from API
const searchFields = ref({});
const sortFields = ref({});
const sortDirections = ref({});
const pageSizes = ref([]);

// State for user's selected values
const selectedSearchField = ref('all');
const selectedSortField = ref('quiz_id');
const selectedSortDirection = ref('DESC');
const selectedPageSize = ref(10);

const fetchQuizzes = async (page = 0) => {
  if (!props.isVisible) return;
  isLoading.value = true;
  error.value = null;
  
  const params = {
    page: page + 1,
    size: selectedPageSize.value,
    sortField: selectedSortField.value,
    sortDirection: selectedSortDirection.value,
  };

  if (searchKeyword.value) {
    params.searchKeyword = searchKeyword.value;
    params.selectedSearchField = selectedSearchField.value;
  }

  try {
    const response = await apiClient.get('http://192.168.0.227:8082/api/quizzes', {
      params,
    });
    const responseData = response.data.data;
    quizzes.value = responseData.items;
    totalPages.value = responseData.totalPages;
    totalCount.value = responseData.totalCount;
    currentPage.value = responseData.currentPage - 1;

    // Populate options only on the first successful load to avoid resetting user selections
    if (responseData.allowedSearchFields && Object.keys(searchFields.value).length === 0) {
      searchFields.value = responseData.allowedSearchFields;
    }
    if (responseData.allowedSortFields && Object.keys(sortFields.value).length === 0) {
      sortFields.value = responseData.allowedSortFields;
    }
    if (responseData.allowedSortDirections && Object.keys(sortDirections.value).length === 0) {
      sortDirections.value = responseData.allowedSortDirections;
    }
    if (responseData.allowedPageSizes && pageSizes.value.length === 0) {
      pageSizes.value = responseData.allowedPageSizes;
      // Keep the default selectedPageSize unless it's not in the allowed list
      if (!responseData.allowedPageSizes.includes(selectedPageSize.value)) {
        selectedPageSize.value = responseData.allowedPageSizes[0];
      }
    }
  } catch (err) {
    error.value = '문제 목록을 불러오는 데 실패했습니다. API 서버 연결 또는 CORS 설정을 확인해주세요.';
    console.error(err);
  } finally {
    isLoading.value = false;
  }
};

const handleSearch = () => {
  fetchQuizzes(0);
};

const selectQuiz = async (quiz) => {
  isLinking.value = true;
  try {
    await linkQuizToQrCode(props.qrCodeId, quiz.quizId);
    emit('link-success', quiz);
    closeModal();
  } catch (err) {
    alert(err?.message || '문제 연결에 실패했습니다.');
  } finally {
    isLinking.value = false;
  }
};

const closeModal = () => {
  emit('close');
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('ko-KR');
};

watch(() => props.isVisible, (newValue) => {
  if (newValue) {
    fetchQuizzes(0); // 모달이 보일 때 첫 페이지 데이터 로드
  }
}, { immediate: true });
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 800px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-filter-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.search-container, .filter-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.search-select {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.btn-search {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-search:hover {
  background-color: #0056b3;
}

.no-results {
  text-align: center;
  padding: 40px 0;
  color: #666;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.loading-indicator, .error-message {
  text-align: center;
  padding: 40px 0;
}

.quiz-table {
  width: 100%;
  border-collapse: collapse;
}

.quiz-table th, .quiz-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.quiz-table th {
  background-color: #f2f2f2;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.pagination button {
  margin: 0 5px;
}

.btn-primary[disabled] {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
