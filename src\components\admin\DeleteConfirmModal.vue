<template>
  <div v-if="show" class="modal-overlay" @click="$emit('close')">
    <div class="modal-content delete-modal" @click.stop>
      <div class="modal-header">
        <h3>메뉴 삭제 확인</h3>
        <button @click="$emit('close')" class="close-btn">×</button>
      </div>

      <div class="delete-modal-body">
        <div class="warning-icon">⚠️</div>
        <h4>정말로 이 메뉴를 삭제하시겠습니까?</h4>

        <div v-if="menuToDelete" class="menu-info-box">
          <div class="info-row">
            <span class="label">메뉴명:</span>
            <span class="value">{{ menuToDelete.label }}</span>
          </div>
          <div class="info-row">
            <span class="label">메뉴 코드:</span>
            <span class="value">{{ menuToDelete.menuCode || menuToDelete.routeName }}</span>
          </div>
          <div class="info-row">
            <span class="label">경로:</span>
            <span class="value">{{ menuToDelete.routePath }}</span>
          </div>
        </div>

        <div class="warning-message">
          <p><strong>주의사항:</strong></p>
          <ul>
            <li>삭제된 메뉴는 복구할 수 없습니다.</li>
            <li>이 메뉴에 설정된 모든 권한도 함께 삭제됩니다.</li>
            <li v-if="hasChildMenus" class="error-text">
              ❌ 하위 메뉴가 있어 삭제할 수 없습니다.
            </li>
          </ul>
        </div>
      </div>

      <div class="modal-actions">
        <button @click="$emit('close')" class="cancel-btn">
          취소
        </button>
        <button
          @click="$emit('confirm')"
          class="delete-confirm-btn"
          :disabled="isDeleting || hasChildMenus"
        >
          {{ isDeleting ? '삭제 중...' : '삭제' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  menuToDelete: {
    type: Object,
    default: null
  },
  isDeleting: {
    type: Boolean,
    default: false
  }
});

defineEmits(['close', 'confirm']);

// 하위 메뉴 존재 여부 확인
const hasChildMenus = computed(() => {
  return props.menuToDelete && props.menuToDelete.nodes && props.menuToDelete.nodes.length > 0;
});
</script>

<style scoped>
/* 모달 기본 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.delete-modal {
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.delete-modal-body {
  padding: 20px;
  text-align: center;
}

.warning-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.delete-modal-body h4 {
  color: #dc3545;
  margin-bottom: 20px;
  font-size: 18px;
}

.menu-info-box {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  text-align: left;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  font-weight: bold;
  color: #495057;
  min-width: 80px;
  margin-right: 10px;
}

.info-row .value {
  color: #333;
  font-family: monospace;
}

.warning-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  text-align: left;
}

.warning-message p {
  margin: 0 0 10px 0;
  color: #856404;
  font-weight: bold;
}

.warning-message ul {
  margin: 0;
  padding-left: 20px;
  color: #856404;
}

.warning-message li {
  margin-bottom: 5px;
}

.error-text {
  color: #dc3545 !important;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #dee2e6;
}

.cancel-btn,
.delete-confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}

.delete-confirm-btn {
  background: #dc3545;
  color: white;
}

.delete-confirm-btn:hover:not(:disabled) {
  background: #c82333;
}

.delete-confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #6c757d;
}
</style>
