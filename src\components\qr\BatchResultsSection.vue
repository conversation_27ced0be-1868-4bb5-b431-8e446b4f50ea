<template>
  <div v-if="Array.isArray(batchCreationList) && batchCreationList.length > 0" class="qr-batch-results" data-qr-batch-results>
    <h3>QR 코드 연속 생성 결과</h3>
    
    <!-- 전체 진행률 표시 -->
    <div v-if="isBatchProcessing" class="batch-progress">
      <div class="progress-text">
        {{ Math.floor(batchProgress) }}% 완료 ({{ completedCount }}/{{ totalCount }}개)
      </div>
      <div class="progress-bar-container">
        <div class="progress-bar" :style="{ width: batchProgress + '%' }"></div>
      </div>
    </div>
    
    <!-- 전역 오류 메시지 -->
    <div v-if="batchGlobalError" class="batch-error-message">
      오류: {{ batchGlobalError }}
    </div>
    
    <!-- 결과 요약 (완료 후) -->
    <div v-if="!isBatchProcessing && Array.isArray(batchCreationList) && batchCreationList.length > 0" class="batch-summary">
      <p>처리 완료: 총 {{ totalCount }}개 중 
        <span class="success-count">{{ successCount }}개 성공</span>, 
        <span class="error-count">{{ errorCount }}개 실패</span>
      </p>
    </div>
    
    <!-- 개별 QR 코드 생성 결과 목록 -->
    <div class="batch-results-list">
      <table class="batch-results-table">
        <thead>
          <tr>
            <th>행 번호</th>
            <th>QR 코드 이름</th>
            <th>타입</th>
            <th>타겟 콘텐츠</th>
            <th>상태</th>
            <th>메시지</th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="item in safeBatchCreationList" 
            :key="item.id" 
            :class="{ 'row-success': item.status === 'success', 'row-error': item.status === 'error' }"
          >
            <td>{{ item.excelRowNumber }}</td>
            <td>{{ item.finalQrName }}</td>
            <td>{{ item.qrType }}</td>
            <td>{{ truncateContent(item.targetContent) }}</td>
            <td>
              <span v-if="item.status === 'pending'" class="status-pending">대기 중...</span>
              <span v-else-if="item.status === 'processing'" class="status-processing">생성 중...</span>
              <span v-else-if="item.status === 'success'" class="status-success">성공</span>
              <span v-else-if="item.status === 'error'" class="status-error">실패</span>
            </td>
            <td>
              <span v-if="item.status === 'error'" class="error-message">{{ item.errorMessage }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { truncateText } from '@/utils/qrFormUtils';

// Props 정의
const props = defineProps({
  batchCreationList: {
    type: Array,
    default: () => []
  },
  isBatchProcessing: {
    type: Boolean,
    default: false
  },
  batchProgress: {
    type: Number,
    default: 0
  },
  batchGlobalError: {
    type: String,
    default: ''
  }
});

// 안전한 배치 생성 목록 (배열이 아닌 경우 빈 배열 반환)
const safeBatchCreationList = computed(() => {
  return Array.isArray(props.batchCreationList) ? props.batchCreationList : [];
});

// 전체 개수
const totalCount = computed(() => {
  return safeBatchCreationList.value.length;
});

// 완료된 개수 (성공 + 실패)
const completedCount = computed(() => {
  return safeBatchCreationList.value.filter(item => 
    item.status === 'success' || item.status === 'error'
  ).length;
});

// 성공 개수
const successCount = computed(() => {
  return safeBatchCreationList.value.filter(item => item.status === 'success').length;
});

// 실패 개수
const errorCount = computed(() => {
  return safeBatchCreationList.value.filter(item => item.status === 'error').length;
});

// 타겟 콘텐츠 텍스트 자르기 함수
const truncateContent = (content) => {
  return truncateText(content, 30);
};
</script>
