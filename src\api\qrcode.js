import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 프로젝트의 QR 코드 목록을 가져옵니다.
 * @param {number} projectId - 프로젝트 ID
 * @returns {Promise<Object>} QR 코드 목록을 포함한 응답 객체 Promise
 */
export const getQrCodes = async (projectId, page = 0, size = 10, searchParams = {}) => {
  try {
    // 프로젝트 ID가 없는 경우 에러 발생
    if (!projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 페이지네이션 파라미터 추가
    const params = {
      projectId,
      page,
      size,
      sort: 'createDate,desc'
    };

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    // 새로운 서버 응답 구조: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    const response = await executeApiCall(
      () => apiClient.get(`/qr-codes/list`, { params }),
      'QR 코드 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN용 모든 QR 코드 목록을 가져옵니다.
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {number} [projectId] - 선택적 프로젝트 ID (특정 프로젝트로 필터링하는 경우)
 * @returns {Promise<Object>} QR 코드 목록을 포함한 응답 객체 Promise
 */
export const getAllQrCodes = async (page = 0, size = 10, projectId = null, searchParams = {}) => {
  try {
    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 프로젝트 ID가 있는 경우에만 파라미터에 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/super/qr-codes/all`, { params }),
      'QR 코드 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드 상세 정보를 가져옵니다.
 * @param {number} qrCodeId - QR 코드 ID
 * @returns {Promise<object>} QR 코드 상세 정보 Promise
 */
export const getQrCodeById = async (qrCodeId) => {
  try {
    // QR 코드 ID가 없는 경우 에러 발생
    if (!qrCodeId) {
      throw new Error('QR 코드 ID가 필요합니다.');
    }

    const result = await executeApiCall(
      () => apiClient.get(`/qr-codes/${qrCodeId}`),
      'QR 코드 상세 정보를 가져오는 데 실패했습니다.'
    );

    return result;
  } catch (error) {
    console.error(`QR 코드 상세 정보 오류:`, error);
    const errorMessage = handleApiError(error, 'QR 코드 상세 정보를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 QR 코드를 생성합니다.
 * @param {object} qrCodeData - QR 코드 데이터 (FormData 객체)
 * @returns {Promise<object>} 성공 시 생성된 QR 코드 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const createQrCode = async (qrCodeData) => {
  try {
    // 프로젝트 ID가 없는 경우 에러 발생
    if (!qrCodeData.get('projectId')) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 이벤트 ID 관련 필드 확인
    const eventId = qrCodeData.get('linkedEventId') || qrCodeData.get('linked_event_id') ||
                    qrCodeData.get('eventId') || qrCodeData.get('event_id');

    if (eventId) {
      // 모든 가능한 필드명으로 이벤트 ID 추가 (중복 추가해도 문제 없음)
      qrCodeData.set('linkedEventId', eventId);
      qrCodeData.set('linked_event_id', eventId);
      qrCodeData.set('eventId', eventId);
      qrCodeData.set('event_id', eventId);
    }

    // 서버에서 요구하는 정확한 Content-Type 헤더 설정
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      transformRequest: [(data, headers) => {
        // 추가 파라미터(boundary, charset 등)가 없는 정확한 Content-Type 헤더 설정
        headers['Content-Type'] = 'multipart/form-data';
        return data;
      }]
    };

    const response = await apiClient.post(`/qr-codes`, qrCodeData, config);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 또는 필요한 부분 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || 'QR 코드 생성에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 생성 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드를 수정합니다.
 * @param {number} qrCodeId - QR 코드 ID
 * @param {object} qrCodeData - QR 코드 데이터 (FormData 객체)
 * @returns {Promise<object>} 성공 시 수정된 QR 코드 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const updateQrCode = async (qrCodeId, qrCodeData) => {
  try {
    // QR 코드 ID가 없는 경우 에러 발생
    if (!qrCodeId) {
      throw new Error('QR 코드 ID가 필요합니다.');
    }

    // 이벤트 ID 관련 필드 확인
    const eventId = qrCodeData.get('linkedEventId') || qrCodeData.get('linked_event_id') ||
                    qrCodeData.get('eventId') || qrCodeData.get('event_id');

    if (eventId) {
      // 모든 가능한 필드명으로 이벤트 ID 추가 (중복 추가해도 문제 없음)
      qrCodeData.set('linkedEventId', eventId);
      qrCodeData.set('linked_event_id', eventId);
      qrCodeData.set('eventId', eventId);
      qrCodeData.set('event_id', eventId);
    }

    // 서버에서 요구하는 정확한 Content-Type 헤더 설정
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      transformRequest: [(data, headers) => {
        // 추가 파라미터(boundary, charset 등)가 없는 정확한 Content-Type 헤더 설정
        headers['Content-Type'] = 'multipart/form-data';
        return data;
      }]
    };

    const response = await apiClient.put(`/qr-codes/${qrCodeId}`, qrCodeData, config);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 또는 필요한 부분 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || 'QR 코드 수정에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 수정 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드를 삭제합니다.
 * @param {number} qrCodeId - 삭제할 QR 코드 ID
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const deleteQrCode = async (qrCodeId) => {
  try {
    // QR 코드 ID가 없는 경우 에러 발생
    if (!qrCodeId) {
      throw new Error('QR 코드 ID가 필요합니다.');
    }

    const response = await apiClient.delete(`/qr-codes/remove/${qrCodeId}`);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return true; // 성공 시 true 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || 'QR 코드 삭제에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 여러 QR 코드를 일괄 삭제합니다.
 * @param {number[]} qrCodeIds - 삭제할 QR 코드 ID 배열
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const deleteMultipleQrCodes = async (qrCodeIds) => {
  try {
    // QR 코드 ID 배열이 비어있는 경우 에러 발생
    if (!qrCodeIds || !Array.isArray(qrCodeIds) || qrCodeIds.length === 0) {
      throw new Error('삭제할 QR 코드 ID 목록이 필요합니다.');
    }

    const response = await apiClient.delete('/qr-codes/all/remove', {
      data: qrCodeIds
    });

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return true; // 성공 시 true 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.message || response.data?.error?.message || 'QR 코드 일괄 삭제에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, 'QR 코드 일괄 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};




