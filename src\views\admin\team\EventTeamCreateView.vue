<template>
  <div class="team-create">
    <div class="header-section">
      <h1>팀 생성</h1>
      <button @click="goBack" class="back-btn">목록으로</button>
    </div>

    <div class="create-form-container">
      <form @submit.prevent="createTeam" class="team-form">
        <div class="form-group">
          <label for="eventId">이벤트 선택 *</label>
          <select 
            id="eventId" 
            v-model="formData.eventId" 
            :disabled="isLoadingEvents"
            required
          >
            <option value="" disabled>{{ isLoadingEvents ? '이벤트 목록을 불러오는 중...' : '이벤트를 선택하세요' }}</option>
            <option 
              v-for="event in events" 
              :key="event.eventId" 
              :value="event.eventId"
            >
              {{ event.eventName }}
            </option>
            <option v-if="!isLoadingEvents && events.length === 0" value="" disabled>
              등록된 이벤트가 없습니다
            </option>
          </select>
        </div>

        <div class="form-group">
          <label for="teamName">팀 이름 *</label>
          <input 
            id="teamName" 
            v-model="formData.teamName" 
            type="text" 
            placeholder="팀 이름을 입력하세요"
            required
          />
        </div>

        <div class="form-group">
          <label for="leaderName">팀장 이름</label>
          <input
            id="leaderName"
            type="text"
            v-model="formData.leaderName"
            placeholder="팀장 이름을 입력해주세요"
            :disabled="isLoading"
          />
        </div>

        <div class="form-group">
          <label for="leaderPhone">팀장 연락처</label>
          <input
            id="leaderPhone"
            type="tel"
            v-model="formData.leaderPhone"
            placeholder="팀장 연락처를 입력해주세요"
            :disabled="isLoading"
          />
        </div>

        <div class="form-group">
          <label for="teamCode">팀 코드</label>
          <input
            id="teamCode"
            type="text"
            v-model="formData.teamCode"
            placeholder="팀 고유 코드를 입력하세요(미 입력시 자동생성. 모든 프로젝트, 이벤트 내 유일해야합니다)"
            maxlength="20"
            :disabled="isLoading"
          />
        </div>

        <div class="form-group">
          <label for="description">팀 설명</label>
          <textarea
            id="description"
            v-model="formData.description"
            placeholder="팀에 대한 간단한 설명을 입력해주세요"
            rows="4"
            :disabled="isLoading"
          ></textarea>
        </div>

        <div class="form-group">
          <label for="maxMembers">팀 최대 인원 제한</label>
          <input
            id="maxMembers"
            type="number"
            v-model.number="formData.maxMembers"
            placeholder="최대 인원을 입력하세요 (미입력시 무제한)"
            min="1"
            max="1000"
            :disabled="isLoading"
          />
        </div>

        <div class="form-group">
          <label for="status">팀 상태 *</label>
          <select
            id="status"
            v-model="formData.teamStatus"
            required
            :disabled="isLoading"
          >
            <option value="ACTIVE">활성</option>
            <option value="LOCKED">잠김</option>
            <option value="PENDING_APPROVAL">승인대기</option>
          </select>
        </div>

        <div class="form-group">
          <label for="profileImage">팀 프로필 이미지 업로드</label>
          <input
            id="profileImage"
            type="file"
            @change="handleProfileImageUpload"
            accept="image/*"
            :disabled="isLoading"
          />
          <div v-if="profileImagePreview" class="profile-image-preview">
            <img :src="profileImagePreview" alt="팀 프로필 이미지 미리보기" style="max-width: 200px; margin-top: 8px;" @error="handleImageError" />
            <div class="file-info">
              <div class="file-name">파일명: {{ getFileName() }}</div>
              <button type="button" @click="removeProfileImage" class="remove-btn">제거</button>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" @click="goBack" class="cancel-btn" :disabled="isLoading">
            취소
          </button>
          <button type="submit" class="submit-btn" :disabled="isLoading || !isFormValid">
            <span v-if="isLoading">생성 중...</span>
            <span v-else>팀 생성</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { getEvents } from '@/api/events';
import { useImageError } from '@/composables/useImageError';
import apiClient from '@/api/index';

const router = useRouter();
const authStore = useAuthStore();
const isLoading = ref(false);
const events = ref([]);
const isLoadingEvents = ref(false);

// 이미지 에러 핸들링
const { handleImageError } = useImageError();

// 현재 프로젝트 정보
const currentProject = computed(() => {
  return authStore.currentProject;
});

const formData = ref({
  eventId: '',
  teamName: '',
  teamCode: '',
  description: '',
  maxMembers: null,
  teamStatus: 'ACTIVE',
  leaderName: '',
  leaderEmail: '',
  leaderPhone: ''
});

// 이미지 관련 변수들
const profileImageFile = ref(null);
const profileImagePreview = ref('');

// 폼 유효성 검사 - 항상 true로 설정하여 버튼을 활성화
const isFormValid = computed(() => {
  return true;
});

// 프로필 이미지 업로드 처리
const handleProfileImageUpload = (e) => {
  const file = e.target.files && e.target.files[0];
  if (file) {
    profileImageFile.value = file;
    profileImagePreview.value = URL.createObjectURL(file);
  } else {
    profileImageFile.value = null;
    profileImagePreview.value = '';
  }
};

// 프로필 이미지 제거
const removeProfileImage = () => {
  profileImageFile.value = null;
  profileImagePreview.value = '';
  // 파일 input 초기화
  const fileInput = document.getElementById('profileImage');
  if (fileInput) {
    fileInput.value = '';
  }
};

// 파일 이름 가져오기
const getFileName = () => {
  if (profileImageFile.value) {
    return profileImageFile.value.name;
  }
  return '';
};

// 목록으로 돌아가기
const goBack = () => {
  router.push('/events/teams');
};

// 팀 생성
const createTeam = async () => {
  // 필수 항목 체크 및 구체적인 안내
  const missingFields = [];
  
  if (!formData.value.eventId) {
    missingFields.push('이벤트 선택');
  }
  if (!formData.value.teamName.trim()) {
    missingFields.push('팀 이름');
  }
  
  if (missingFields.length > 0) {
    alert(`다음 필수 항목을 입력해주세요:\n\n${missingFields.map(field => `• ${field}`).join('\n')}`);
    return;
  }

  if (!currentProject.value?.projectId) {
    alert('프로젝트가 선택되지 않았습니다.');
    return;
  }

  isLoading.value = true;
  try {
    const formDataToSend = new FormData();
    
    // 기본 팀 정보
    formDataToSend.append('projectId', currentProject.value.projectId);
    formDataToSend.append('eventId', formData.value.eventId);
    formDataToSend.append('teamName', formData.value.teamName);
    formDataToSend.append('teamCode', formData.value.teamCode || '');
    formDataToSend.append('description', formData.value.description || '');
    formDataToSend.append('maxMembers', formData.value.maxMembers || '');
    formDataToSend.append('teamStatus', formData.value.teamStatus);
    
    // 팀장 정보
    formDataToSend.append('leaderName', formData.value.leaderName);
    formDataToSend.append('leaderPhone', formData.value.leaderPhone || '');
    
    // 프로필 이미지 파일
    if (profileImageFile.value) {
      formDataToSend.append('profileImageFile', profileImageFile.value);
    }
    
    const response = await apiClient.post('/teams', formDataToSend, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (response.data && response.data.success) {
      alert(response.data.message || '팀이 성공적으로 생성되었습니다.');
      goBack();
    } else {
      // 서버에서 success: false로 응답한 경우
      const errorMessage = response.data?.error?.message || response.data?.message || '팀 생성에 실패했습니다.';
      alert(errorMessage);
    }
  } catch (error) {
    console.error('팀 생성 실패:', error);
    // HTTP 에러 응답 처리
    if (error.response?.data) {
      const errorData = error.response.data;
      const errorMessage = errorData.error?.message || errorData.message || '팀 생성에 실패했습니다.';
      alert(errorMessage);
    } else {
      alert(error.message || '팀 생성에 실패했습니다. 다시 시도해주세요.');
    }
  } finally {
    isLoading.value = false;
  }
};

// 이벤트 목록 로드
const loadEvents = async () => {
  if (!currentProject.value?.projectId) {
    console.warn('프로젝트 ID가 없어 이벤트 목록을 가져올 수 없습니다.');
    events.value = [];
    return;
  }

  isLoadingEvents.value = true;
  try {
    // API 호출
    const response = await getEvents(currentProject.value.projectId);

    // 응답 구조 확인
    let eventItems = [];
    if (Array.isArray(response)) {
      // 직접 배열이 반환된 경우
      eventItems = response;
    } else {
      eventItems = [];
    }

    events.value = eventItems;
  } catch (error) {
    console.error('이벤트 목록 로드 실패:', error);
    events.value = [];
  } finally {
    isLoadingEvents.value = false;
  }
};

// 컴포넌트 마운트 시 초기화
onMounted(() => {
  loadEvents();
});
</script>

<style scoped>
.team-create {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

h1 {
  margin: 0;
  color: #333;
}

.back-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.back-btn:hover {
  background-color: #5a6268;
}

.create-form-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 32px;
}

.team-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.form-group select {
  background-color: white;
  cursor: pointer;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  padding-top: 24px;
  border-top: 1px solid #eee;
}

.cancel-btn {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.cancel-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.submit-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  min-width: 120px;
}

.submit-btn:hover:not(:disabled) {
  background-color: #218838;
}

.submit-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* 이미지 관련 스타일 */
.profile-image-preview {
  margin-top: 8px;
}

.file-info {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-name {
  font-size: 0.9em;
  color: #666;
  margin-right: 10px;
}

.remove-btn {
  background-color: #F44336;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
}

.remove-btn:hover {
  background-color: #d32f2f;
}

@media (max-width: 768px) {
  .team-create {
    padding: 12px;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .create-form-container {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}
</style>