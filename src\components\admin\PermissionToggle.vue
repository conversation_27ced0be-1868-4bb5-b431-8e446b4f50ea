<template>
  <label class="permission-toggle" :class="{ disabled: disabled }">
    <input
      type="checkbox"
      :checked="value === 'Y'"
      :disabled="disabled"
      @change="handleChange"
    />
    <span class="toggle-slider"></span>
    <span class="toggle-label">{{ value === 'Y' ? '허용' : '거부' }}</span>
  </label>
</template>

<script setup>
const props = defineProps({
  value: {
    type: String,
    default: 'N',
    validator: (value) => ['Y', 'N'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['change']);

const handleChange = (event) => {
  if (!props.disabled) {
    const newValue = event.target.checked ? 'Y' : 'N';
    emit('change', newValue);
  }
};
</script>

<style scoped>
.permission-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.permission-toggle.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.permission-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  background-color: #ccc;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.toggle-slider:before {
  content: "";
  position: absolute;
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s;
}

.permission-toggle input:checked + .toggle-slider {
  background-color: #28a745;
}

.permission-toggle input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.permission-toggle.disabled .toggle-slider {
  background-color: #e9ecef;
}

.permission-toggle.disabled input:checked + .toggle-slider {
  background-color: #6c757d;
}

.toggle-label {
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
}

.permission-toggle input:checked + .toggle-slider + .toggle-label {
  color: #28a745;
}

.permission-toggle.disabled .toggle-label {
  color: #6c757d;
}
</style>
