/**
 * 메뉴 관리 유틸리티 함수들
 * 라우터 정보를 tree 구조로 변환하고 메뉴 관리 기능을 제공합니다.
 */

/**
 * 라우터 정보를 tree 구조로 변환
 * @param {Array} routes - Vue Router의 routes 배열
 * @param {String} userRole - 현재 사용자 역할
 * @returns {Array} tree 구조의 메뉴 데이터
 */
export function convertRoutesToTreeData(routes, userRole) {
  const treeData = [];
  let nodeId = 1;

  // 메뉴에 표시할 라우트만 필터링
  const menuRoutes = routes.filter(route => {
    // 메뉴 제목이 있고, 인증이 필요한 라우트만 포함
    if (!route.meta || !route.meta.title || !route.meta.requiresAuth) {
      return false;
    }

    // 권한 체크
    const requiredPermissions = route.meta.permissions;
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (!userRole || !requiredPermissions.includes(userRole)) {
        return false;
      }
    }

    return true;
  });

  // 라우트를 tree 구조로 변환
  menuRoutes.forEach(route => {
    const menuItem = {
      id: nodeId++,
      label: route.meta.title,
      routeName: route.name,
      routePath: route.path,
      permissions: route.meta.permissions || [],
      visible: true, // 메뉴 표시 여부
      order: route.meta.order || 999, // 메뉴 순서
      meta: route.meta
    };

    // 자식 라우트가 있는 경우
    if (route.children && route.children.length > 0) {
      const childNodes = [];
      
      route.children.forEach(child => {
        // 자식 라우트도 메뉴에 표시할 조건 확인
        if (child.meta && child.meta.title) {
          // 자식 라우트 권한 체크
          const childPermissions = child.meta.permissions;
          if (childPermissions && childPermissions.length > 0) {
            if (!userRole || !childPermissions.includes(userRole)) {
              return; // 권한이 없으면 스킵
            }
          }

          childNodes.push({
            id: nodeId++,
            label: child.meta.title,
            routeName: child.name,
            routePath: child.path,
            permissions: child.meta.permissions || [],
            visible: true,
            order: child.meta.order || 999,
            meta: child.meta,
            openInNewTab: child.meta.openInNewTab || false
          });
        }
      });

      if (childNodes.length > 0) {
        // 자식 노드를 order 순으로 정렬
        childNodes.sort((a, b) => a.order - b.order);
        menuItem.nodes = childNodes;
      }
    }

    treeData.push(menuItem);
  });

  // 최상위 메뉴를 order 순으로 정렬
  treeData.sort((a, b) => a.order - b.order);

  return treeData;
}

/**
 * Tree 데이터에서 특정 노드 찾기
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Number} nodeId - 찾을 노드의 ID
 * @returns {Object|null} 찾은 노드 또는 null
 */
export function findNodeById(treeData, nodeId) {
  for (const node of treeData) {
    if (node.id === nodeId) {
      return node;
    }
    
    if (node.nodes && node.nodes.length > 0) {
      const found = findNodeById(node.nodes, nodeId);
      if (found) {
        return found;
      }
    }
  }
  
  return null;
}

/**
 * Tree 데이터에서 노드 업데이트
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Number} nodeId - 업데이트할 노드의 ID
 * @param {Object} updates - 업데이트할 속성들
 * @returns {Boolean} 업데이트 성공 여부
 */
export function updateNodeById(treeData, nodeId, updates) {
  for (const node of treeData) {
    if (node.id === nodeId) {
      Object.assign(node, updates);
      return true;
    }
    
    if (node.nodes && node.nodes.length > 0) {
      const updated = updateNodeById(node.nodes, nodeId, updates);
      if (updated) {
        return true;
      }
    }
  }
  
  return false;
}

/**
 * 메뉴 표시/숨김 토글
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Number} nodeId - 토글할 노드의 ID
 * @returns {Boolean} 토글 후 표시 상태
 */
export function toggleMenuVisibility(treeData, nodeId) {
  const node = findNodeById(treeData, nodeId);
  if (node) {
    node.visible = !node.visible;
    return node.visible;
  }
  return false;
}

/**
 * 메뉴 순서 변경
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Number} nodeId - 순서를 변경할 노드의 ID
 * @param {Number} newOrder - 새로운 순서
 * @returns {Boolean} 변경 성공 여부
 */
export function changeMenuOrder(treeData, nodeId, newOrder) {
  return updateNodeById(treeData, nodeId, { order: newOrder });
}

/**
 * Tree 데이터를 평면 배열로 변환 (모든 노드를 하나의 배열로)
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Number} level - 현재 레벨 (기본값: 0)
 * @returns {Array} 평면화된 노드 배열
 */
export function flattenTreeData(treeData, level = 0) {
  const flattened = [];
  
  treeData.forEach(node => {
    flattened.push({
      ...node,
      level,
      hasChildren: node.nodes && node.nodes.length > 0
    });
    
    if (node.nodes && node.nodes.length > 0) {
      flattened.push(...flattenTreeData(node.nodes, level + 1));
    }
  });
  
  return flattened;
}

/**
 * 메뉴 권한 체크
 * @param {Object} menuItem - 메뉴 아이템
 * @param {String} userRole - 사용자 역할
 * @returns {Boolean} 접근 권한 여부
 */
export function hasMenuPermission(menuItem, userRole) {
  if (!menuItem.permissions || menuItem.permissions.length === 0) {
    return true;
  }
  
  return userRole && menuItem.permissions.includes(userRole);
}

/**
 * 표시 가능한 메뉴만 필터링
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {String} userRole - 사용자 역할
 * @returns {Array} 표시 가능한 메뉴 데이터
 */
export function getVisibleMenus(treeData, userRole) {
  return treeData.filter(node => {
    // 메뉴가 숨김 상태이면 제외
    if (!node.visible) {
      return false;
    }

    // 권한 체크
    if (!hasMenuPermission(node, userRole)) {
      return false;
    }

    // 자식 메뉴가 있는 경우 재귀적으로 필터링
    if (node.nodes && node.nodes.length > 0) {
      node.nodes = getVisibleMenus(node.nodes, userRole);
    }

    return true;
  });
}

/**
 * 메뉴 설정을 localStorage에 저장
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 */
export function saveMenuSettings(treeData) {
  try {
    const settings = extractMenuSettings(treeData);
    localStorage.setItem('menuSettings', JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('메뉴 설정 저장 실패:', error);
    return false;
  }
}

/**
 * localStorage에서 메뉴 설정 불러오기
 * @returns {Object|null} 저장된 메뉴 설정 또는 null
 */
export function loadMenuSettings() {
  try {
    const settings = localStorage.getItem('menuSettings');
    return settings ? JSON.parse(settings) : null;
  } catch (error) {
    console.error('메뉴 설정 불러오기 실패:', error);
    return null;
  }
}

/**
 * 메뉴 데이터에서 설정 정보만 추출
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @returns {Object} 메뉴 설정 정보
 */
function extractMenuSettings(treeData) {
  const settings = {};

  const extractFromNodes = (nodes) => {
    nodes.forEach(node => {
      if (node.routeName) {
        settings[node.routeName] = {
          visible: node.visible,
          order: node.order
        };
      }

      if (node.nodes && node.nodes.length > 0) {
        extractFromNodes(node.nodes);
      }
    });
  };

  extractFromNodes(treeData);
  return settings;
}

/**
 * 저장된 설정을 메뉴 데이터에 적용
 * @param {Array} treeData - tree 구조의 메뉴 데이터
 * @param {Object} settings - 저장된 메뉴 설정
 */
export function applyMenuSettings(treeData, settings) {
  if (!settings) return;

  const applyToNodes = (nodes) => {
    nodes.forEach(node => {
      if (node.routeName && settings[node.routeName]) {
        const savedSettings = settings[node.routeName];
        if (savedSettings.visible !== undefined) {
          node.visible = savedSettings.visible;
        }
        if (savedSettings.order !== undefined) {
          node.order = savedSettings.order;
        }
      }

      if (node.nodes && node.nodes.length > 0) {
        applyToNodes(node.nodes);
      }
    });
  };

  applyToNodes(treeData);
}
