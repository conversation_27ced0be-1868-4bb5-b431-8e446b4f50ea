/**
 * 메뉴 관리 API 클라이언트
 * 백엔드 메뉴 관리 시스템과 연동하는 API 함수들
 */

import apiClient from './index';

const BASE_URL = '/manage/menus';

/**
 * SUPER_ADMIN 전용 API
 */

/**
 * 메뉴 생성
 * @param {Object} menuData - 메뉴 생성 데이터
 * @param {number|null} menuData.parentMenuId - 상위 메뉴 ID (최상위면 null)
 * @param {string} menuData.menuCode - 메뉴 코드 (영문 대문자, 숫자, 언더스코어)
 * @param {string} menuData.menuName - 메뉴 이름
 * @param {string} menuData.menuUrl - 메뉴 URL
 * @param {string} menuData.menuIcon - 아이콘 클래스
 * @param {number} menuData.menuLevel - 메뉴 레벨 (1: 최상위, 2: 2단계...)
 * @param {number} menuData.displayOrder - 표시 순서 (생략 시 자동 설정)
 * @param {string} menuData.status - 메뉴 상태 (ACTIVE/INACTIVE)
 * @returns {Promise<Object>} 생성된 메뉴 ID
 */
export async function createMenu(menuData) {
  try {
    const response = await apiClient.post(BASE_URL, menuData);
    return response.data;
  } catch (error) {
    console.error('메뉴 생성 실패:', error);
    throw error;
  }
}

/**
 * 메뉴 수정
 * @param {number} menuId - 메뉴 ID
 * @param {Object} menuData - 수정할 메뉴 데이터
 * @returns {Promise<Object>} 성공 여부
 */
export async function updateMenu(menuId, menuData) {
  try {
    const response = await apiClient.put(`${BASE_URL}/${menuId}`, menuData);
    return response.data;
  } catch (error) {
    console.error('메뉴 수정 실패:', error);
    throw error;
  }
}

/**
 * 메뉴 삭제
 * @param {number} menuId - 메뉴 ID
 * @returns {Promise<Object>} 성공 여부
 */
export async function deleteMenu(menuId) {
  try {
    const response = await apiClient.delete(`${BASE_URL}/${menuId}`);
    return response.data;
  } catch (error) {
    console.error('메뉴 삭제 실패:', error);
    throw error;
  }
}

/**
 * 전체 메뉴 트리 조회 (SUPER_ADMIN 전용)
 * @returns {Promise<Array>} 모든 메뉴를 계층형 트리 구조로 반환
 */
export async function getAllMenuTree() {
  try {
    const response = await apiClient.get(`${BASE_URL}/tree`);
    return response.data;
  } catch (error) {
    console.error('전체 메뉴 트리 조회 실패:', error);
    throw error;
  }
}

/**
 * 역할별 메뉴 권한 설정
 * @param {number} menuId - 메뉴 ID
 * @param {string} roleId - 역할 ID (PROJECT_ADMIN, SUB_ADMIN, VIEWER 등)
 * @param {boolean} isAccessible - 접근 가능 여부
 * @returns {Promise<Object>} 성공 여부
 */
export async function setRoleMenuPermission(menuId, roleId, isAccessible) {
  try {
    const accessibleParam = isAccessible ? 'Y' : 'N';
    const response = await apiClient.post(
      `${BASE_URL}/${menuId}/roles/${roleId}/permissions?isAccessible=${accessibleParam}`
    );
    return response.data;
  } catch (error) {
    console.error('역할별 메뉴 권한 설정 실패:', error);
    throw error;
  }
}

/**
 * 개별 사용자 메뉴 권한 설정
 * @param {number} menuId - 메뉴 ID
 * @param {string} userEmail - 사용자 이메일
 * @param {Object} permissionData - 권한 설정 데이터
 * @param {string} permissionData.isAccessible - 접근 가능 여부 (Y/N)
 * @param {string} permissionData.permissionNote - 권한 부여 사유
 * @returns {Promise<Object>} 성공 여부
 */
export async function setUserMenuPermission(menuId, userEmail, permissionData) {
  try {
    const response = await apiClient.post(
      `${BASE_URL}/${menuId}/users/${userEmail}/permissions`,
      permissionData
    );
    return response.data;
  } catch (error) {
    console.error('개별 사용자 메뉴 권한 설정 실패:', error);
    throw error;
  }
}

/**
 * 특정 메뉴의 사용자별 CRUD 권한 조회
 * @param {number} menuId - 메뉴 ID
 * @param {Object} params - 쿼리 파라미터
 * @param {number} params.page - 페이지 번호 (0부터 시작)
 * @param {number} params.size - 페이지 크기 (기본값: 10)
 * @param {string} params.search - 사용자 이메일 검색어 (선택사항)
 * @returns {Promise<Object>} 사용자별 CRUD 권한 목록
 */
export async function getMenuUserPermissions(menuId, params = {}) {
  try {
    const { page = 0, size = 10, search = '' } = params;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      ...(search && { search })
    });

    const response = await apiClient.get(
      `${BASE_URL}/${menuId}/permissions/users?${queryParams}`
    );
    return response.data;
  } catch (error) {
    console.error('메뉴 사용자별 권한 조회 실패:', error);
    throw error;
  }
}

/**
 * 메뉴별 사용자 CRUD 권한 설정/수정
 * @param {number} menuId - 메뉴 ID
 * @param {string} userEmail - 대상 사용자 이메일
 * @param {Object} permissionData - CRUD 권한 설정 데이터
 * @param {string} permissionData.canRead - 읽기 권한 (Y/N)
 * @param {string} permissionData.canWrite - 쓰기 권한 (Y/N)
 * @param {string} permissionData.canUpdate - 수정 권한 (Y/N)
 * @param {string} permissionData.canDelete - 삭제 권한 (Y/N)
 * @param {string} permissionData.permissionNote - 권한 부여 사유
 * @returns {Promise<Object>} 성공 여부
 */
export async function setUserCrudPermission(menuId, userEmail, permissionData) {
  try {
    const response = await apiClient.post(
      `${BASE_URL}/${menuId}/users/${userEmail}/permissions`,
      permissionData
    );
    return response.data;
  } catch (error) {
    console.error('사용자 CRUD 권한 설정 실패:', error);
    throw error;
  }
}

/**
 * 메뉴별 사용자 CRUD 권한 수정
 * @param {number} menuId - 메뉴 ID
 * @param {string} userEmail - 대상 사용자 이메일
 * @param {Object} permissionData - CRUD 권한 수정 데이터
 * @param {string} permissionData.canRead - 읽기 권한 (Y/N)
 * @param {string} permissionData.canWrite - 쓰기 권한 (Y/N)
 * @param {string} permissionData.canUpdate - 수정 권한 (Y/N)
 * @param {string} permissionData.canDelete - 삭제 권한 (Y/N)
 * @param {string} permissionData.permissionNote - 권한 부여 사유
 * @returns {Promise<Object>} 성공 여부
 */
export async function updateUserCrudPermission(menuId, userEmail, permissionData) {
  try {
    const response = await apiClient.put(
      `${BASE_URL}/${menuId}/users/${userEmail}/permissions`,
      permissionData
    );
    return response.data;
  } catch (error) {
    console.error('사용자 CRUD 권한 수정 실패:', error);
    throw error;
  }
}

/**
 * 메뉴별 사용자 권한 삭제 (기본값으로 복원)
 * @param {number} menuId - 메뉴 ID
 * @param {string} userEmail - 대상 사용자 이메일
 * @returns {Promise<Object>} 성공 여부
 */
export async function deleteUserPermission(menuId, userEmail) {
  try {
    const response = await apiClient.delete(
      `${BASE_URL}/${menuId}/permissions/users/${userEmail}`
    );
    return response.data;
  } catch (error) {
    console.error('사용자 권한 삭제 실패:', error);
    throw error;
  }
}

/**
 * 메뉴별 사용자 권한 일괄 설정
 * @param {number} menuId - 메뉴 ID
 * @param {Array} userPermissions - 사용자 권한 배열
 * @param {string} userPermissions[].userEmail - 사용자 이메일
 * @param {string} userPermissions[].canRead - 읽기 권한 (Y/N)
 * @param {string} userPermissions[].canWrite - 쓰기 권한 (Y/N)
 * @param {string} userPermissions[].canUpdate - 수정 권한 (Y/N)
 * @param {string} userPermissions[].canDelete - 삭제 권한 (Y/N)
 * @param {string} userPermissions[].permissionNote - 권한 부여 사유
 * @returns {Promise<Object>} 성공 여부
 */
export async function setBatchUserPermissions(menuId, userPermissions) {
  try {
    const response = await apiClient.post(
      `${BASE_URL}/${menuId}/users/permissions/batch`,
      { userPermissions }
    );
    return response.data;
  } catch (error) {
    console.error('사용자 권한 일괄 설정 실패:', error);
    throw error;
  }
}

/**
 * 일반 사용자용 API
 */

/**
 * 접근 가능한 메뉴 트리 조회
 * @returns {Promise<Array>} 현재 로그인한 사용자가 접근 가능한 메뉴만 계층형 트리로 반환
 */
export async function getAccessibleMenuTree() {
  try {
    const response = await apiClient.get(`${BASE_URL}/accessible`);
    return response.data;
  } catch (error) {
    console.error('접근 가능한 메뉴 트리 조회 실패:', error);
    throw error;
  }
}

/**
 * 특정 메뉴 접근 권한 확인
 * @param {number} menuId - 메뉴 ID
 * @returns {Promise<boolean>} 접근 가능 여부
 */
export async function checkMenuAccess(menuId) {
  try {
    const response = await apiClient.get(`${BASE_URL}/${menuId}/access`);
    return response.data;
  } catch (error) {
    console.error('메뉴 접근 권한 확인 실패:', error);
    throw error;
  }
}

/**
 * 유틸리티 함수들
 */

/**
 * 백엔드 메뉴 데이터를 프론트엔드 tree 구조로 변환
 * @param {Array} backendMenuData - 백엔드에서 받은 메뉴 데이터
 * @returns {Array} vue3-tree에서 사용할 수 있는 형태로 변환된 데이터
 */
export function convertBackendMenuToTreeData(backendMenuData) {
  return backendMenuData.map(menu => ({
    id: menu.menuId,
    label: menu.menuName,
    routeName: menu.menuCode,
    routePath: menu.menuUrl,
    menuCode: menu.menuCode,
    menuIcon: menu.menuIcon,
    menuLevel: menu.menuLevel,
    displayOrder: menu.displayOrder,
    status: menu.status,
    visible: menu.status === 'ACTIVE',
    accessible: menu.accessible,
    createDate: menu.createDate,
    parentMenuId: menu.parentMenuId,
    nodes: menu.children ? convertBackendMenuToTreeData(menu.children) : []
  }));
}

/**
 * 프론트엔드 tree 데이터를 백엔드 형태로 변환
 * @param {Object} treeNode - vue3-tree 노드 데이터
 * @returns {Object} 백엔드 API에 전송할 수 있는 형태로 변환된 데이터
 */
export function convertTreeDataToBackendMenu(treeNode) {
  return {
    parentMenuId: treeNode.parentMenuId || null,
    menuCode: treeNode.menuCode || treeNode.routeName,
    menuName: treeNode.label,
    menuUrl: treeNode.routePath,
    menuIcon: treeNode.menuIcon || 'fas fa-circle',
    menuLevel: treeNode.menuLevel || 1,
    displayOrder: treeNode.displayOrder || treeNode.order || 1,
    status: treeNode.visible ? 'ACTIVE' : 'INACTIVE'
  };
}

/**
 * API 에러 처리 헬퍼
 * @param {Error} error - API 에러 객체
 * @returns {string} 사용자에게 표시할 에러 메시지
 */
export function getMenuApiErrorMessage(error) {
  if (error.response && error.response.data) {
    const responseData = error.response.data;

    // 백엔드 표준 오류 응답 구조 처리
    if (responseData.success === false && responseData.error) {
      return responseData.error.message || '알 수 없는 오류가 발생했습니다.';
    }

    // 기존 방식 호환성 유지
    if (responseData.message) {
      return responseData.message;
    }

    // HTTP 상태 코드별 기본 메시지
    const status = error.response.status;
    switch (status) {
      case 403:
        return '메뉴 관리 권한이 없습니다.';
      case 404:
        return '요청한 메뉴를 찾을 수 없습니다.';
      case 409:
        return '요청을 처리할 수 없습니다. (충돌)';
      case 500:
        return '서버 내부 오류가 발생했습니다.';
      default:
        return `오류가 발생했습니다. (${status})`;
    }
  }

  // 네트워크 오류 등
  if (error.message) {
    return `네트워크 오류: ${error.message}`;
  }

  return '알 수 없는 오류가 발생했습니다.';
}
