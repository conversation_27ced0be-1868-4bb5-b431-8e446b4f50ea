<template>
  <div class="control-panel">
    <div class="controls">
      <button @click="$emit('expand-all')" class="control-btn">모두 펼치기</button>
      <button @click="$emit('collapse-all')" class="control-btn">모두 접기</button>
      <button @click="$emit('create-menu')" class="control-btn create-btn">새 메뉴 추가</button>
      <button @click="$emit('refresh')" class="control-btn">새로고침</button>
    </div>
    <div class="info">
      <span class="menu-count">총 {{ totalMenuCount }}개 메뉴</span>
      <span class="visible-count">표시: {{ visibleMenuCount }}개</span>
      <span class="hidden-count">숨김: {{ hiddenMenuCount }}개</span>
    </div>
  </div>
</template>

<script setup>
defineProps({
  totalMenuCount: {
    type: Number,
    default: 0
  },
  visibleMenuCount: {
    type: Number,
    default: 0
  },
  hiddenMenuCount: {
    type: Number,
    default: 0
  }
});

defineEmits([
  'expand-all',
  'collapse-all', 
  'create-menu',
  'refresh'
]);
</script>

<style scoped>
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.controls {
  display: flex;
  gap: 10px;
}

.control-btn {
  padding: 8px 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.control-btn:hover {
  background: #545b62;
}

.create-btn {
  background: #28a745 !important;
}

.create-btn:hover {
  background: #218838 !important;
}

.info {
  display: flex;
  gap: 15px;
  font-size: 14px;
}

.menu-count {
  color: #333;
  font-weight: bold;
}

.visible-count {
  color: #28a745;
}

.hidden-count {
  color: #dc3545;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 15px;
  }

  .controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .info {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
