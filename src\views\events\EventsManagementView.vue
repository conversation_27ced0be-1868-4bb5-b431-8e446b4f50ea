<template>
  <div class="events-management">
    <h1>이벤트 관리</h1>

    <div class="filters">
      <div class="filter-group">
        <button @click="navigateToCreateEvent" class="create-btn">새 이벤트 추가</button>
        
        <!-- 페이지당 항목 수 선택 UI -->
        <div class="items-per-page-selector">
          <span>페이지당 항목:</span>
          <div class="items-per-page-buttons">
            <button 
              @click="changeItemsPerPage(10)" 
              :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
              10개
            </button>
            <button 
              @click="changeItemsPerPage(30)" 
              :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
              30개
            </button>
            <button 
              @click="changeItemsPerPage(50)" 
              :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
              50개
            </button>
          </div>
        </div>
      </div>

      <div class="filter-group">
        <label for="searchTypeSelect">검색 유형:</label>
        <select id="searchTypeSelect" v-model="searchType">
          <option value="">전체</option>
          <option v-for="type in searchTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>

        <label for="searchInput">검색어:</label>
        <input
          id="searchInput"
          v-model="searchQuery"
          @input="handleSearch"
          @keyup.enter="searchEvents"
          placeholder="검색어 입력"
        />

        <button @click="searchEvents" class="search-btn">검색</button>
        <button @click="resetSearch" class="reset-btn">초기화</button>
      </div>
    </div>

    <div class="events-table-container">
      <table class="events-table" v-if="filteredEvents.length > 0">
        <thead>
          <tr>
            <th>번호</th>
            <th>이벤트 이름</th>
            <th>설명</th>
            <th>시작날짜</th>
            <th>종료날짜</th>
            <th>연동된 사전 신청서 이름</th>
            <th>연동된 팀 이름</th>
            <th>생성날짜</th>
            <th>기능</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(event, index) in filteredEvents" :key="event.eventId">
            <td>{{ calculateIndex(index) }}</td>
            <td>{{ event.eventName }}</td>
            <td>{{ event.description }}</td>
            <td>{{ formatDate(event.startDate) }}</td>
            <td>{{ formatDate(event.endDate) }}</td>
            <td>{{ event.preRegistrationFormName ? event.preRegistrationFormName : '-' }}</td>
            <td>{{ event.teamName ? event.teamName : '-' }}</td>
            <td>{{ formatDate(event.createDate) }}</td>
            <td>
              <button class="action-btn view-btn" @click="viewEvent(event.eventId)">보기</button>
              <button class="action-btn edit-btn" @click="editEvent(event.eventId)">수정</button>
              <button class="action-btn delete-btn" @click="removeEvent(event.eventId)">삭제</button>
              <button class="action-btn" @click="copyEvent(event.eventId)">복사</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="isLoading" class="loading-message">
        <div class="loading-spinner"></div>
        <p>이벤트 정보를 불러오는 중입니다...</p>
      </div>

      <div v-else-if="error" class="error-message">
        <p>{{ error }}</p>
      </div>

      <div v-else-if="!authStore.user?.roleId === 'SUPER_ADMIN' && !currentProjectId" class="no-data-message">
        <p>프로젝트를 선택해주세요.</p>
      </div>

      <div v-else class="no-data-message">
        <p>이벤트 정보가 없습니다.</p>
      </div>
    </div>

    <!-- 페이지네이션 -->
    <div class="pagination" v-if="events.length > 0">
      <button
        @click="goToPage(0)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &laquo;
      </button>
      <button
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &lt;
      </button>

      <span class="page-info">{{ displayPage }} / {{ totalPages || 1 }}</span>

      <button
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
        class="pagination-btn"
      >
        &gt;
      </button>
      <button
        @click="goToPage(totalPages - 1)"
        :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
        class="pagination-btn"
      >
        &raquo;
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { deleteEvent, getEventsPaginated, getAllEvents, copyEvent as copyEventApi } from '@/api/events';

const authStore = useAuthStore();
const router = useRouter();
const events = ref([]);
const searchQuery = ref('');
const searchType = ref(''); // 검색 유형 (서버에서 제공하는 availableSearchTypes의 value)
const searchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록
const isLoading = ref(false);
const error = ref(null);

// 개발 환경 여부 확인 (디버깅 정보 표시용)
const isDev = ref(process.env.NODE_ENV === 'development');

// 페이지네이션 관련 상태
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = ref(10); // 페이지당 항목 수
const totalElements = ref(0); // 전체 항목 수
const totalPages = ref(0); // 전체 페이지 수

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 필터링된 이벤트 목록 (서버 검색 사용 시에는 events.value를 그대로 사용)
const filteredEvents = computed(() => {
  return events.value;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  return totalElements.value - (currentPage.value * itemsPerPage.value + index);
};

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  itemsPerPage.value = count;
  currentPage.value = 0; // 첫 페이지로 이동
  loadEvents(); // 이벤트 목록 다시 로드
};

const currentProjectId = computed(() => authStore.currentProject?.projectId);

const loadEvents = async () => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 모든 이벤트를 가져올 수 있음
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

  // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
  if (!isSuperAdmin && !currentProjectId.value) {
    error.value = '프로젝트가 선택되지 않았습니다.';
    return;
  }

  isLoading.value = true;
  error.value = null;

  try {
    // 검색 파라미터 준비
    const searchParams = {};
    if (searchQuery.value && searchType.value) {
      searchParams.searchType = searchType.value;
      searchParams.searchKeyword = searchQuery.value;
    }

    let response;

    // SUPER_ADMIN인 경우 모든 이벤트를 가져오는 API 호출
    if (isSuperAdmin) {
      // 현재 선택된 프로젝트 ID가 있으면 파라미터로 전달
      const projectId = currentProjectId.value || null;

      if (projectId) {
        response = await getAllEvents(currentPage.value, itemsPerPage.value, projectId, searchParams);
      } else {
        response = await getAllEvents(currentPage.value, itemsPerPage.value, null, searchParams);
      }
    } else {
      // 일반 사용자는 프로젝트별 이벤트 목록 요청
      response = await getEventsPaginated(currentProjectId.value, currentPage.value, itemsPerPage.value, searchParams);
    }

    // API 응답 구조 처리: { success: true, data: { content: [...], totalPages, totalElements, availableSearchTypes: [...] } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 검색 타입 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        searchTypes.value = responseData.availableSearchTypes;
      }

      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || (responseData.content?.length || 0);
        totalPages.value = Math.ceil(total / itemsPerPage) || 1;
      }

      totalElements.value = responseData.totalElements || 0;

      // 이벤트 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        events.value = responseData.content;

        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (events.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else {
        console.warn('이벤트 데이터가 없거나 배열이 아닙니다.');
        events.value = [];
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      events.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
    }

    // 최종 페이지네이션 상태 로깅
  } catch (err) {
    console.error('이벤트 목록 로드 실패:', err);
    error.value = err.message || '이벤트 리스트를 불러오는 중 오류가 발생했습니다.';
    events.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  searchType.value = '';
  currentPage.value = 0;
  loadEvents();
};

// 프로젝트 변경 이벤트 핸들러
const handleProjectChange = () => {
  // 프로젝트 변경 시 검색 초기화
  resetSearch();
};

// 컴포넌트 마운트 시 이벤트 목록 로드 및 이벤트 리스너 등록
onMounted(() => {
  loadEvents();

  // 프로젝트 변경 이벤트 리스너 등록
  window.addEventListener('project-changed', handleProjectChange);
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('project-changed', handleProjectChange);
});

const navigateToCreateEvent = () => {
  router.push({ name: 'event-create' });
};

// 검색 처리 (디바운스 적용)
let searchTimeout;
const handleSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    // 검색어가 비어있을 때는 아무 동작도 하지 않음
    // 사용자가 직접 검색 버튼을 클릭해야 데이터가 로드됨
  }, 300);
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['startDate', 'endDate', 'createDate', 'registrationDate'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchEvents = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동

  // 날짜 관련 검색 타입인 경우 날짜 형식 변환
  if (searchType.value && isDateSearchType(searchType.value) && searchQuery.value) {
    // 원본 검색어 저장
    const originalQuery = searchQuery.value;
    // 변환된 날짜 형식으로 검색어 업데이트
    const formattedDate = formatDateForSearch(originalQuery);

    if (formattedDate !== originalQuery) {
      searchQuery.value = formattedDate;
    }
  }

  loadEvents(); // 서버에 검색 요청
};

// 페이지 이동 함수
const goToPage = (page) => {
  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    console.warn(`페이지 범위 초과: ${page}, 최대 페이지: ${maxPage}`);
    return;
  }

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;
  loadEvents();

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// 페이지 변경 시 상단으로 스크롤
watch(currentPage, () => {
  window.scrollTo(0, 0);
});

// 정렬 또는 페이지당 항목 수 변경 시 데이터 다시 로드
watch(itemsPerPage, () => {
  // 페이지당 항목 수 변경 시 첫 페이지로 이동
  currentPage.value = 0;
  loadEvents();
});

// 날짜 포맷팅
const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return new Intl.DateTimeFormat('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const viewEvent = (id) => router.push({ name: 'event-view', params: { eventId: id } });
const editEvent = (id) => router.push({ name: 'event-edit', params: { eventId: id } });

// 이벤트 복사 함수
const copyEvent = async (eventId) => {
  if (!eventId) {
    alert('유효하지 않은 이벤트 ID입니다.');
    return;
  }

  if (confirm('이 이벤트를 복사하시겠습니까?')) {
    try {
      // events.js에서 제공하는 copyEvent API 함수 호출
      await copyEventApi(eventId);
      alert('이벤트가 성공적으로 복사되었습니다.');
      // 이벤트 목록 새로고침
      await loadEvents();
    } catch (error) {
      console.error('이벤트 복사 중 오류 발생:', error);
      // 오류 메시지 처리
      alert(error.message || '이벤트 복사 중 오류가 발생했습니다.');
    }
  }
};

const removeEvent = async (id) => {
  if (!confirm('정말 삭제하시겠습니까?')) return;
  try {
    await deleteEvent(id);
    await loadEvents();
  }
  catch(err) {
    alert(err.message || '삭제 중 오류가 발생했습니다.');
  }
};

// 이벤트 타입 포맷팅
const formatEventType = (type) => {
  const typeMap = {
    'SCHEDULED': '예정',
    'ONGOING': '진행',
    'FINISHED': '종료',
    'CANCELED' : '취소'
  };
  return typeMap[type] || type;
};
</script>

<style scoped>
.events-management {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #333;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

/* 테이블 스타일 */
.events-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.events-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  font-weight: 600;
  color: #333;
}

.events-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.events-table tr:hover {
  background-color: #f9f9f9;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

.edit-btn {
  background-color: #fff8e1;
  color: #ffa000;
}

.edit-btn:hover {
  background-color: #ffecb3;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

/* 로딩 및 에러 메시지 스타일 */
.loading-message,
.no-data-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin: 20px 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

/* 디버깅 정보 스타일 */
.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

/* 반응형 스타일 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }

  .filter-group {
    width: 100%;
  }

  .events-table th,
  .events-table td {
    padding: 8px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}
/* 페이지당 항목 수 선택 UI 스타일 */
.events-management .items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.events-management .items-per-page-buttons {
  display: flex;
  margin-left: 10px;
}

.events-management .item-count-btn {
  padding: 6px 10px;
  margin: 0 4px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.events-management .item-count-btn:hover {
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.events-management .item-count-btn.active {
  background-color: #4CAF50 !important;
  color: white !important;
  border-color: #45a049 !important;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
</style>
