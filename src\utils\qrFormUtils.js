import * as XLSX from 'xlsx';

// ==================== 날짜 포맷팅 유틸리티 ====================

/**
 * 서버에서 받은 날짜를 입력 필드용 형식으로 변환
 * @param {string} dateString - 서버 날짜 문자열 (YYYY-MM-DD HH:MM:SS)
 * @returns {string} - 입력 필드용 날짜 문자열 (YYYY-MM-DDTHH:MM)
 */
export const formatDateForInput = (dateString) => {
  if (!dateString) return '';

  // 서버에서 받은 'YYYY-MM-DD HH:MM:SS' 형식을 'YYYY-MM-DDTHH:MM' 형식으로 변환
  if (typeof dateString === 'string' && dateString.includes(' ')) {
    // 'YYYY-MM-DD HH:MM:SS' -> 'YYYY-MM-DDTHH:MM'
    const [datePart, timePart] = dateString.split(' ');
    const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
    return `${datePart}T${timeWithoutSeconds}`;
  }

  // 다른 형식의 날짜인 경우 기존 방식 사용
  try {
    const date = new Date(dateString);
    return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM 형식
  } catch (e) {
    console.error('날짜 변환 오류:', e);
    return '';
  }
};

/**
 * 입력 필드 날짜를 서버용 형식으로 변환
 * @param {string} dateString - 입력 필드 날짜 문자열 (YYYY-MM-DDTHH:MM)
 * @returns {string|null} - 서버용 날짜 문자열 (YYYY-MM-DD HH:MM:SS)
 */
export const formatDateForServer = (dateString) => {
  if (!dateString) return null;

  const date = new Date(dateString);

  // YYYY-MM-DD HH:MM:SS 형식으로 변환
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// ==================== QR 코드 관련 유틸리티 ====================

/**
 * QR 코드 타입을 한글로 포맷팅
 * @param {string} type - QR 코드 타입
 * @returns {string} - 한글 타입명
 */
export const formatQrType = (type) => {
  const typeMap = {
    'URL': 'URL',
    'TEXT': '텍스트',
    'VCARD': '연락처',
    'EMAIL': '이메일',
    'SNS': 'SNS',
    'WIFI': 'Wi-Fi',
    'GEO': '위치',
    'LANDING_PAGE': '랜딩페이지',
    'EVENT_ATTENDANCE': '이벤트'
  };
  return typeMap[type] || type;
};

/**
 * QR 코드 상태를 한글로 포맷팅
 * @param {string} status - QR 코드 상태
 * @returns {string} - 한글 상태명
 */
export const formatQrStatus = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'INACTIVE': '비활성',
    'EXPIRED': '만료됨',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

/**
 * QR 코드 스캔율 계산
 * @param {string} errorCorrectionLevel - 오류 복원 수준 (L, M, Q, H)
 * @param {boolean} hasLogo - 로고 포함 여부
 * @param {number} logoSize - 로고 크기 (퍼센트)
 * @returns {number} - 스캔율 (퍼센트)
 */
export const calculateScanReliability = (errorCorrectionLevel, hasLogo = false, logoSize = 0) => {
  // 기본 스캔율 (오류 복원 수준에 따라 다름)
  let reliability;

  // 오류 복원 수준에 따른 기본 스캔율 설정
  switch (errorCorrectionLevel) {
    case 'L': // Low (7%)
      reliability = 85;
      break;
    case 'M': // Medium (15%)
      reliability = 90;
      break;
    case 'Q': // Quartile (25%)
      reliability = 95;
      break;
    case 'H': // High (30%)
      reliability = 98;
      break;
    default:
      reliability = 90; // 기본값
  }

  // 로고가 있는 경우 스캔율 감소
  if (hasLogo && logoSize > 0) {
    // 로고 크기에 따른 스캔율 감소 (로고가 클수록 더 많이 감소)
    const logoReduction = Math.min(logoSize * 0.3, 15); // 최대 15% 감소
    reliability -= logoReduction;
  }

  // 최소 70%, 최대 99%로 제한
  return Math.max(70, Math.min(99, Math.round(reliability)));
};

// ==================== A4 캔버스 관련 유틸리티 ====================

// A4 용지 실제 크기 (mm 단위)
export const A4_WIDTH_MM = 210;
export const A4_HEIGHT_MM = 297;

/**
 * A4 캔버스 위치 제약 함수
 * @param {number} x - X 좌표
 * @param {number} y - Y 좌표
 * @param {number} width - 너비
 * @param {number} height - 높이
 * @returns {object} - 제약된 좌표 {x, y}
 */
export const constrainA4CanvasPosition = (x, y, width, height) => {
  const parentContainer = document.querySelector('[data-a4-canvas-parent-container]');
  if (!parentContainer) return { x, y };

  // 부모 컨테이너의 실제 내부 크기 계산 (패딩 제외)
  const parentStyle = window.getComputedStyle(parentContainer);
  const parentRect = parentContainer.getBoundingClientRect();
  
  const paddingLeft = parseFloat(parentStyle.paddingLeft) || 0;
  const paddingTop = parseFloat(parentStyle.paddingTop) || 0;
  const paddingRight = parseFloat(parentStyle.paddingRight) || 0;
  const paddingBottom = parseFloat(parentStyle.paddingBottom) || 0;
  
  const maxX = parentRect.width - paddingLeft - paddingRight - width;
  const maxY = parentRect.height - paddingTop - paddingBottom - height;
  
  return {
    x: Math.max(0, Math.min(x, maxX)),
    y: Math.max(0, Math.min(y, maxY))
  };
};

/**
 * 픽셀을 밀리미터로 변환
 * @param {number} pixel - 픽셀 값
 * @param {boolean} isWidth - 너비 여부 (true: 너비, false: 높이)
 * @param {HTMLElement} containerElement - A4 캔버스 컨테이너 요소
 * @returns {number} - 밀리미터 값
 */
export const pixelToMm = (pixel, isWidth = true, containerElement = null) => {
  const container = containerElement || document.querySelector('[data-a4-canvas-container]');
  if (!container) return 0;

  const containerRect = container.getBoundingClientRect();
  const containerSize = isWidth ? containerRect.width : containerRect.height;
  const a4Size = isWidth ? A4_WIDTH_MM : A4_HEIGHT_MM;

  return (pixel / containerSize) * a4Size;
};

/**
 * 밀리미터를 픽셀로 변환
 * @param {number} mm - 밀리미터 값
 * @param {boolean} isWidth - 너비 여부 (true: 너비, false: 높이)
 * @param {HTMLElement} containerElement - A4 캔버스 컨테이너 요소
 * @returns {number} - 픽셀 값
 */
export const mmToPixel = (mm, isWidth = true, containerElement = null) => {
  const container = containerElement || document.querySelector('[data-a4-canvas-container]');
  if (!container) return 0;

  const containerRect = container.getBoundingClientRect();
  const containerSize = isWidth ? containerRect.width : containerRect.height;
  const a4Size = isWidth ? A4_WIDTH_MM : A4_HEIGHT_MM;

  return (mm / a4Size) * containerSize;
};

// ==================== 엑셀 처리 유틸리티 ====================

/**
 * 엑셀 파일 파싱
 * @param {File} excelFile - 엑셀 파일 객체
 * @returns {Promise<Array>} - 파싱된 데이터 배열
 */
export const parseExcelFile = (excelFile) => {
  return new Promise((resolve, reject) => {
    if (!excelFile) {
      reject('엑셀 파일이 선택되지 않았습니다.');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        
        // 첫 번째 시트 사용
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 배열로 변환
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // 헤더와 데이터 확인
        if (jsonData.length < 2) {
          reject('엑셀 파일에 데이터가 충분하지 않습니다.');
          return;
        }
        
        // 헤더 행에서 필요한 컬럼 인덱스 찾기
        const headerRow = jsonData[0];
        const qrTypeColumnIndex = headerRow.findIndex(header => 
          header && header.toString().toLowerCase().includes('타입')
        );
        const targetContentColumnIndex = headerRow.findIndex(header => 
          header && header.toString().toLowerCase().includes('콘텐츠')
        );
        
        if (qrTypeColumnIndex === -1 || targetContentColumnIndex === -1) {
          reject('엑셀 파일에 필수 컬럼(타입, 콘텐츠)이 없습니다.');
          return;
        }
        
        // 데이터 행 파싱 (최대 100개)
        const dataRows = jsonData.slice(1).slice(0, 100);
        const parsedRows = dataRows.map((row, index) => {
          return {
            originalRowNumber: index + 2, // 엑셀 행 번호 (1부터 시작, 헤더가 1행이므로 2부터)
            qrType: row[qrTypeColumnIndex] || '',
            targetContent: row[targetContentColumnIndex] || ''
          };
        });
        
        // 유효한 데이터가 있는 행만 필터링
        const validRows = parsedRows.filter(row => row.qrType && row.targetContent);
        
        if (validRows.length === 0) {
          reject('엑셀 파일에 유효한 데이터가 없습니다.');
          return;
        }
        
        resolve(validRows);
      } catch (error) {
        console.error('엑셀 파일 파싱 오류:', error);
        reject(`엑셀 파일 파싱 중 오류가 발생했습니다: ${error.message || '알 수 없는 오류'}`);
      }
    };
    
    reader.onerror = (error) => {
      console.error('파일 읽기 오류:', error);
      reject('파일을 읽는 중 오류가 발생했습니다.');
    };
    
    reader.readAsBinaryString(excelFile);
  });
};

// ==================== UI 유틸리티 ====================

/**
 * 화면 아래로 스크롤 (배치 결과 영역으로)
 */
export const scrollToBottom = () => {
  setTimeout(() => {
    // 배치 결과 영역 요소 찾기
    const batchResultsElement = document.querySelector('[data-qr-batch-results]');
    if (batchResultsElement) {
      batchResultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
      // 배치 결과 영역이 없으면 페이지 하단으로 스크롤
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }
  }, 100); // 약간의 지연을 두어 DOM 업데이트 후 스크롤
};

// ==================== 유효성 검사 유틸리티 ====================

/**
 * QR 코드 폼 데이터 유효성 검사
 * @param {object} formData - 폼 데이터 객체
 * @param {boolean} isEditMode - 수정 모드 여부
 * @returns {object} - 검사 결과 {isValid: boolean, errors: string[]}
 */
export const validateQrFormData = (formData, isEditMode = false) => {
  const errors = [];

  // 필수 필드 검사
  if (!formData.qrName?.trim()) {
    errors.push('QR 코드 이름은 필수입니다.');
  }

  if (!formData.qrType?.trim()) {
    errors.push('QR 코드 타입은 필수입니다.');
  }

  if (!formData.status?.trim()) {
    errors.push('상태는 필수입니다.');
  }

  // 타겟 콘텐츠 검사 (특정 타입에서만)
  if (formData.qrType && formData.qrType !== 'EVENT_ATTENDANCE' && !formData.targetContent?.trim()) {
    errors.push('타겟 콘텐츠는 필수입니다.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 텍스트 자르기 (말줄임표 추가)
 * @param {string} text - 원본 텍스트
 * @param {number} maxLength - 최대 길이 (기본값: 30)
 * @returns {string} - 자른 텍스트
 */
export const truncateText = (text, maxLength = 30) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};
