<template>
  <!-- QR 코드 미리보기 섹션 -->
  <div class="qr-code-preview">
    <h3>QR 코드 미리보기</h3>
    <div v-if="isEditMode" class="edit-mode-notice">
      <strong>수정 모드에서는 QR 코드 미리보기만 가능합니다.</strong>
    </div>
    <!-- 클라이언트에서 생성한 QR 코드 미리보기 -->
    <div ref="qrCodePreviewContainer" class="qr-preview-container">
      <img
      v-if="qrImageUrl && !imageLoadError"
      :src="qrImageUrl"
      alt="QR Code Image"
      class="qr-image"
      @error="handleImageError"
    />
    </div>
    <div v-if="!hasTargetContent" class="image-placeholder">
      타겟 콘텐츠를 입력하면 QR 코드가 보입니다.<br>(샘플 이미지이며 생성 요청시 실제 QR코드가 생성됩니다.)
    </div>

    <!-- QR 코드 버전 및 크기 정보 (수정 모드에서는 표시하지 않음) -->
    <div v-if="hasTargetContent && !isEditMode" class="qr-version-info">
      <!-- <div class="version-label">
        <span>QR 코드 버전:</span>
        <span class="version-value">{{ qrVersion > 0 ? qrVersion : '자동' }}</span>
        <span class="size-info">(250x250 px)</span>
      </div> -->
      <div class="version-note">
        위의 QR코드는 샘플이며 등록시 디자인을 반영한 실제 QR코드가 생성됩니다.
      </div>
    </div>

    <!-- QR 코드 스캔율 표시 (수정 모드에서는 표시하지 않음) -->
    <div v-if="hasTargetContent && !isEditMode" class="scan-reliability-container">
      <div class="scan-reliability-label">
        <span>스캔율:</span>
        <span :class="scanReliabilityClass">{{ scanReliability }}%</span>
      </div>
      <div class="scan-reliability-progress-container">
        <div class="scan-reliability-progress" :style="{ width: `${scanReliability}%`, backgroundColor: scanReliabilityColor }"></div>
      </div>
      <div class="scan-reliability-note">
        {{ scanReliabilityMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import QRCodeStyling from 'qr-code-styling';

// Props 정의
const props = defineProps({
  isEditMode: {
    type: Boolean,
    default: false
  },
  qrImageUrl: {
    type: String,
    default: ''
  },
  hasTargetContent: {
    type: Boolean,
    default: false
  },
  targetContent: {
    type: String,
    default: ''
  },
  qrType: {
    type: String,
    default: ''
  },
  qrColor: {
    type: String,
    default: '#000000'
  },
  qrBgColor: {
    type: String,
    default: '#FFFFFF'
  },
  qrEyeColor: {
    type: String,
    default: '#000000'
  },
  qrEyeStyle: {
    type: Number,
    default: 0
  },
  qrDotsStyle: {
    type: Number,
    default: 0
  },
  qrErrorCorrectionLevel: {
    type: String,
    default: 'M'
  },
  logoPreview: {
    type: String,
    default: ''
  },
  logoSize: {
    type: Number,
    default: 40
  },
  useA4Canvas: {
    type: Boolean,
    default: false
  },
  isDesignUpdateOnly: {
    type: Boolean,
    default: false
  },
  excelSampleData: {
    type: Object,
    default: () => ({
      isActive: false,
      qrType: '',
      targetContent: ''
    })
  },
  selectedEventId: {
    type: String,
    default: ''
  },
  initialTargetContent: {
    type: String,
    default: ''
  },
  qrVersion: {
    type: Number,
    default: 0
  }
});

// Emits 정의
const emit = defineEmits([
  'qr-generated',
  'qr-download',
  'image-error',
  'scan-reliability-calculated'
]);

// 로컬 상태
const qrCodePreviewContainer = ref(null);
const qrCodeInstance = ref(null);
const imageLoadError = ref(false);
const isQrCodeGenerated = ref(false);
const scanReliability = ref(85);
const qrVersion = ref(0);

// 유틸리티 함수들
const getFrontendDomain = () => {
  return window.location.origin;
};

const calculateQrVersion = (content, errorCorrectionLevel) => {
  // QR 코드 버전 계산 로직 (간단한 버전)
  const contentLength = content.length;
  if (contentLength <= 25) return 1;
  if (contentLength <= 47) return 2;
  if (contentLength <= 77) return 3;
  if (contentLength <= 114) return 4;
  if (contentLength <= 154) return 5;
  return 6; // 더 긴 내용은 6버전 이상
};

// QR 코드 스캔율 관련 computed 속성
const scanReliabilityClass = computed(() => {
  const reliability = scanReliability.value;
  if (reliability >= 90) return 'reliability-high';
  if (reliability >= 75) return 'reliability-medium';
  if (reliability >= 40) return 'reliability-low';
  return 'reliability-critical';
});

const scanReliabilityColor = computed(() => {
   const reliability = scanReliability.value;
  if (reliability >= 90) return '#4CAF50'; // 녹색
  if (reliability >= 75) return '#FFC107'; // 노란색
  if (reliability >= 40) return '#F44336'; // 빨간색
  return '#9C27B0'; // 보라색 (스캔 불가능 수준)
});

const scanReliabilityMessage = computed(() => {
   const reliability = scanReliability.value;
  if (reliability >= 90) return '스캔 신뢰도가 높습니다. 대부분의 기기에서 잘 인식됩니다.';
  if (reliability >= 75) return '스캔 신뢰도가 보통입니다. 일부 기기에서 인식이 어려울 수 있습니다.';
  if (reliability >= 40) return '스캔 신뢰도가 낮습니다. 많은 기기에서 인식이 어려울 수 있습니다.';
  return '스캔이 거의 불가능합니다. 모듈과 배경색의 대비를 높여주세요.';
});

// QR 코드 미리보기 생성
const generateClientSideQrCode = async () => {
  if (!qrCodePreviewContainer.value) {
    return;
  }

  // 엑셀 샘플 데이터가 있는 경우 사용하거나, 이벤트 타입일 때 full URL 사용, 기타 타입은 targetContent 사용
  let contentToUse = '';
  let qrTypeToUse = props.qrType;

  // 엑셀 샘플 데이터가 활성화된 경우 (엑셀 등록 후)
  if (props.excelSampleData.isActive && props.excelSampleData.targetContent) {
    contentToUse = props.excelSampleData.targetContent;
    qrTypeToUse = props.excelSampleData.qrType;
  }
  // 엑셀 샘플 데이터가 없는 일반 경우
  else if (props.qrType === 'EVENT_ATTENDANCE') {
    if (!props.selectedEventId) {
      // 이벤트 미선택 시 미리보기 제거
      if (qrCodeInstance.value) {
        while (qrCodePreviewContainer.value.firstChild) {
          qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
        }
        qrCodeInstance.value = null;
      }
      return;
    }
    contentToUse = `${getFrontendDomain()}/event/${props.selectedEventId}`;
  } else {
    if (!props.targetContent) {
      if (qrCodeInstance.value) {
        while (qrCodePreviewContainer.value.firstChild) {
          qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
        }
        qrCodeInstance.value = null;
      }
      return;
    }
    contentToUse = (props.isEditMode && props.isDesignUpdateOnly && props.initialTargetContent)
      ? props.initialTargetContent
      : props.targetContent;
  }

  // 오류 복원 수준에 따라 QR 코드 버전 계산
  const calculatedVersion = calculateQrVersion(contentToUse, props.qrErrorCorrectionLevel);
  qrVersion.value = calculatedVersion; // 계산된 버전 적용

  // 엑셀 샘플 데이터가 사용되는 경우, 타입 정보 출력
  if (props.excelSampleData.isActive) {
  }

  const qrSize = 150; // QR 코드 크기

  // QR 코드 스타일링 라이브러리용 옵션 구성
  const baseDesignOptions = {
    width: qrSize,
    height: qrSize,
    data: contentToUse,
    margin: 0,
    dotsOptions: {
      color: props.qrColor,
      type: props.qrDotsStyle === 0 ? 'square' : 'dots'
    },
    backgroundOptions: {
      color: props.qrBgColor,
    },
    cornersSquareOptions: {
      color: props.qrEyeColor,
      type: props.qrEyeStyle === 0 ? 'square' : 'dot',
    },
    cornersDotOptions: {
      color: props.qrEyeColor,
      type: props.qrEyeStyle === 0 ? undefined : 'dot'
    },
    qrOptions: {
      errorCorrectionLevel: props.qrErrorCorrectionLevel
    }
  };

  // 로고가 있는 경우 이미지 옵션 추가
  if (props.logoPreview) {
    baseDesignOptions.imageOptions = {
      hideBackgroundDots: true,
      imageSize: props.logoSize / 100, // 퍼센트를 0-1 사이 값으로 변환
      margin: 0
    };
    baseDesignOptions.image = props.logoPreview;
  }

  try {
    if (!qrCodeInstance.value) {
      // 컨테이너 비우기
      while (qrCodePreviewContainer.value.firstChild) {
        qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
      }
      qrCodeInstance.value = new QRCodeStyling(baseDesignOptions);
      await qrCodeInstance.value.append(qrCodePreviewContainer.value);
    } else {
      await qrCodeInstance.value.update(baseDesignOptions);
    }

    // QR 코드 생성/업데이트 후 스캔율 계산
    scanReliability.value = calculateScanReliability();
    isQrCodeGenerated.value = true;

    // 부모 컴포넌트에 QR 코드 생성 완료 알림
    emit('qr-generated', {
      qrCodeInstance: qrCodeInstance.value,
      scanReliability: scanReliability.value
    });

    // 스캔율 계산 결과 전달
    emit('scan-reliability-calculated', scanReliability.value);

  } catch (error) {
    console.error("QR 코드 생성/업데이트 실패:", error);
    qrCodePreviewContainer.value.innerHTML = '<p style="color: red; text-align: center; padding: 10px;">QR 코드 미리보기 생성 중 오류가 발생했습니다.</p>';
    qrCodeInstance.value = null;
  }
};

// 디바운스된 QR 코드 생성 함수
const generateQrCodeDebounced = (() => {
  let timeoutId;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      generateClientSideQrCode();
    }, 300);
  };
})();

// QR 코드 스캔율 계산
const calculateScanReliability = () => {
  let reliability = 100;

  // 타겟 콘텐츠 길이에 따른 감점
  const contentLength = props.targetContent?.length || 0;
  if (contentLength > 100) reliability -= 15;
  else if (contentLength > 50) reliability -= 10;
  else if (contentLength > 25) reliability -= 5;

  // 오류 복원 수준에 따른 보정
  switch (props.qrErrorCorrectionLevel) {
    case 'L': reliability -= 10; break;
    case 'M': reliability -= 5; break;
    case 'Q': reliability += 0; break;
    case 'H': reliability += 5; break;
  }

  // 로고 크기에 따른 감점
  if (props.logoPreview) {
    if (props.logoSize > 30) reliability -= 15;
    else if (props.logoSize > 20) reliability -= 10;
    else if (props.logoSize > 10) reliability -= 5;
  }

  // 색상 대비에 따른 감점 (간단한 계산)
  if (props.qrColor === props.qrBgColor) reliability -= 50;
  
  return Math.max(30, Math.min(100, reliability));
};

// QR 코드 다운로드
const downloadQrCode = async () => {
  if (!qrCodeInstance.value) {
    alert('QR 코드가 생성되지 않았습니다.');
    return;
  }

  try {
    // PNG 형식으로 다운로드
    await qrCodeInstance.value.download({
      name: 'qr-code',
      extension: 'png'
    });

    emit('qr-download', 'png');
  } catch (error) {
    console.error('QR 코드 다운로드 실패:', error);
    alert('QR 코드 다운로드 중 오류가 발생했습니다.');
  }
};

// 이미지 에러 처리
const handleImageError = (event) => {
  imageLoadError.value = true;
  // no-image 이미지로 대체
  event.target.src = '/src/assets/image/no-image.png';
  emit('image-error', event);
};

// Props 변경 감지하여 QR 코드 재생성
watch([
  () => props.targetContent,
  () => props.qrColor,
  () => props.qrBgColor,
  () => props.qrEyeColor,
  () => props.qrEyeStyle,
  () => props.qrDotsStyle,
  () => props.qrErrorCorrectionLevel,
  () => props.logoPreview,
  () => props.logoSize,
  () => props.excelSampleData
], () => {
  if (props.hasTargetContent) {
    generateQrCodeDebounced();
  }
}, { deep: true });

// 컴포넌트 마운트 시 초기 QR 코드 생성
onMounted(() => {
  if (props.hasTargetContent) {
    nextTick(() => {
      generateClientSideQrCode();
    });
  }
});
</script>

<style scoped>
/* 스타일은 부모 컴포넌트에서 상속받아 사용 */
</style>
