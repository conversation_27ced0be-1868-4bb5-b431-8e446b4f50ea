<template>
  <div class="public-event-page">
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>이벤트를 불러오는 중...</p>
    </div>

    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <h2>오류가 발생했습니다</h2>
      <p>{{ error }}</p>
    </div>

    <div v-else-if="eventData" class="event-content">
      <!-- 등록 성공 화면 -->
      <div v-if="registrationSuccess" class="registration-success">
        <div class="success-icon">✅</div>
        <p class="success-message">{{ getCompletionMessage() }}</p>

        <!-- QR 코드 표시 -->
        <div v-if="qrCodeUrl" class="qr-code-container" data-qr-code-container>
          <h2 class="event-title">{{ eventData.eventName }}</h2>
          
          <!-- 이벤트 이미지 표시 -->
          <div v-if="eventData.eventImagePath" class="event-image-wrapper">
            <img :src="eventImageUrl" alt="이벤트 이미지" class="event-image" @error="handleImageError" />
          </div>
          
          <div class="qr-code-content">
            <div class="qr-code-wrapper">
              <img :src="qrCodeUrl" alt="QR 코드" class="qr-code-image" data-qr-code-image @error="handleImageError" />
            </div>
            
            <div class="event-details">
              <div class="event-detail-item">
                <span class="detail-label">설명:</span>
                <span class="detail-value">{{ eventData.description || '설명 없음' }}</span>
              </div>
              
              <div class="event-detail-item">
                <span class="detail-label">장소:</span>
                <span class="detail-value">{{ eventData.location || '장소 정보 없음' }}</span>
              </div>
            </div>
          </div>
          
          <p class="qr-code-save-text" data-qr-code-save-text>이 QR 코드를 저장하거나 캡처하여 참가자 확인에 활용할 수 있습니다. 저장해주세요.</p>
          <button @click="downloadQrCode" class="download-btn" data-download-btn>다운로드</button>
        </div>

        <button class="back-btn" @click="resetForm">다시 등록하기</button>
      </div>

      <!-- 이벤트 정보 및 등록 폼 -->
      <div v-else>
        <h1>이벤트 이름 : {{ eventData.eventName }}</h1>
        <h2 v-if="eventData.teamName">소속 팀 이름(코드) : {{ eventData.teamName }}({{ eventData.teamCode }})</h2>
        <div v-if="eventData.eventImagePath" class="public-event-image">
          <img :src="eventImageUrl" alt="이벤트 이미지" style="max-width:100%; margin:16px 0;" @error="handleImageError" />
        </div>
        <p><strong>설명:</strong> {{ eventData.description || '설명 없음' }}</p>
        <p><strong>시작:</strong> {{ formatDate(eventData.startDate) }}</p>
        <p><strong>종료:</strong> {{ formatDate(eventData.endDate) }}</p>
        <p><strong>장소:</strong> {{ eventData.location || '장소 정보 없음' }}</p>
        <p><strong>참가제한 인원:</strong> {{ eventData.participantLimit ? eventData.participantLimit : '제한 없음' }}</p>
        <!-- <p><strong>상태:</strong> {{ formatEventType(eventData.status) }}</p> -->

        <div v-if="formData" class="pre-registration-form">
          <h2>사전등록 폼: {{ formData.formName }}</h2>
          <p v-if="formData.description"><strong>설명:</strong> {{ formData.description }}</p>
          <!-- 모든 필드 -->
          <div v-for="field in formData.fields" :key="field.fieldId" class="field-preview">
            <label :for="field.fieldName">{{ field.fieldLabel }}<span v-if="field.required" class="required">*</span></label>
            <template v-if="field.fieldType === 'NAME'">
              <input type="text" :id="field.fieldName" v-model="submissionData[field.fieldName]" :placeholder="field.helpText" />
            </template>
            <template v-if="field.fieldType === 'TEL'">
              <input type="text" :id="field.fieldName" v-model="submissionData[field.fieldName]" :placeholder="field.helpText" />
            </template>
            <template v-if="field.fieldType === 'EMAIL'">
              <input type="text" :id="field.fieldName" v-model="submissionData[field.fieldName]" :placeholder="field.helpText" />
            </template>
            <template v-if="field.fieldType === 'TEXT'">
              <input type="text" :id="field.fieldName" v-model="submissionData[field.fieldName]" :placeholder="field.helpText" />
            </template>
            <template v-else-if="field.fieldType === 'NUMBER'">
              <input type="number" :id="field.fieldName" v-model="submissionData[field.fieldName]" :placeholder="field.helpText" />
            </template>
            <template v-else-if="field.fieldType === 'DATE'">
              <input type="date" :id="field.fieldName" v-model="submissionData[field.fieldName]" style="background-color: #ffffff;"/>
            </template>
            <template v-else-if="field.fieldType === 'TIME'">
              <input type="time" :id="field.fieldName" v-model="submissionData[field.fieldName]" style="background-color: #ffffff;"/>
            </template>
            <template v-else-if="field.fieldType === 'SELECT'">
              <select :id="field.fieldName" v-model="submissionData[field.fieldName]" style="background-color: #ffffff;">
                <option value="" disabled selected>선택해주세요</option>
                <option v-for="opt in field.options" :key="opt" :value="opt">{{ opt }}</option>
              </select>
            </template>
          </div>

          <!-- 개인정보 수집·이용 동의 체크박스 -->
          <div v-if="formData.requireConsent" class="privacy-consent">
            <label class="checkbox-label">
              <input type="checkbox" v-model="privacyConsent" />
              <span class="required">*</span> 개인정보 수집·이용 동의 (필수)
            </label>
            <div class="privacy-text">
              {{ formData.privacyPolicyAgreementText || '개인정보 수집·이용에 동의합니다.' }}
            </div>
          </div>

          <!-- 오류 메시지 표시 영역 -->
          <div v-if="validationError" class="validation-error">
            {{ validationError }}
          </div>

          <button class="submit-btn" @click="submitRegistration">제출</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import publicApiClient from '@/api/publicApi';
import html2canvas from 'html2canvas';
import { useImageError } from '@/composables/useImageError';

const route = useRoute();
const eventData = ref(null);
const formData = ref(null);
const submissionData = reactive({});
// 이름, 연락처, 이메일은 submissionData에 포함되어 있음
const privacyConsent = ref(false);
const validationError = ref(null);
const isLoading = ref(true);
const error = ref(null);
const qrCodeUrl = ref(null);
const registrationSuccess = ref(false);

// 이미지 에러 핸들링
const { handleImageError } = useImageError();

// 백엔드 서버 기본 URL에서 `/api/way` 제거하여 호스트만 사용
const baseUrl = import.meta.env.VITE_API_BASE_URL.replace(/\/api\/way$/, '');
// 전체 이미지 URL
const eventImageUrl = computed(() => {
  if (!eventData.value || !eventData.value.eventImagePath) return '';

  // 이미지 경로가 이미 전체 URL인 경우 그대로 사용
  if (eventData.value.eventImagePath.startsWith('http')) {
    return eventData.value.eventImagePath;
  }

  // 상대 경로인 경우 baseUrl과 결합
  return `${baseUrl}${eventData.value.eventImagePath}`;
});

// 공개 이벤트 데이터 로드
const loadEvent = async (id) => {
  isLoading.value = true;
  error.value = null;
  try {
    const res = await publicApiClient.get(`/public/event/${id}`);
    if (res.data && res.data.success) {
      eventData.value = res.data.data;
      // backend에 포함된 preRegistrationForm 데이터 사용
      if (eventData.value.preRegistrationForm) {
        formData.value = eventData.value.preRegistrationForm;

        // 기본 필드 표시 여부 초기화 (백엔드에서 제공한 값만 사용)
        formData.value.showBasicFields = formData.value.showBasicFields === true;

        // 각 기본 필드 표시 여부 초기화 (백엔드에서 제공한 값만 사용)
        formData.value.showNameField = formData.value.showNameField === true;
        formData.value.showContactField = formData.value.showContactField === true;
        formData.value.showEmailField = formData.value.showEmailField === true;

        // 각 기본 필드 필수 여부 초기화 (백엔드에서 제공한 값만 사용)
        formData.value.nameRequired = formData.value.nameRequired === true;
        formData.value.contactRequired = formData.value.contactRequired === true;
        formData.value.emailRequired = formData.value.emailRequired === true;

        // options 문자열을 배열로 변환 및 required 플래그 설정
        formData.value.fields = formData.value.fields.map(field => {
          let opts;
          try { opts = JSON.parse(field.options); } catch { opts = []; }
          return { ...field, options: opts, required: field.isRequiredYn === 'Y' };
        });

        // 모든 필드를 submissionData에 추가 (제출 시 NAME, TEL, EMAIL 타입은 최상위로 이동할 것임)
        formData.value.fields.forEach(field => {
          submissionData[field.fieldName] = '';
        });

      }
    } else {
      error.value = res.data?.error?.message || '이벤트를 불러오는 데 실패했습니다.';
    }
  } catch (err) {
    console.error('이벤트 로드 오류:', err);
    // 서버에서 반환하는 에러 메시지만 표시
    if (err.response) {
      // 서버 응답이 있는 경우
      error.value = err.response.data?.error?.message || '서버에서 오류가 발생했습니다.';
    } else {
      // 서버 응답이 없는 경우 (네트워크 오류 등)
      error.value = '서버에 연결할 수 없습니다. 잠시 후 다시 시도해주세요.';
    }
  } finally {
    isLoading.value = false;
  }
};

const addBodyClass = () => document.body.classList.add('event-page-body');
const removeBodyClass = () => document.body.classList.remove('event-page-body');

const submitRegistration = async () => {
  // 유효성 검사 시작
  validationError.value = null;

  // 1. 필수 필드 검사
  const requiredFieldsMissing = [];

  // 모든 필드의 필수 항목 검사
  // 폼에서 필수 필드 검사
  if (formData.value && formData.value.fields) {
    formData.value.fields.forEach(field => {
      if (field.required) {
        const value = submissionData[field.fieldName];
        if (!value || (typeof value === 'string' && !value.trim())) {
          // 중복 방지를 위해 이미 추가된 필드인지 확인
          if (!requiredFieldsMissing.includes(field.fieldLabel)) {
            requiredFieldsMissing.push(field.fieldLabel);
          }
        }
      }
    });
  }

  // 개인정보 동의 검사
  if (formData.value && formData.value.requireConsent && !privacyConsent.value) {
    requiredFieldsMissing.push('개인정보 수집·이용 동의');
  }

  // 필수 필드가 비어있으면 오류 메시지 표시
  if (requiredFieldsMissing.length > 0) {
    validationError.value = `다음 필수 항목을 입력해주세요: ${requiredFieldsMissing.join(', ')}`;
    return; // 제출 중단
  }

  // 2. 이메일 형식 검사
  if (formData.value && formData.value.fields) {
    // 중복 방지를 위해 Set을 사용하여 고유한 이메일 필드만 검사
    const emailFields = [...new Set(formData.value.fields.filter(field => field.fieldType === 'EMAIL'))];

    for (const emailField of emailFields) {
      const emailValue = submissionData[emailField.fieldName];
      if (emailValue && typeof emailValue === 'string' && emailValue.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailValue)) {
          validationError.value = `${emailField.fieldLabel} 형식이 올바르지 않습니다.`;
          return; // 제출 중단
        }
      }
    }
  }

  // 유효성 검사 통과, 서버에 제출
  const eventId = route.params.id;

  // 페이로드 생성 - NAME, TEL, EMAIL 타입은 최상위로 이동
  const payload = {
    formId: eventData.value.preRegistrationFormId || null,
    submissionData: { ...submissionData } // 복사본 생성
  };

  // teamId가 존재하면 payload에 추가
  if (eventData.value && eventData.value.teamId) {
    payload.teamId = eventData.value.teamId;
  }

  // NAME, TEL, EMAIL 타입의 필드를 최상위 JSON에 추가
  if (formData.value && formData.value.fields) {
    // 각 타입별로 처음 발견된 필드만 처리하도록 플래그 사용
    let nameProcessed = false;
    let telProcessed = false;
    let emailProcessed = false;

    formData.value.fields.forEach(field => {
      if (field.fieldType === 'NAME' && !nameProcessed) {
        payload.attendeeName = submissionData[field.fieldName] || '';
        // submissionData에서 제거
        delete payload.submissionData[field.fieldName];
        nameProcessed = true;
      } else if (field.fieldType === 'TEL' && !telProcessed) {
        payload.attendeeContact = submissionData[field.fieldName] || '';
        // submissionData에서 제거
        delete payload.submissionData[field.fieldName];
        telProcessed = true;
      } else if (field.fieldType === 'EMAIL' && !emailProcessed) {
        payload.attendeeEmail = submissionData[field.fieldName] || '';
        // submissionData에서 제거
        delete payload.submissionData[field.fieldName];
        emailProcessed = true;
      }
    });
  }

  try {
    const response = await publicApiClient.post(`/public/event/${eventId}/attendees`, payload, { headers: { 'Content-Type': 'application/json' } });

    // 응답에서 qrCodeUrl 확인
    if (response.data && response.data.qrCodeUrl) {
      qrCodeUrl.value = response.data.qrCodeUrl;
    }

    // 등록 성공 상태로 변경
    registrationSuccess.value = true;

    // 사전등록 폼에 설정된 완료 메시지 사용 (alert 대신 화면에 표시)
    // alert(getCompletionMessage());
    // 페이지 새로고침 대신 성공 화면 표시
    // window.location.reload();

  } catch (err) {
    console.error('등록 오류:', err);
    // 서버에서 반환하는 에러 메시지만 표시
    if (err.response) {
      // 서버 응답이 있는 경우
      alert(err.response.data?.error?.message || '서버에서 오류가 발생했습니다.');
    } else {
      // 서버 응답이 없는 경우 (네트워크 오류 등)
      alert('서버에 연결할 수 없습니다. 잠시 후 다시 시도해주세요.');
    }
  }
};



// 날짜 형식화 함수 - 널값이면 빈 문자열 반환
const formatDate = (dateString) => {
  if (!dateString) return '';

  // 유효한 날짜인지 확인
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return ''; // 유효하지 않은 날짜인 경우 빈 문자열 반환

  // 1970년 1월 1일이면 기본값으로 간주하고 빈 문자열 반환
  if (date.getFullYear() === 1970 && date.getMonth() === 0 && date.getDate() === 1) {
    return '';
  }

  return date.toLocaleString();
};

// 완료 메시지 가져오기 - 설정된 메시지가 없으면 기본 메시지 반환
const getCompletionMessage = () => {
  const defaultMessage = '등록이 완료되었습니다.';

  // formData가 없는 경우
  if (!formData.value) return defaultMessage;

  // completionMessage가 없거나 빈 문자열인 경우
  if (!formData.value.completionMessage || formData.value.completionMessage.trim() === '') {
    return defaultMessage;
  }

  return formData.value.completionMessage;
};

// SVG를 PNG로 변환하는 헬퍼 함수
const convertSvgToPng = (svgElement, width = 300, height = 300) => {
  return new Promise((resolve, reject) => {
    try {
      // SVG를 XML 문자열로 변환
      const serializer = new XMLSerializer();
      const svgStr = serializer.serializeToString(svgElement);

      // Canvas 생성
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = width;
      canvas.height = height;

      // 배경을 흰색으로 설정
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // Image 객체 생성
      const img = new Image();
      img.onload = () => {
        // SVG 이미지를 Canvas에 그리기
        ctx.drawImage(img, 0, 0, width, height);

        // Canvas를 PNG 데이터 URL로 변환
        const pngDataUrl = canvas.toDataURL('image/png');
        resolve(pngDataUrl);
      };

      img.onerror = () => {
        reject(new Error('SVG 이미지 로드 실패'));
      };

      // SVG 데이터를 base64로 인코딩하여 이미지 소스로 설정
      const svgBlob = new Blob([svgStr], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      img.src = url;

      // 메모리 정리를 위해 타이머 설정
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);

    } catch (error) {
      reject(error);
    }
  });
};

// QR 코드 이미지를 PNG로 변환하는 함수
const ensureQrCodeIsPng = async (qrImageElement) => {
  if (!qrImageElement) return null;

  // 이미지가 SVG인지 확인
  const isSvg = qrImageElement.tagName === 'svg' ||
                qrImageElement.src?.includes('.svg') ||
                qrImageElement.src?.includes('svg+xml') ||
                qrImageElement.src?.startsWith('data:image/svg+xml');

  if (isSvg) {
    // SVG인 경우 PNG로 변환
    const width = qrImageElement.width || qrImageElement.offsetWidth || 180;
    const height = qrImageElement.height || qrImageElement.offsetHeight || 180;

    if (qrImageElement.tagName === 'svg') {
      // SVG 요소인 경우
      return await convertSvgToPng(qrImageElement, width, height);
    } else {
      // SVG 이미지 소스인 경우
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = async () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // 배경을 흰색으로 설정
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(0, 0, width, height);

            // 이미지를 Canvas에 그리기 (가운데 정렬)
            const imgWidth = img.naturalWidth || img.width;
            const imgHeight = img.naturalHeight || img.height;

            // 비율을 유지하면서 캔버스에 맞게 크기 조정
            const scale = Math.min(width / imgWidth, height / imgHeight);
            const scaledWidth = imgWidth * scale;
            const scaledHeight = imgHeight * scale;
            const x = (width - scaledWidth) / 2;
            const y = (height - scaledHeight) / 2;

            ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

            // Canvas를 PNG 데이터 URL로 변환
            const pngDataUrl = canvas.toDataURL('image/png', 1.0);
            resolve(pngDataUrl);
          } catch (error) {
            reject(error);
          }
        };

        img.onerror = () => reject(new Error('이미지 로드 실패'));

        // CORS 문제 해결을 위한 시도
        if (qrImageElement.src.startsWith('data:')) {
          img.src = qrImageElement.src;
        } else {
          // 외부 이미지인 경우 프록시를 통해 로드하거나 직접 시도
          img.src = qrImageElement.src;
        }
      });
    }
  } else {
    // 이미 PNG/JPG 등인 경우 그대로 반환
    return qrImageElement.src;
  }
};

// QR 코드 컨테이너를 스타일이 적용된 이미지로 다운로드하는 함수
const downloadQrCode = async () => {
  if (!qrCodeUrl.value) {
    alert('다운로드할 QR 코드 이미지가 없습니다.');
    return;
  }

  try {
    // QR 코드 컨테이너 요소 가져오기
    const qrContainer = document.querySelector('[data-qr-code-container]');
    if (!qrContainer) {
      throw new Error('QR 코드 컨테이너를 찾을 수 없습니다.');
    }

    // QR 코드 이미지 요소 찾기
    const qrImageElement = qrContainer.querySelector('[data-qr-code-image]');
    let tempImageElement = null;

    // QR 코드 이미지를 PNG로 변환 (SVG인 경우)
    if (qrImageElement) {
      const pngDataUrl = await ensureQrCodeIsPng(qrImageElement);
      if (pngDataUrl && pngDataUrl !== qrImageElement.src) {
        // 원본 이미지를 숨기고 임시 PNG 이미지 생성
        const originalDisplay = qrImageElement.style.display;
        qrImageElement.style.display = 'none';

        // 임시 이미지 요소 생성
        tempImageElement = document.createElement('img');
        tempImageElement.src = pngDataUrl;
        tempImageElement.className = qrImageElement.className;
        tempImageElement.style.cssText = qrImageElement.style.cssText;
        tempImageElement.style.display = 'block';

        // 원본 이미지 다음에 임시 이미지 삽입
        qrImageElement.parentNode.insertBefore(tempImageElement, qrImageElement.nextSibling);

        // 이미지 로드 완료 대기
        await new Promise((resolve) => {
          if (tempImageElement.complete) {
            resolve();
          } else {
            tempImageElement.onload = resolve;
          }
        });
      }
    }

    // 다운로드 버튼과 안내 텍스트 요소 찾기
    const downloadBtn = qrContainer.querySelector('[data-download-btn]');
    const saveText = qrContainer.querySelector('[data-qr-code-save-text]');

    // 요소들을 일시적으로 숨기기 (다운로드 시 제외하기 위해)
    const hideElements = [];
    if (downloadBtn) {
      hideElements.push({ element: downloadBtn, display: downloadBtn.style.display });
      downloadBtn.style.display = 'none';
    }
    if (saveText) {
      hideElements.push({ element: saveText, display: saveText.style.display });
      saveText.style.display = 'none';
    }

    // html2canvas를 사용하여 컨테이너를 이미지로 캡처
    const canvas = await html2canvas(qrContainer, {
      backgroundColor: '#ffffff',
      scale: 2, // 고해상도로 캡처하여 품질 향상
      useCORS: true, // 외부 이미지 리소스 허용
      logging: false,
      allowTaint: true,
      width: qrContainer.offsetWidth,
      height: qrContainer.offsetHeight,
      foreignObjectRendering: false, // iOS Safari 호환성 개선
      imageTimeout: 15000, // 이미지 로드 타임아웃 증가
      removeContainer: true, // 임시 컨테이너 자동 제거
      ignoreElements: (element) => {
        // 원본 SVG 요소는 무시 (이미 PNG로 변환된 이미지만 캡처)
        return element.tagName === 'svg' && element.classList.contains('original-svg');
      }
    });

    // 숨겼던 요소들 복원
    hideElements.forEach(item => {
      item.element.style.display = item.display || '';
    });

    // 임시 이미지 제거 및 원본 이미지 복원
    if (tempImageElement) {
      tempImageElement.remove();
      if (qrImageElement) {
        qrImageElement.style.display = '';
      }
    }

    // 캔버스를 데이터 URL로 변환
    const dataUrl = canvas.toDataURL('image/png');

    // 데이터 URL을 Blob으로 변환
    const response = await fetch(dataUrl);
    const blob = await response.blob();

    // 다운로드 링크 생성 및 클릭
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 다운로드 파일명 설정 (이벤트 이름이 있으면 이벤트 이름 사용)
    const eventName = eventData.value?.eventName || 'event';
    const downloadFileName = `${eventName.replace(/[^a-zA-Z0-9가-힣]/g, '_')}_qr.png`;
    link.setAttribute('download', downloadFileName);

    document.body.appendChild(link);
    link.click();

    // 링크 제거
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

  } catch (error) {
    console.error('QR 코드 다운로드 실패:', error);
    alert(`QR 코드 다운로드 중 오류가 발생했습니다: ${error.message}`);

    // 오류 발생 시 기존 방식으로 다운로드 시도 (QR 코드만)
    try {
      const response = await fetch(qrCodeUrl.value);
      if (!response.ok) {
        throw new Error(`이미지 다운로드 실패: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 이벤트 이름을 파일명에 포함 (오류 시 간단히 qrcode.png로 설정)
      const eventName = eventData.value?.eventName || 'qrcode';
      const simplifiedName = eventName.replace(/[^a-zA-Z0-9가-힣]/g, '_');
      link.setAttribute('download', `${simplifiedName}_qr.png`);

      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

    } catch (fallbackError) {
      console.error('기존 방식으로도 다운로드 실패:', fallbackError);
    }
  }
};

// 폼 초기화 함수
const resetForm = () => {
  // 등록 성공 상태 초기화
  registrationSuccess.value = false;
  qrCodeUrl.value = null;

  // 입력 데이터 초기화
  Object.keys(submissionData).forEach(key => {
    submissionData[key] = '';
  });

  // 개인정보 동의 초기화
  privacyConsent.value = false;

  // 유효성 오류 초기화
  validationError.value = null;
};

onMounted(() => {
  addBodyClass();
  const id = route.params.id;
  if (id) loadEvent(id);
  else {
    error.value = '이벤트 ID가 유효하지 않습니다.';
    isLoading.value = false;
  }
});

onUnmounted(() => removeBodyClass());
</script>

<style>
body.event-page-body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 전체 페이지에 적용되는 스타일 */
* {
  box-sizing: border-box;
}

/* 사이드바 제거 관련 스타일 */
.public-event-page-container {
  width: 100%;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
}
</style>

<style scoped>
.public-event-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background-color: #f5f5f5;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  box-sizing: border-box;
}
.event-content {
  width: 100%;
  max-width: 600px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 32px;
  margin: 32px 16px;
}
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  padding: 20px;
  text-align: center;
}
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}
.error-container h2 {
  margin-bottom: 10px;
  color: #e74c3c;
}
.error-container p {
  color: #666;
  max-width: 400px;
}
.pre-registration-form {
  margin-top: 32px;
  background-color: #f9f9f9;
  padding: 24px;
  border-radius: 8px;
}
.field-preview,
.attendee-info .form-group {
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
}
.field-preview label,
.attendee-info label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
  color: #333333;
}
.field-preview input,
.field-preview select,
.attendee-info input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cccccc;
  border-radius: 4px;
  font-size: 1rem;
  color: #333333;
}
.submit-btn {
  display: inline-block;
  margin-top: 16px;
  background-color: #007bff;
  color: #ffffff;
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
}
.submit-btn:hover {
  background-color: #0056b3;
}

/* 개인정보 동의 스타일 */
.privacy-consent {
  margin-top: 24px;
  padding: 16px;
  background-color: #f0f0f0;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-weight: 600;
  margin-bottom: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  width: 18px;
  height: 18px;
}

.privacy-text {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  margin-top: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* 필수 필드 표시 */
.required {
  color: #e74c3c;
  margin-left: 4px;
}

/* 유효성 오류 메시지 */
.validation-error {
  margin-top: 16px;
  padding: 12px;
  background-color: #ffebee;
  color: #e74c3c;
  border-radius: 4px;
  border-left: 4px solid #e74c3c;
  font-weight: 500;
}

/* 등록 성공 화면 스타일 */
.registration-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32px 16px;
  background-color: #f0f8ff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.success-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.success-message {
  font-size: 18px;
  color: #333;
  margin: 16px 0 32px;
}

/* QR 코드 스타일 */
.qr-code-container {
  background-color: #fff;
  padding: 24px;
  border-radius: 10px;
  margin: 24px 0;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  text-align: center;
}

.event-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.qr-code-image {
  width: 180px;
  height: 180px; /* SVG → PNG 변환 시 일관된 크기 유지 */
  display: block;
  margin: 0 auto;
  border: 1px solid #eee;
  padding: 8px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  object-fit: contain;
  object-position: center;
}

.qr-code-title {
  text-align: center;
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.qr-code-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.qr-code-save-text {
  text-wrap: wrap;
}

@media (min-width: 768px) {
  .qr-code-content {
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
  }
  
  .qr-code-wrapper {
    margin-right: 24px;
  }
  
  .event-details {
    text-align: left;
    min-width: 220px;
  }
}

.qr-code-wrapper {
  margin-bottom: 16px;
}

.event-details {
  margin-bottom: 16px;
}

.event-detail-item {
  margin-bottom: 12px;
  line-height: 1.5;
}

.detail-label {
  font-weight: 600;
  color: #555;
  margin-right: 6px;
}

.detail-value {
  color: #333;
}

.event-image-wrapper {
  margin: 0 auto 20px;
  max-width: 100%;
  text-align: center;
}

.event-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  object-fit: cover;
}

.download-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.download-btn:hover {
  background-color: #45a049;
  box-shadow: 0 3px 6px rgba(76, 175, 80, 0.3);
  transform: translateY(-1px);
}

.back-btn {
  margin-top: 24px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.back-btn:hover {
  background-color: #e0e0e0;
}
</style>
