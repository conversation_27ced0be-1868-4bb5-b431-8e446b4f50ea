{"name": "way_qr_front", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.11.7", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "chart.js": "^4.4.9", "compressorjs": "^1.2.1", "cropperjs": "^2.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "filepond": "^4.32.7", "html2canvas": "^1.4.1", "html5-qrcode": "^2.3.8", "interactjs": "^1.10.27", "lodash": "^4.17.21", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "qr-code-styling": "^1.9.1", "qrcode": "^1.5.4", "quill": "^2.0.3", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-advanced-cropper": "^2.8.9", "vue-chartjs": "^5.3.2", "vue-color": "^2.8.2", "vue-draggable-next": "^2.2.1", "vue-draggable-resizable-gorkys": "^2.4.8", "vue-filepond": "^7.0.4", "vue-grid-layout": "^2.4.0", "vue-quill-editor": "^3.0.6", "vue-router": "^4.5.0", "vue3-draggable-resizable": "^1.6.5", "vue3-moveable": "^0.28.0", "vue3-tree": "^0.11.5", "xlsx": "^0.18.5"}, "devDependencies": {"@fortawesome/vue-fontawesome": "^3.0.8", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "connect-history-api-fallback": "^2.0.0", "jsdom": "^26.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "vite": "^4.5.0", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1"}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.39.0"}}