<template>
  <div class="menu-item" :class="{ 'hidden-menu': !menuItem.visible, 'sub-menu-item': isSubMenu }">
    <!-- 하위 메뉴 들여쓰기 표시 -->
    <span v-if="isSubMenu" class="sub-spacer"></span>
    
    <!-- 펼치기/접기 버튼 (최상위 메뉴만) -->
    <button
      v-if="!isSubMenu"
      @click="$emit('toggle-node', menuItem)"
      class="toggle-btn"
      :class="{ 'expanded': menuItem.expanded }"
      :title="menuItem.nodes && menuItem.nodes.length > 0 ? '하위 메뉴 펼치기/접기' : '하위 메뉴 드롭 존 표시/숨기기'"
    >
      <span class="arrow">▶</span>
    </button>

    <!-- 메뉴 정보 -->
    <div class="menu-info">
      <span class="menu-label">{{ menuItem.label }}</span>
      <div class="menu-details">
        <span class="route-name">{{ menuItem.routeName }}</span>
        <span class="route-path">{{ menuItem.routePath }}</span>
        <span v-if="menuItem.permissions && menuItem.permissions.length > 0" class="permissions">
          권한: {{ menuItem.permissions.join(', ') }}
        </span>
        <span v-if="menuItem.openInNewTab" class="new-tab">새 탭</span>
      </div>
    </div>

    <!-- 컨트롤 버튼들 -->
    <div class="menu-controls">
      <button
        @click="$emit('toggle-visibility', menuItem)"
        class="control-btn visibility-btn"
        :class="{ 'visible': menuItem.visible, 'hidden': !menuItem.visible }"
        :title="menuItem.visible ? '메뉴 숨기기' : '메뉴 표시'"
      >
        {{ menuItem.visible ? '표시' : '숨김' }}
      </button>

      <button
        @click="$emit('edit-menu', menuItem)"
        class="control-btn edit-btn"
        title="메뉴 수정"
      >
        수정
      </button>
      
      <button
        @click="$emit('delete-menu', menuItem)"
        class="control-btn delete-btn"
        title="메뉴 삭제"
        :disabled="hasChildMenus"
      >
        삭제
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  menuItem: {
    type: Object,
    required: true
  },
  isSubMenu: {
    type: Boolean,
    default: false
  }
});

defineEmits([
  'toggle-node',
  'toggle-visibility',
  'edit-menu',
  'delete-menu'
]);

// 하위 메뉴 존재 여부 확인
const hasChildMenus = computed(() => {
  return props.menuItem && props.menuItem.nodes && props.menuItem.nodes.length > 0;
});
</script>

<style scoped>
.menu-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: white;
  border-bottom: 1px solid #f1f3f5;
  transition: all 0.2s;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.hidden-menu {
  opacity: 0.6;
  background: #fff3cd;
}

.sub-menu-item {
  background: #f8f9fa;
  margin-left: 20px;
  border-left: 3px solid #007bff;
}

.sub-spacer {
  width: 28px;
  display: inline-block;
  border-left: 2px solid #dee2e6;
  margin-right: 8px;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background: #e9ecef;
}

.arrow {
  transition: transform 0.2s;
  font-size: 12px;
  color: #6c757d;
}

.toggle-btn.expanded .arrow {
  transform: rotate(90deg);
}

.menu-info {
  flex: 1;
  margin-right: 10px;
}

.menu-label {
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.menu-details {
  display: flex;
  gap: 10px;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

.route-name {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

.route-path {
  color: #007bff;
  font-family: monospace;
}

.permissions {
  background: #fff3cd;
  padding: 2px 6px;
  border-radius: 3px;
  color: #856404;
}

.new-tab {
  background: #d1ecf1;
  padding: 2px 6px;
  border-radius: 3px;
  color: #0c5460;
}

.menu-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.menu-controls .control-btn {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 50px;
  text-align: center;
}

.visibility-btn.visible {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.visibility-btn.hidden {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.edit-btn {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.edit-btn:hover {
  background: #ffeaa7;
  border-color: #ffdf7e;
}

.delete-btn {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.delete-btn:hover:not(:disabled) {
  background: #f5c6cb;
  border-color: #f1b0b7;
}

.delete-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
  color: #6c757d;
  border-color: #dee2e6;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .menu-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .menu-controls {
    align-self: flex-end;
    width: 100%;
    justify-content: flex-end;
  }

  .menu-controls .control-btn {
    font-size: 11px;
    padding: 4px 8px;
    min-width: 40px;
  }
}
</style>
