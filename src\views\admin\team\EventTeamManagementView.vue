<template>
  <div class="attendees-management">
    <div class="header-section">
      <h1>이벤트 참가 팀 관리</h1>
    </div>

    <div class="filters">
      <div class="filter-group">
        <label for="eventFilter">이벤트 선택:</label>
        <select id="eventFilter" v-model="selectedEventId" @change="onEventChange">
          <option value="">모든 이벤트</option>
          <option v-for="event in events" :key="event.eventId" :value="event.eventId">
            {{ event.eventName }}
          </option>
        </select>
        
        <div class="items-per-page-selector">
          <span>페이지당 항목:</span>
          <div class="items-per-page-buttons">
            <button 
              @click="changeItemsPerPage(10)" 
              :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
              10개
            </button>
            <button 
              @click="changeItemsPerPage(30)" 
              :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
              30개
            </button>
            <button 
              @click="changeItemsPerPage(50)" 
              :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
              50개
            </button>
          </div>
        </div>
      </div>

      <div class="filter-group">
        <label for="searchTypeSelect">검색 유형:</label>
        <select id="searchTypeSelect" v-model="searchType">
          <option v-for="type in availableSearchTypes.length > 0 ? availableSearchTypes : searchTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>

        <label for="searchInput">검색어:</label>
        <input
          id="searchInput"
          v-model="searchKeyword"
          @input="handleSearch"
          @keyup.enter="searchTeams"
          placeholder="검색어 입력"
        />

        <div class="button-group">
          <button @click="searchTeams" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>
    </div>
 
    <div class="attendees-table-container">
      <button @click="goToCreateTeam" class="create-team-btn">팀 생성</button>
      <table class="attendees-table" v-if="teams.length > 0">
        <thead>
          <tr>
            <th>번호</th>
            <th>소속된 이벤트</th>
            <th>팀 이름</th>
            <th>팀 코드</th>
            <th>팀장 이름</th>
            <th>팀 상태</th>
            <th>등록일시</th>
            <th>기능</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(team, index) in paginatedTeams" :key="team.id">
            <td>{{ calculateIndex(index) }}</td>
            <td>{{ team.eventName }}</td>
            <td>{{ team.teamName || '-' }}</td>
            <td>{{ team.teamCode || '-' }}</td>
            <td>{{ team.leaderName || '-' }}</td>
            <td>{{ getTeamStatusText(team.teamStatus) }}</td>
            <td>{{ formatDate(team.createDate) }}</td>
            <td>
              <button class="action-btn view-btn" @click="viewTeam(team)">상세</button>
              <button class="action-btn delete-btn" @click="confirmDelete(team)">삭제</button>
            </td>
          </tr>
        </tbody>
      </table>

      <div v-else-if="isLoading" class="loading-message">
        <div class="loading-spinner"></div>
        <p>팀 정보를 불러오는 중입니다...</p>
      </div>

      <div v-else-if="authStore.user?.roleId !== 'SUPER_ADMIN' && !authStore.currentProject" class="no-data-message">
        <p>프로젝트를 선택해주세요.</p>
      </div>

      <div v-else class="no-data-message">
        <p v-if="selectedEventId">선택한 이벤트에 참가 팀 정보가 없습니다.</p>
        <p v-else>참가 팀 정보가 없습니다.</p>
      </div>
    </div>

    <!-- 페이지네이션 -->
    <div class="pagination" v-if="teams.length > 0">
      <button
        @click="goToPage(0)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &laquo;
      </button>
      <button
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage === 0"
        class="pagination-btn"
      >
        &lt;
      </button>

      <span class="page-info">{{ displayPage }} / {{ totalPages }}</span>

      <button
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage === totalPages - 1"
        class="pagination-btn"
      >
        &gt;
      </button>
      <button
        @click="goToPage(totalPages - 1)"
        :disabled="currentPage === totalPages - 1"
        class="pagination-btn"
      >
        &raquo;
      </button>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content delete-modal" @click.stop>
        <h2>팀 삭제</h2>
        <div class="modal-close" @click="cancelDelete">&times;</div>
        <p>다음 팀 정보를 삭제하시겠습니까?</p>
        <p><strong>{{ selectedTeam?.teamName }}</strong></p>
        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelDelete">취소</button>
          <button class="confirm-delete-btn" @click="deleteTeam">삭제</button>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';
import apiClient from '@/api/index';

const authStore = useAuthStore();
const router = useRouter();
const isLoading = ref(false);
const error = ref(null);
const teams = ref([]);
const events = ref([]);
const selectedEventId = ref('');
const searchKeyword = ref('');
const searchType = ref('teamName'); // 기본 검색 타입
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 기본 검색 유형 목록 (서버에서 제공하지 않을 경우 사용)
const searchTypes = [
  { value: 'teamName', label: '팀명' },
  { value: 'leaderName', label: '팀장명' },
  { value: 'eventName', label: '이벤트명' }
];
const currentPage = ref(0);
const itemsPerPage = ref(10);
const totalElements = ref(0);
const totalPages = ref(1);

const showDeleteModal = ref(false);
const selectedTeam = ref(null);

// 팀 생성 페이지로 이동하는 함수
const goToCreateTeam = () => {
  router.push('/events/teams/create');
};

// 컴포넌트 마운트 시 초기화
onMounted(async () => {
  // 실제 API 호출로 데이터 로드
  loadEvents();
  loadTeams();
});

const onEventChange = () => {
  currentPage.value = 0;
  loadTeams();
};

const changeItemsPerPage = (newSize) => {
  itemsPerPage.value = newSize;
  currentPage.value = 0; // 첫 페이지로 이동
  loadTeams(); // 팀 목록 다시 로드
};

const searchTeams = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동
  loadTeams();
};

const resetSearch = () => {
  searchKeyword.value = '';
  // 서버에서 제공하는 검색 유형이 있으면 첫 번째 값을 사용, 없으면 기본값 사용
  if (availableSearchTypes.value.length > 0) {
    searchType.value = availableSearchTypes.value[0].value;
  } else {
    searchType.value = 'teamName';
  }
  currentPage.value = 0;
  loadTeams();
};

const handleSearch = () => {
  // 검색 디바운스 로직
};

const paginatedTeams = computed(() => {
  return teams.value;
});

const calculateIndex = (index) => {
  return totalElements.value - (currentPage.value * itemsPerPage.value + index);
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const goToPage = (page) => {
  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    return;
  }
  
  currentPage.value = page;
  loadTeams();
  
  // 페이지 변경 후 상단으로 스크롤
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

const displayPage = computed(() => currentPage.value + 1);

// 팀 상태를 한글로 변환하는 함수
const getTeamStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'LOCKED': '잠김',
    'PENDING_APPROVAL': '승인대기'
  };
  return statusMap[status] || status;
};

const viewTeam = (team) => {
  router.push(`/events/teams/${team.teamId}`);
};

const confirmDelete = (team) => {
  selectedTeam.value = team;
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedTeam.value = null;
};

const deleteTeam = async () => {
  if (!selectedTeam.value) return;
  
  try {
    const response = await apiClient.delete(`/teams/${selectedTeam.value.teamId}`);
    
    if (response && response.data && response.data.success) {
      alert('팀이 성공적으로 삭제되었습니다.');
      // 팀 목록 새로고침
      loadTeams();
    } else {
      const errorMessage = response?.data?.error?.message || '팀 삭제에 실패했습니다.';
      alert(errorMessage);
    }
  } catch (err) {
    console.error('팀 삭제 실패:', err);
    const errorMessage = err.response?.data?.error?.message || '팀 삭제 중 오류가 발생했습니다.';
    alert(errorMessage);
  } finally {
    cancelDelete();
  }
};



// API를 통해 팀 목록을 로드하는 함수
const loadTeams = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const params = {
      page: currentPage.value,
      size: itemsPerPage.value,
      sort: 'createDate,desc'
    };
    
    // SUPER_ADMIN이 아닌 경우 프로젝트 ID 추가
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';
    if (!isSuperAdmin) {
      const projectId = authStore.currentProject?.projectId;
      if (projectId) {
        params.projectId = projectId;
      }
    }
    
    // 이벤트 필터가 선택된 경우 eventId 추가
    if (selectedEventId.value) {
      params.eventId = selectedEventId.value;
    }
    
    // 검색어가 있는 경우 검색 파라미터 추가
    if (searchKeyword.value.trim()) {
      params.searchType = searchType.value;
      params.searchKeyword = searchKeyword.value.trim();
    }
    
    const response = await apiClient.get('/teams/list', { params });
    
    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.data && response.data.success && response.data.data) {
      console.log(response.data.data)
      const responseData = response.data.data;
      
      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형 기본값 설정 (searchType이 비어있거나 availableSearchTypes에 없을 때)
        if (availableSearchTypes.value.length > 0 && 
            (!searchType.value || !availableSearchTypes.value.some(type => type.value === searchType.value))) {
          searchType.value = availableSearchTypes.value[0].value;
        }
      }
      
      totalElements.value = responseData.totalElements || 0;
      
      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || 0;
        totalPages.value = Math.ceil(total / itemsPerPage.value) || 1;
      }
      
      // 팀 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        teams.value = responseData.content;
        
        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (teams.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        teams.value = responseData;
        totalElements.value = responseData.length;
        totalPages.value = Math.ceil(responseData.length / itemsPerPage.value) || 1;
      } else {
        console.warn('팀 데이터가 없거나 배열이 아닙니다.');
        teams.value = [];
        totalPages.value = 1;
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      teams.value = [];
      totalElements.value = 0;
      totalPages.value = 1;
    }
  } catch (err) {
    console.error('팀 목록 로드 실패:', err);
    error.value = '팀 목록을 불러오는 데 실패했습니다.';
    teams.value = [];
    totalElements.value = 0;
    totalPages.value = 1;
  } finally {
    isLoading.value = false;
  }
};

// API를 통해 이벤트 목록을 로드하는 함수
const loadEvents = async () => {
  try {
    isLoading.value = true;

    // SUPER_ADMIN인 경우 프로젝트 ID 없이도 이벤트 목록을 로드할 수 있음
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

    // 현재 프로젝트의 이벤트 목록 가져오기
    const projectId = authStore.currentProject?.projectId;

    // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
    if (!isSuperAdmin && !projectId) {
      console.error('프로젝트 ID가 없습니다.');
      events.value = [];
      return;
    }

    // API 호출 파라미터 준비 - 서버 요구사항에 맞게 수정
    const paginationParams = {
      page: 0,
      size: 100, // 충분히 큰 값으로 설정하여 모든 이벤트를 가져옴
      sort: "registrationDate,desc" // 등록일시 기준 내림차순 정렬 (배열이 아닌 문자열로 전송)
    };

    // 이벤트 목록 API 호출 - 서버 요구사항에 맞게 수정
    const response = await apiClient.get(`/events`, {
      params: {
        projectId: projectId,
        page: paginationParams.page,
        size: paginationParams.size,
        sort: paginationParams.sort
      }
    });

    if (response && response.data && response.data.success === true) {
      if (response.data.data && Array.isArray(response.data.data.content)) {
        events.value = response.data.data.content;
      }
      else if (Array.isArray(response.data.data)) {
        events.value = response.data.data;
      } else {
        // Unexpected structure inside response.data.data
        console.error('DEBUG: response.data.success was true, but response.data.data structure is unexpected or content array is missing.', response.data.data);
        console.error('API 응답 형식이 예상과 다릅니다. data.data.content 배열을 찾을 수 없습니다.', response.data);
        events.value = [];
      }
    } else {
      // response.data.success is not true or response.data is invalid
      console.error('DEBUG: response.data.success was not true or response.data was invalid.', response?.data);
      console.error('이벤트 목록을 불러오는 데 실패했습니다.', response?.data?.message || 'API 응답 실패 또는 형식이 올바르지 않음');
      events.value = [];
    }
  } catch (error) {
    console.error('이벤트 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    events.value = [];
  } finally {
    isLoading.value = false;
  }
};

</script>

<style scoped>
.attendees-management {
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

h1 {
  margin: 0;
  color: #333;
}

.create-team-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.create-team-btn:hover {
  background-color: #45a049;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

.create-team-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(76, 175, 80, 0.4);
}



.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 24px;
  background-color: #f0f4f8;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e7ef;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 250px;
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #444;
  font-size: 14px;
}

.filter-group select,
.filter-group input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group select:hover,
.filter-group input:hover {
  border-color: #aaa;
}

.filter-group select {
  background-color: white;
  cursor: pointer;
}

.filter-group .button-group {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.search-btn:hover {
  background-color: #0b7dda;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(33, 150, 243, 0.4);
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
  flex: 1;
  text-align: center;
  box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.reset-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.4);
}

.reset-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(244, 67, 54, 0.4);
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.25);
}

.attendees-table-container {
  overflow-x: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.attendees-table {
  width: 100%;
  border-collapse: collapse;
}

.attendees-table th,
.attendees-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.attendees-table th {
  background-color: #f8f8f8;
  font-weight: 600;
  color: #333;
}

.attendees-table tr:hover {
  background-color: #f9f9f9;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

.loading-message,
.no-data-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #333;
}



.additional-info {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
}

.field-id {
  font-family: monospace;
  font-size: 0.9em;
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  color: #666;
  word-break: break-all;
}

.no-data-message {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 10px;
}

.delete-modal, .attendance-modal {
  max-width: 400px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.cancel-btn {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-delete-btn {
  padding: 8px 16px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-delete-btn:hover {
  background-color: #d32f2f;
}

/* 페이지당 항목 선택 UI 스타일 */
.items-per-page-selector {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.items-per-page-selector span {
  font-size: 14px;
  color: #555;
  margin-right: 10px;
}

.items-per-page-buttons {
  display: flex;
  gap: 5px;
}

.item-count-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s, color 0.2s;
}

.item-count-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.item-count-btn:hover:not(.active) {
  background-color: #f0f0f0;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    padding: 12px;
  }

  .filter-group {
    min-width: auto;
    margin-bottom: 12px;
  }

  .filter-group .button-group {
    flex-direction: column;
    gap: 8px;
  }

  .search-btn,
  .reset-btn {
    width: 100%;
  }

  .attendees-table th,
  .attendees-table td {
    padding: 8px;
    font-size: 13px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
    margin-bottom: 4px;
  }
}
</style>