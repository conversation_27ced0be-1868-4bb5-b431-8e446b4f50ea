<template>
  <div class="attendee-registration">
    <div class="header-section">
      <h1>참가자 등록</h1>
      <div class="header-controls">
        <div class="bulk-registration-toggle">
          <label class="toggle-label">
            <input 
              type="checkbox" 
              v-model="isBulkRegistrationMode" 
              @change="onBulkModeToggle"
            />
            <span class="toggle-slider"></span>
            <span class="toggle-text">대량 등록</span>
          </label>
        </div>
        <button @click="goBack" class="back-btn">목록으로 돌아가기</button>
      </div>
    </div>

    <div class="registration-form-container">
      <!-- 대량 등록 모드 UI -->
      <div v-if="isBulkRegistrationMode" class="bulk-registration-section">
        <div class="bulk-controls">
          <button @click="downloadExcelTemplate" class="download-btn">
            📥 참가자 등록 양식 다운로드
          </button>
          <div class="file-upload-area">
            <input 
              ref="fileInput" 
              type="file" 
              accept=".xlsx,.xls" 
              @change="handleFileUpload" 
              style="display: none;"
            />
            <button @click="$refs.fileInput.click()" class="upload-btn">
              📤 참가자 목록 업로드
            </button>
            <span v-if="uploadedFile" class="file-name">{{ uploadedFile.name }}</span>
          </div>
        </div>
        
        <!-- 업로드된 데이터 미리보기 -->
        <div v-if="parsedAttendees.length > 0" class="preview-section">
          <h3>업로드된 참가자 목록 ({{ parsedAttendees.length }}명)</h3>
          <div class="preview-table">
            <table>
              <thead>
                <tr>
                  <th>이름</th>
                  <th>이메일</th>
                  <th>연락처</th>
                  <th>상태</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(attendee, index) in parsedAttendees.slice(0, 5)" :key="index">
                  <td>{{ attendee.attendeeName }}</td>
                  <td>{{ attendee.attendeeEmail }}</td>
                  <td>{{ attendee.attendeeContact || '-' }}</td>
                  <td>
                    <span v-if="attendee.isValid" class="status-valid">✓</span>
                    <span v-else class="status-invalid">✗ {{ attendee.error }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
            <p v-if="parsedAttendees.length > 5" class="more-data">... 외 {{ parsedAttendees.length - 5 }}명</p>
          </div>
        </div>
        
        <!-- 오류 메시지 -->
        <div v-if="errorMessages.length > 0" class="error-section">
          <h4>오류 목록</h4>
          <ul>
            <li v-for="(error, index) in errorMessages" :key="index" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>
        
        <!-- 진행 상태 -->
        <div v-if="uploadProgress > 0" class="progress-section">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
          </div>
          <p>처리 중... {{ uploadProgress }}%</p>
        </div>
      </div>
      
      <!-- 단일 등록 모드 폼 -->
      <form v-if="!isBulkRegistrationMode" @submit.prevent="submitRegistration" class="registration-form">
        <div class="form-section">
          <h2>기본 정보</h2>
          
          <div class="form-group">
            <label for="attendeeName">이름 *</label>
            <input
              id="attendeeName"
              v-model="formData.attendeeName"
              type="text"
              required
              placeholder="참가자 이름을 입력하세요"
            />
          </div>

          <div class="form-group">
            <label for="attendeeEmail">이메일 *</label>
            <input
              id="attendeeEmail"
              v-model="formData.attendeeEmail"
              type="email"
              required
              placeholder="이메일 주소를 입력하세요"
            />
          </div>

          <div class="form-group">
            <label for="attendeeContact">연락처</label>
            <input
              id="attendeeContact"
              v-model="formData.attendeeContact"
              type="tel"
              placeholder="연락처를 입력하세요"
            />
          </div>
        </div>

        <div class="form-section">
          <h2>이벤트 정보</h2>
          
          <div class="form-group">
            <label for="eventSelect">이벤트 선택 *</label>
            <select
              id="eventSelect"
              v-model="formData.eventId"
              required
              @change="onEventChange"
            >
              <option value="">이벤트를 선택하세요</option>
              <option v-for="event in events" :key="event.eventId" :value="event.eventId">
                {{ event.eventName }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="teamSelect">팀 선택</label>
            <select
              id="teamSelect"
              v-model="formData.teamId"
              :disabled="!formData.eventId"
            >
              <option value="">{{ !formData.eventId ? '먼저 이벤트를 선택하세요' : (isLoadingTeams ? '팀 목록을 불러오는 중...' : (teams.length === 0 ? '이벤트에 생성된 팀이 없습니다' : '팀을 선택하세요 (선택사항)')) }}</option>
              <option v-for="team in teams" :key="team.teamId" :value="team.teamId">
                {{ team.teamName }}
              </option>
            </select>
          </div>
        </div>

        <div class="form-section">
          <h2>참석 정보</h2>
          
          <div class="form-group">
            <label for="attendedConfirmYn">참석 권한 여부 *</label>
            <select
              id="attendedConfirmYn"
              v-model="formData.attendedConfirmYn"
              required
            >
              <option value="Y">가능</option>
              <option value="N">불가능</option>
            </select>
          </div>

          <div class="form-group">
            <label for="attendedYn">실제 참석 상태 *</label>
            <select
              id="attendedYn"
              v-model="formData.attendedYn"
              required
            >
              <option value="PENDING">대기 중</option>
              <option value="Y">참석</option>
              <option value="N">미참석</option>
            </select>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" @click="goBack" class="cancel-btn">취소</button>
          <button type="submit" class="submit-btn" :disabled="isSubmitting">
            {{ isSubmitting ? '등록 중...' : '참가자 등록' }}
          </button>
        </div>
      </form>
      
      <!-- 대량 등록 모드 공통 정보 및 액션 -->
      <div v-if="isBulkRegistrationMode" class="bulk-common-info">
        <div class="form-section">
          <h2>이벤트 정보</h2>
          
          <div class="form-group">
            <label for="eventSelect">이벤트 선택 *</label>
            <select
              id="eventSelect"
              v-model="formData.eventId"
              required
              @change="onEventChange"
            >
              <option value="">이벤트를 선택하세요</option>
              <option v-for="event in events" :key="event.eventId" :value="event.eventId">
                {{ event.eventName }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="teamSelect">팀 선택</label>
            <select
              id="teamSelect"
              v-model="formData.teamId"
              :disabled="!formData.eventId"
            >
              <option value="">{{ !formData.eventId ? '먼저 이벤트를 선택하세요' : (isLoadingTeams ? '팀 목록을 불러오는 중...' : (teams.length === 0 ? '이벤트에 생성된 팀이 없습니다' : '팀을 선택하세요 (선택사항)')) }}</option>
              <option v-for="team in teams" :key="team.teamId" :value="team.teamId">
                {{ team.teamName }}
              </option>
            </select>
          </div>
        </div>

        <div class="form-section">
          <h2>참석 정보</h2>
          
          <div class="form-group">
            <label for="attendedConfirmYn">참석 권한 여부 *</label>
            <select
              id="attendedConfirmYn"
              v-model="formData.attendedConfirmYn"
              required
            >
              <option value="Y">가능</option>
              <option value="N">불가능</option>
            </select>
          </div>

          <div class="form-group">
            <label for="attendedYn">실제 참석 상태 *</label>
            <select
              id="attendedYn"
              v-model="formData.attendedYn"
              required
            >
              <option value="PENDING">대기 중</option>
              <option value="Y">참석</option>
              <option value="N">미참석</option>
            </select>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" @click="goBack" class="cancel-btn">취소</button>
          <button 
            @click="submitBulkRegistration" 
            class="submit-btn" 
            :disabled="isSubmitting || parsedAttendees.length === 0 || !formData.eventId"
          >
            {{ isSubmitting ? '등록 중...' : `${parsedAttendees.length}명 일괄 등록` }}
          </button>
        </div>
      </div>
    </div>

    <!-- 성공 모달 -->
    <div v-if="showSuccessModal" class="modal-overlay" @click="closeSuccessModal">
      <div class="modal-content success-modal" @click.stop>
        <h2>등록 완료</h2>
        <div class="modal-close" @click="closeSuccessModal">&times;</div>
        
        <div class="success-message">
          <div class="success-icon">✓</div>
          <p>참가자가 성공적으로 등록되었습니다.</p>
          <div class="registration-info">
            <p><strong>이름:</strong> {{ formData.attendeeName }}</p>
            <p><strong>이메일:</strong> {{ formData.attendeeEmail }}</p>
            <p v-if="selectedEventName"><strong>이벤트:</strong> {{ selectedEventName }}</p>
          </div>
        </div>

        <div class="modal-actions">
          <button @click="registerAnother" class="register-another-btn">추가 등록</button>
          <button @click="goToList" class="go-to-list-btn">목록으로 이동</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import apiClient from '@/api/index';
import publicApiClient from '@/api/publicApi';
import * as XLSX from 'xlsx';

// 상태 관리
const router = useRouter();
const authStore = useAuthStore();
const isSubmitting = ref(false);
const showSuccessModal = ref(false);
const events = ref([]);
const teams = ref([]);
const isLoadingTeams = ref(false);

// 대량 등록 관련 상태
const isBulkRegistrationMode = ref(false);
const uploadedFile = ref(null);
const parsedAttendees = ref([]);
const uploadProgress = ref(0);
const errorMessages = ref([]);
const fileInput = ref(null);

// 폼 데이터
const formData = ref({
  attendeeName: '',
  attendeeEmail: '',
  attendeeContact: '',
  eventId: '',
  teamId: '',
  attendedConfirmYn: 'Y', // 기본값: 가능
  attendedYn: 'PENDING' // 기본값: 대기 중
});

// 선택된 이벤트 이름
const selectedEventName = computed(() => {
  const selectedEvent = events.value.find(event => event.eventId === formData.value.eventId);
  return selectedEvent ? selectedEvent.eventName : '';
});

// 뒤로 가기
const goBack = () => {
  router.push('/attendees');
};

// 목록으로 이동
const goToList = () => {
  router.push('/attendees');
};

// 이벤트 목록 로드 (EventTeamManagementView.vue에서 개선된 버전)
const loadEvents = async () => {
  try {
    // SUPER_ADMIN인 경우 프로젝트 ID 없이도 이벤트 목록을 로드할 수 있음
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

    // 현재 프로젝트의 이벤트 목록 가져오기
    const projectId = authStore.currentProject?.projectId;

    // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
    if (!isSuperAdmin && !projectId) {
      console.error('프로젝트 ID가 없습니다.');
      events.value = [];
      return;
    }

    // API 호출 파라미터 준비 - 서버 요구사항에 맞게 수정
    const paginationParams = {
      page: 0,
      size: 100, // 충분히 큰 값으로 설정하여 모든 이벤트를 가져옴
      sort: "registrationDate,desc" // 등록일시 기준 내림차순 정렬 (배열이 아닌 문자열로 전송)
    };

    // 이벤트 목록 API 호출 - 서버 요구사항에 맞게 수정
    const response = await apiClient.get(`/events`, {
      params: {
        projectId: projectId,
        page: paginationParams.page,
        size: paginationParams.size,
        sort: paginationParams.sort
      }
    });

    if (response && response.data && response.data.success === true) {
      if (response.data.data && Array.isArray(response.data.data.content)) {
        events.value = response.data.data.content;
      }
      else if (Array.isArray(response.data.data)) {
        events.value = response.data.data;
      } else {
        // Unexpected structure inside response.data.data
        console.error('DEBUG: response.data.success was true, but response.data.data structure is unexpected or content array is missing.', response.data.data);
        console.error('API 응답 형식이 예상과 다릅니다. data.data.content 배열을 찾을 수 없습니다.', response.data);
        events.value = [];
      }
    } else {
      // response.data.success is not true or response.data is invalid
      console.error('DEBUG: response.data.success was not true or response.data was invalid.', response?.data);
      console.error('이벤트 목록을 불러오는 데 실패했습니다.', response?.data?.message || 'API 응답 실패 또는 형식이 올바르지 않음');
      events.value = [];
    }
  } catch (error) {
    console.error('이벤트 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    events.value = [];
    alert('이벤트 목록을 불러오는데 실패했습니다.');
  }
};

// 팀 목록 로드 (EventTeamManagementView.vue에서 개선된 버전)
const loadTeams = async (eventId) => {
  try {
    if (!eventId) {
      teams.value = [];
      isLoadingTeams.value = false;
      return;
    }

    isLoadingTeams.value = true;
    const params = {
      page: 0,
      size: 100, // 충분히 큰 값으로 설정하여 모든 팀을 가져옴
      sort: 'createDate,desc',
      eventId: eventId
    };
    
    // SUPER_ADMIN이 아닌 경우 프로젝트 ID 추가
    const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';
    if (!isSuperAdmin) {
      const projectId = authStore.currentProject?.projectId;
      if (projectId) {
        params.projectId = projectId;
      }
    }
    
    const response = await apiClient.get('/teams/list', { params });
    
    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.data && response.data.success && response.data.data) {
      console.log('팀 목록 응답:', response.data.data);
      const responseData = response.data.data;
      
      // 팀 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        teams.value = responseData.content;
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        teams.value = responseData;
      } else {
        console.warn('팀 데이터가 없거나 배열이 아닙니다.');
        teams.value = [];
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      teams.value = [];
    }
  } catch (error) {
    console.error('팀 목록 로드 실패:', error);
    if (error.response) {
      console.error('서버 응답:', error.response.data);
      console.error('상태 코드:', error.response.status);
    }
    teams.value = [];
  } finally {
    isLoadingTeams.value = false;
  }
};

// 이벤트 변경 시
const onEventChange = () => {
  formData.value.teamId = ''; // 팀 선택 초기화
  loadTeams(formData.value.eventId);
};

// 참가자 등록 제출
const submitRegistration = async () => {
  try {
    isSubmitting.value = true;

    // PublicEventPage.vue와 동일한 API 엔드포인트 사용
    const payload = {
      attendeeName: formData.value.attendeeName,
      attendeeEmail: formData.value.attendeeEmail,
      attendeeContact: formData.value.attendeeContact,
      eventId: formData.value.eventId,
      teamId: formData.value.teamId || null,
      attendedConfirmYn: formData.value.attendedConfirmYn,
      attendedYn: formData.value.attendedYn,
      submissionData: {}, // 빈 객체로 설정
      skipValidation: true // 관리자 페이지에서는 유효성 검사 건너뛰기
    };

    const response = await publicApiClient.post(`/public/event/${formData.value.eventId}/attendees`, payload, { 
      headers: { 'Content-Type': 'application/json' } 
    });

    console.log('등록 API 응답:', response.data);
    
    // 성공 모달 표시
    showSuccessModal.value = true;
    
  } catch (error) {
    console.error('참가자 등록 실패:', error);
    // 서버에서 반환하는 에러 메시지 표시
    if (error.response) {
      alert(error.response.data?.error?.message || '서버에서 오류가 발생했습니다.');
    } else {
      alert('서버에 연결할 수 없습니다. 잠시 후 다시 시도해주세요.');
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 성공 모달 닫기
const closeSuccessModal = () => {
  showSuccessModal.value = false;
};

// 추가 등록
const registerAnother = () => {
  showSuccessModal.value = false;
  // 폼 초기화 (이벤트 선택은 유지)
  const currentEventId = formData.value.eventId;
  const currentTeamId = formData.value.teamId;
  
  formData.value = {
    attendeeName: '',
    attendeeEmail: '',
    attendeeContact: '',
    eventId: currentEventId,
    teamId: currentTeamId,
    attendedConfirmYn: 'Y', // 기본값: 가능
    attendedYn: 'PENDING' // 기본값: 대기 중
  };
};

// 대량 등록 모드 토글
const onBulkModeToggle = () => {
  if (!isBulkRegistrationMode.value) {
    // 대량 등록 모드 해제 시 관련 데이터 초기화
    uploadedFile.value = null;
    parsedAttendees.value = [];
    uploadProgress.value = 0;
    errorMessages.value = [];
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  }
};

// 엑셀 양식 다운로드
const downloadExcelTemplate = () => {
  const templateData = [
    {
      '이름': '홍길동',
      '이메일': '<EMAIL>',
      '연락처': '010-1234-5678'
    },
    {
      '이름': '김철수',
      '이메일': '<EMAIL>',
      '연락처': '010-9876-5432'
    }
  ];
  
  const worksheet = XLSX.utils.json_to_sheet(templateData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, '참가자목록');
  
  // 컬럼 너비 설정
  worksheet['!cols'] = [
    { width: 15 }, // 이름
    { width: 25 }, // 이메일
    { width: 20 }  // 연락처
  ];
  
  const selectedEvent = events.value.find(event => event.eventId === formData.value.eventId);
  const fileName = `참가자_등록_양식_${selectedEvent?.eventName || '이벤트'}_${new Date().toISOString().split('T')[0]}.xlsx`;
  
  XLSX.writeFile(workbook, fileName);
};

// 파일 업로드 처리
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  uploadedFile.value = file;
  parseExcelFile(file);
};

// 엑셀 파일 파싱
const parseExcelFile = (file) => {
  const reader = new FileReader();
  
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      validateAndProcessData(jsonData);
    } catch (error) {
      console.error('엑셀 파일 파싱 오류:', error);
      errorMessages.value = ['엑셀 파일을 읽는 중 오류가 발생했습니다.'];
    }
  };
  
  reader.readAsArrayBuffer(file);
};

// 데이터 유효성 검사 및 처리
const validateAndProcessData = (data) => {
  errorMessages.value = [];
  const validAttendees = [];
  const errors = [];
  
  data.forEach((row, index) => {
    const rowNumber = index + 2; // 엑셀 행 번호 (헤더 제외)
    const attendee = {
      attendeeName: row['이름'] || row['name'] || '',
      attendeeEmail: row['이메일'] || row['email'] || '',
      attendeeContact: row['연락처'] || row['contact'] || row['phone'] || '',
      isValid: true,
      error: ''
    };
    
    // 필수 필드 검증
    if (!attendee.attendeeName.trim()) {
      attendee.isValid = false;
      attendee.error = '이름이 필요합니다';
      errors.push(`${rowNumber}행: 이름이 필요합니다`);
    }
    
    if (!attendee.attendeeEmail.trim()) {
      attendee.isValid = false;
      attendee.error = '이메일이 필요합니다';
      errors.push(`${rowNumber}행: 이메일이 필요합니다`);
    } else {
      // 이메일 형식 검증
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(attendee.attendeeEmail)) {
        attendee.isValid = false;
        attendee.error = '이메일 형식이 올바르지 않습니다';
        errors.push(`${rowNumber}행: 이메일 형식이 올바르지 않습니다`);
      }
    }
    
    validAttendees.push(attendee);
  });
  
  parsedAttendees.value = validAttendees;
  errorMessages.value = errors;
  
  if (errors.length === 0) {
    console.log(`${validAttendees.length}명의 참가자 데이터가 성공적으로 파싱되었습니다.`);
  }
};

// 대량 등록 제출
const submitBulkRegistration = async () => {
  try {
    isSubmitting.value = true;
    uploadProgress.value = 0;
    
    // 유효한 참가자만 필터링
    const validAttendees = parsedAttendees.value.filter(attendee => attendee.isValid);
    
    if (validAttendees.length === 0) {
      alert('등록할 수 있는 유효한 참가자가 없습니다.');
      return;
    }
    
    uploadProgress.value = 30;
    
    if (isBulkRegistrationMode.value) {
      // 대량 등록 모드: 새로운 bulk API 사용
      try {
        // FormData 생성 (multipart/form-data)
        const bulkFormData = new FormData();
        
        // 공통 파라미터 추가
        if (formData.value.teamId) {
          bulkFormData.append('teamId', formData.value.teamId);
        }
        if (formData.value.attendedYn) {
          bulkFormData.append('attendedYn', formData.value.attendedYn);
        }
        if (formData.value.attendedConfirmYn) {
          bulkFormData.append('attendedConfirmYn', formData.value.attendedConfirmYn);
        }
        
        // 업로드된 엑셀 파일 추가
        if (uploadedFile.value) {
          bulkFormData.append('file', uploadedFile.value);
        }
        
        uploadProgress.value = 50;
        
        // 백엔드 bulk API 호출
        const response = await apiClient.post(
          `/events/${formData.value.eventId}/attendees/bulk`,
          bulkFormData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        );
        
        uploadProgress.value = 100;
        
        // 성공 결과 처리
        const result = response.data;
        
        // 206 상태 코드 (부분 성공) 처리
        if (response.status === 206 && result.success && result.data) {
          const data = result.data;
          const successCount = data.successCount || 0;
          const failureCount = data.failureCount || 0;
          const totalCount = data.totalCount || 0;
          
          let resultMessage = `등록 완료!\n전체: ${totalCount}명\n성공: ${successCount}명\n실패: ${failureCount}명`;
          
          // failedRegistrations가 있으면 추가로 표시
          if (data.failedRegistrations) {
            resultMessage += `\n\n실패 상세:\n${data.failedRegistrations}`;
          }
          
          alert(resultMessage);
        } else {
          // 일반 성공 응답 처리
          const successCount = result.successCount || validAttendees.length;
          const failCount = result.failCount || 0;
          
          const resultMessage = `등록 완료!\n성공: ${successCount}명\n실패: ${failCount}명`;
          alert(resultMessage);
        }
        
        // 성공한 경우 데이터 초기화
        uploadedFile.value = null;
        parsedAttendees.value = [];
        errorMessages.value = [];
        if (fileInput.value) {
          fileInput.value.value = '';
        }
        
      } catch (error) {
        console.error('대량 등록 API 실패:', error);
        
        // 백엔드 에러 응답 처리
        let errorMessage = '대량 등록 중 오류가 발생했습니다.';
        
        if (error.response && error.response.data) {
          const responseData = error.response.data;
          // 백엔드 응답 구조: { success: false, error: { code, message, details } }
          if (responseData.success === false && responseData.error && responseData.error.message) {
            errorMessage = responseData.error.message;
          }
        }
        
        alert(errorMessage);
        
        // bulk API 실패 시 개별 등록으로 폴백하지 않고 종료
        uploadProgress.value = 0;
        return;
      }
    } else {
      // 개별 등록 모드: 기존 방식 유지
      await performIndividualRegistration(validAttendees);
    }
    
  } catch (error) {
    console.error('대량 등록 실패:', error);
    alert('대량 등록 중 오류가 발생했습니다.');
  } finally {
    isSubmitting.value = false;
    setTimeout(() => {
      uploadProgress.value = 0;
    }, 2000);
  }
};

// 개별 등록 처리 함수
const performIndividualRegistration = async (validAttendees) => {
  let successCount = 0;
  let failCount = 0;
  const failedAttendees = [];
  
  for (let i = 0; i < validAttendees.length; i++) {
    try {
      const attendee = validAttendees[i];
      const payload = {
        attendeeName: attendee.attendeeName,
        attendeeEmail: attendee.attendeeEmail,
        attendeeContact: attendee.attendeeContact,
        eventId: formData.value.eventId,
        teamId: formData.value.teamId || null,
        attendedConfirmYn: formData.value.attendedConfirmYn,
        attendedYn: formData.value.attendedYn,
        submissionData: {},
        skipValidation: true
      };
      
      await publicApiClient.post(`/public/event/${formData.value.eventId}/attendees`, payload, { 
        headers: { 'Content-Type': 'application/json' } 
      });
      
      successCount++;
      uploadProgress.value = 30 + (70 * (i + 1) / validAttendees.length);
    } catch (error) {
      console.error(`참가자 ${validAttendees[i].attendeeName} 등록 실패:`, error);
      failCount++;
      failedAttendees.push(validAttendees[i]);
    }
  }
  
  uploadProgress.value = 100;
  
  // 결과 알림
  const resultMessage = `등록 완료!\n성공: ${successCount}명\n실패: ${failCount}명`;
  alert(resultMessage);
  
  if (successCount > 0) {
    // 성공한 경우 데이터 초기화
    uploadedFile.value = null;
    parsedAttendees.value = [];
    errorMessages.value = [];
    if (fileInput.value) {
      fileInput.value.value = '';
    }
  }
};

// 컴포넌트 마운트 시
onMounted(() => {
  loadEvents();
});
</script>

<style scoped>
.attendee-registration {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-section h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.bulk-registration-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.toggle-label input[type="checkbox"] {
  position: relative;
  width: 50px;
  height: 24px;
  appearance: none;
  background-color: #ccc;
  border-radius: 12px;
  transition: background-color 0.3s;
  cursor: pointer;
}

.toggle-label input[type="checkbox"]:checked {
  background-color: #1976d2;
}

.toggle-label input[type="checkbox"]:before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s;
}

.toggle-label input[type="checkbox"]:checked:before {
  transform: translateX(26px);
}

.toggle-text {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.back-btn {
  padding: 10px 20px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.back-btn:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.registration-form-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  padding: 32px;
  border: 1px solid #e9ecef;
}

.bulk-registration-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2px dashed #1976d2;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
}

.bulk-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.download-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.download-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.download-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.file-upload-area {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.upload-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
}

.file-name {
  font-size: 14px;
  color: #28a745;
  font-weight: 500;
  padding: 8px 12px;
  background-color: #e8f5e8;
  border-radius: 6px;
  border: 1px solid #c3e6c3;
}

.preview-section {
  margin-bottom: 24px;
}

.preview-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.preview-table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th {
  background-color: #f8f9fa;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.preview-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  color: #333;
}

.preview-table tr:last-child td {
  border-bottom: none;
}

.status-valid {
  color: #28a745;
  font-weight: 600;
}

.status-invalid {
  color: #dc3545;
  font-weight: 600;
}

.more-data {
  padding: 12px 16px;
  margin: 0;
  background-color: #f8f9fa;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

.error-section {
  background-color: #fff5f5;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.error-section h4 {
  margin: 0 0 12px 0;
  color: #dc3545;
  font-size: 16px;
  font-weight: 600;
}

.error-section ul {
  margin: 0;
  padding-left: 20px;
}

.error-item {
  color: #dc3545;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-section {
  margin-bottom: 24px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progress-section p {
  margin: 0;
  text-align: center;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.bulk-common-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
}

.bulk-common-info h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.registration-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 24px;
}

.form-section:last-of-type {
  border-bottom: none;
  padding-bottom: 0;
}

.form-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #1976d2;
  display: inline-block;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s;
  background-color: #fff;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1976d2;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
  transform: translateY(-1px);
}

.form-group input:disabled,
.form-group select:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.bulk-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
}

.cancel-btn {
  padding: 12px 24px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-btn:hover {
  background-color: #5a6268;
  transform: translateY(-1px);
}

.submit-btn,
.bulk-submit-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
}

.submit-btn:hover:not(:disabled),
.bulk-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
}

.submit-btn:disabled,
.bulk-submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 500px;
  width: 90%;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-close {
  position: absolute;
  top: 16px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  line-height: 1;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #333;
}

.success-modal h2 {
  margin: 0 0 24px 0;
  color: #333;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
}

.success-message {
  text-align: center;
  margin-bottom: 32px;
}

.success-icon {
  font-size: 64px;
  color: #4caf50;
  margin-bottom: 20px;
  display: block;
}

.success-message p {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

.registration-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: left;
  border-left: 4px solid #1976d2;
}

.registration-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #495057;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.register-another-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.register-another-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.go-to-list-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.go-to-list-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

@media (max-width: 768px) {
  .attendee-registration {
    padding: 16px;
  }
  
  .header-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: space-between;
  }
  
  .registration-form-container {
    padding: 20px;
  }
  
  .bulk-registration-section {
    padding: 20px;
  }
  
  .bulk-controls {
    flex-direction: column;
  }
  
  .file-upload-area {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-actions,
  .bulk-actions {
    flex-direction: column;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-content {
    padding: 24px;
    margin: 20px;
  }
  
  .preview-table {
    font-size: 12px;
  }
  
  .preview-table th,
  .preview-table td {
    padding: 8px;
  }
}
</style>