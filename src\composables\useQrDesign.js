import { ref, computed, watch, nextTick } from 'vue';
import { calculateScanReliability, constrainA4CanvasPosition, pixelToMm } from '@/utils/qrFormUtils';

export function useQrDesign() {
  // QR 코드 색상 옵션
  const qrColor = ref('#000000'); // QR 코드 색상
  const qrBgColor = ref('#FFFFFF'); // 배경 색상
  const qrEyeColor = ref('#000000'); // 눈 색상
  const qrEyeStyle = ref(0); // 0: 네모, 1: 동그라미
  const qrDotsStyle = ref(0); // 0: 네모, 1: 동그라미

  // QR 코드 오류 복원 수준 (Error Correction Level)
  const qrErrorCorrectionLevel = ref('M'); // 기본값: M (Medium)

  // QR 코드 버전 (1~40, 가능한 경우 자동 계산)
  const qrVersion = ref(0); // 0: 자동 계산

  // QR 코드 스캔율 관련 상태
  const scanReliability = ref(95); // 기본 스캔율 (퍼센트)

  // 로고 관련 상태
  const logoPreview = ref(null); // 로고 이미지 미리보기 URL
  const logoSize = ref(40); // 로고 크기 (기본값: 원본 이미지 크기의 40%)
  const originalLogoSize = ref({ width: 0, height: 0 }); // 원본 로고 이미지 크기
  const logoFile = ref(null); // 업로드된 로고 파일

  // QR 코드 인스턴스 및 컨테이너
  const qrCodeInstance = ref(null);
  const qrCodePreviewContainer = ref(null);

  // A4 캔버스 및 배경 이미지 관련 변수
  const useA4Canvas = ref(false); // A4 용지 박스 사용 여부
  const useBackgroundImage = ref(false); // 배경 이미지 사용 여부

  // A4 캔버스 크기 및 위치
  const a4CanvasX = ref(0);
  const a4CanvasY = ref(0);
  const a4CanvasWidth = ref(793); // 초기 너비 (A4 비율)
  const a4CanvasHeight = ref(1123); // 초기 높이 (A4 비율)

  // px를 cm로 변환하기 위한 computed 속성
  const PX_TO_CM_RATIO = 2.54 / 96; // 96DPI 기준
  const cmA4CanvasWidth = computed(() => (a4CanvasWidth.value * PX_TO_CM_RATIO).toFixed(2));
  const cmA4CanvasHeight = computed(() => (a4CanvasHeight.value * PX_TO_CM_RATIO).toFixed(2));

  // 배경 이미지 관련 변수
  const backgroundInput = ref(null); // 배경 이미지 입력 필드 ref
  const backgroundImageFile = ref(null);
  const backgroundImagePreview = ref(null);
  const backgroundFitMode = ref('fit'); // 기본 맞춤 모드: 'fit' (다른 옵션: 'fill', 'original')

  // QR 코드 드래그 및 리사이즈 관련 변수
  const qrPositionX = ref(50); // A4 캔버스 내에서 QR 코드의 초기 X 위치 (픽셀)
  const qrPositionY = ref(50); // A4 캔버스 내에서 QR 코드의 초기 Y 위치 (픽셀)
  const qrWidth = ref(100); // QR 코드 이미지의 초기 너비 (픽셀)
  const qrHeight = ref(100); // QR 코드 이미지의 초기 높이 (픽셀)

  // 배경 이미지 드래그 및 리사이즈 관련 변수
  const backgroundPositionX = ref(0); // A4 캔버스 내에서 배경 이미지의 초기 X 위치 (픽셀)
  const backgroundPositionY = ref(0); // A4 캔버스 내에서 배경 이미지의 초기 Y 위치 (픽셀)
  const backgroundWidth = ref(300); // 배경 이미지의 초기 너비 (픽셀)
  const backgroundHeight = ref(200); // 배경 이미지의 초기 높이 (픽셀)
  const isQrCodeActive = ref(false);
  const isBackgroundImageActive = ref(false);

  // A4 캔버스 컨테이너 DOM 참조
  const a4CanvasContainer = ref(null);

  // 디자인옵션 객체 초기화 (엑셀 업로드 기능에서 사용하기 위해 명시적으로 초기화)
  const designOptions = ref({
    dotsOptions: {
      color: '#000000',
      type: 'square'
    },
    cornersSquareOptions: {
      color: '#000000',
      type: 'square'
    },
    cornersDotOptions: {
      color: '#000000',
      type: 'square'
    },
    backgroundOptions: {
      color: '#FFFFFF',
    },
    imageOptions: {
      hideBackgroundDots: true,
      imageSize: 0.4,
      margin: 0
    }
  });

  // 현재 QR코드 위치를 mm 단위로 계산
  const mmPositionX = computed(() => {
    return Math.round(pixelToMm(qrPositionX.value, true) * 10) / 10;
  });

  const mmPositionY = computed(() => {
    return Math.round(pixelToMm(qrPositionY.value, false) * 10) / 10;
  });

  const mmWidth = computed(() => {
    return Math.round(pixelToMm(qrWidth.value, true) * 10) / 10;
  });

  const mmHeight = computed(() => {
    return Math.round(pixelToMm(qrHeight.value, false) * 10) / 10;
  });

  // A4 캔버스 mm 단위 크기
  const mmA4CanvasWidth = computed(() => {
    return Math.round(pixelToMm(a4CanvasWidth.value, true) * 10) / 10;
  });

  const mmA4CanvasHeight = computed(() => {
    return Math.round(pixelToMm(a4CanvasHeight.value, false) * 10) / 10;
  });

  // 배경 이미지 mm 단위 위치 및 크기
  const mmBackgroundPositionX = computed(() => {
    return Math.round(pixelToMm(backgroundPositionX.value, true) * 10) / 10;
  });

  const mmBackgroundPositionY = computed(() => {
    return Math.round(pixelToMm(backgroundPositionY.value, false) * 10) / 10;
  });

  const mmBackgroundWidth = computed(() => {
    return Math.round(pixelToMm(backgroundWidth.value, true) * 10) / 10;
  });

  const mmBackgroundHeight = computed(() => {
    return Math.round(pixelToMm(backgroundHeight.value, false) * 10) / 10;
  });

  // 데이터 길이와 오류 복원 수준에 따라 QR 코드 버전 계산
  const calculateQrVersion = (content, errorCorrectionLevel) => {
    if (!content) return 1; // 데이터가 없으면 최소 버전

    const contentLength = content.length;

    // 오류 복원 수준에 따른 버전 계수 조정 계수
    let levelFactor;
    switch (errorCorrectionLevel) {
      case 'L': levelFactor = 1.0; break;  // 기본 버전
      case 'M': levelFactor = 1.2; break;  // L보다 20% 더 크게
      case 'Q': levelFactor = 2.0; break;  // L보다 100% 더 크게 (M과의 차이를 크게 만들기 위해 증가)
      case 'H': levelFactor = 3.0; break;  // L보다 200% 더 크게
      default: levelFactor = 1.2; // 기본값 M
    }

    // 데이터 길이에 따른 기본 버전 계산
    let baseVersion;
    if (contentLength <= 25) {
      baseVersion = 2; // 최소 버전을 2로 설정하여 더 명확한 차이 제공
    } else if (contentLength <= 50) {
      baseVersion = 3;
    } else if (contentLength <= 100) {
      baseVersion = 4;
    } else if (contentLength <= 150) {
      baseVersion = 5;
    } else if (contentLength <= 200) {
      baseVersion = 7;
    } else if (contentLength <= 250) {
      baseVersion = 9;
    } else if (contentLength <= 300) {
      baseVersion = 12;
    } else {
      baseVersion = Math.min(40, Math.ceil(contentLength / 25)); // 최대 버전 40, 더 빠르게 버전 증가
    }

    // 최소 버전을 2로 설정하여 더 명확한 차이 제공
    baseVersion = Math.max(2, baseVersion);

    // 오류 복원 수준을 고려한 최종 버전 계산
    const finalVersion = Math.min(40, Math.ceil(baseVersion * levelFactor));

    return finalVersion;
  };

  // 로고 크기를 계산하는 함수 (원본 이미지 크기 대비 %)
  const calculateLogoSize = () => {
    // 라이브러리는 imageSize를 0~1 사이 값으로 기대함 (QR 코드 크기 대비 비율)

    // 로고가 없으면 0 반환 (라이브러리는 image가 null이면 imageSize 무시)
    if (!logoPreview.value) {
      return 0;
    }

    // 사용자 설정값 (logoSize: 10~100)을 라이브러리 비율 (0.1 ~ 1.0)로 변환
    const userRatio = logoSize.value / 100;

    // 로고가 너무 커지는 것을 방지하기 위한 최대 비율 설정 (예: 0.4 = 40%)
    const maxSizeRatio = 0.4;
    const finalRatio = Math.min(userRatio, maxSizeRatio);

    // 최종 계산된 비율 반환
    return finalRatio;
  };

  // A4 캔버스 드래그 이벤트 핸들러
  const onA4CanvasDrag = (e) => {
    const constrained = constrainA4CanvasPosition(e.x, e.y, a4CanvasWidth.value, a4CanvasHeight.value);
    a4CanvasX.value = constrained.x;
    a4CanvasY.value = constrained.y;
  };

  // A4 캔버스 크기 조절 이벤트 핸들러
  const onA4CanvasResize = (e) => {
    // 부모 컨테이너 크기 확인
    const parentContainer = document.querySelector('[data-a4-canvas-parent-container]');
    if (!parentContainer) return;

    const parentRect = parentContainer.getBoundingClientRect();
    const padding = 40; // 패딩 여유분
    const maxWidth = Math.max(200, parentRect.width - padding);
    const maxHeight = Math.max(200, parentRect.height - padding);

    // 현재 크기와 위치 (독립적으로 조절)
    let newWidth = Math.max(200, Math.min(maxWidth, e.width));
    let newHeight = Math.max(200, Math.min(maxHeight, e.height));
    let newX = e.x;
    let newY = e.y;

    // 최대 크기 제한
    if (newWidth > 1000) newWidth = 1000;
    if (newHeight > 1400) newHeight = 1400;

    // 위치 제약 적용 (부모 컨테이너 내부에 유지)
    const constrained = constrainA4CanvasPosition(newX, newY, newWidth, newHeight);

    // 값 업데이트
    a4CanvasWidth.value = newWidth;
    a4CanvasHeight.value = newHeight;
    a4CanvasX.value = constrained.x;
    a4CanvasY.value = constrained.y;

    // 부모 컨테이너의 최소 높이를 A4 캔버스 크기에 맞게 조정
    nextTick(() => {
      const minHeight = Math.max(600, newHeight + 80); // 여백 80px 추가
      parentContainer.style.minHeight = `${minHeight}px`;
    });
  };

  // 로고 업로드 처리
  const handleLogoUpload = (data) => {
    const { file, preview } = data;
    logoFile.value = file;
    logoPreview.value = preview;

    // 로고 이미지의 원본 크기 계산
    const img = new Image();
    img.onload = () => {
      originalLogoSize.value = { width: img.width, height: img.height };
    };
    img.src = preview;
  };

  // 로고 제거 처리
  const handleLogoRemove = () => {
    logoPreview.value = null;
    logoFile.value = null;
  };

  // 배경 이미지 업로드 처리
  const handleBackgroundUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      backgroundImageFile.value = file;
      const reader = new FileReader();
      reader.onload = (e) => {
        backgroundImagePreview.value = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  // 배경 이미지 제거
  const removeBackgroundImage = () => {
    backgroundImagePreview.value = null;
    backgroundImageFile.value = null;
    backgroundFitMode.value = 'fit';
    if (backgroundInput.value) {
      backgroundInput.value.value = '';
    }
  };

  // 배경 이미지 맞춤 모드 설정
  const setBackgroundFitMode = (mode) => {
    backgroundFitMode.value = mode;
  };

  // A4 캔버스를 고화질 이미지로 생성하는 함수
  const generateA4CanvasImage = async () => {
    if (!a4CanvasContainer.value) {
      console.warn('A4 캔버스 컨테이너를 찾을 수 없습니다.');
      return null;
    }

    try {
      // html2canvas 동적 import
      const html2canvas = (await import('html2canvas')).default;

      // A4 캔버스 영역을 고화질로 캡처
      const canvas = await html2canvas(a4CanvasContainer.value, {
        scale: 3, // 고화질을 위한 스케일 증가 (3배)
        useCORS: true, // CORS 이슈 해결
        allowTaint: true,
        backgroundColor: '#ffffff', // 배경색 설정
        width: a4CanvasContainer.value.offsetWidth,
        height: a4CanvasContainer.value.offsetHeight,
        logging: false // 로깅 비활성화
      });

      // Canvas를 Blob으로 변환
      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          if (blob) {
            // Blob을 File 객체로 변환
            const file = new File([blob], 'a4-canvas-image.png', {
              type: 'image/png',
              lastModified: Date.now()
            });
            resolve(file);
          } else {
            console.warn('Canvas를 Blob으로 변환하는데 실패했습니다.');
            resolve(null);
          }
        }, 'image/png', 0.95); // PNG 형식, 95% 품질
      });
    } catch (error) {
      console.error('A4 캔버스 이미지 생성 중 오류 발생:', error);
      return null;
    }
  };

  // 디자인 옵션 초기화 함수
  const resetDesignOptions = () => {
    qrColor.value = '#000000';
    qrBgColor.value = '#ffffff';
    qrEyeColor.value = '#000000';
    qrDotsStyle.value = 0; // 네모
    qrEyeStyle.value = 0; // 네모
    qrErrorCorrectionLevel.value = 'M'; // 오류 복원 수준 초기화 (중간)

    // 로고 관련 상태 초기화
    logoPreview.value = '';
    logoFile.value = null;
    logoSize.value = 40; // 원본 이미지 크기의 40%로 초기화

    // A4 캔버스 및 배경 이미지 관련 상태 초기화
    useA4Canvas.value = false;
    useBackgroundImage.value = false;
    backgroundImagePreview.value = null;
    backgroundImageFile.value = null;
    backgroundFitMode.value = 'fit'; // 기본 맞춤 모드 초기화
    if (backgroundInput.value) {
      backgroundInput.value.value = ''; // 배경 이미지 파일 입력 필드 초기화
    }

    // QR 코드 위치 및 크기 초기화
    qrPositionX.value = 50;
    qrPositionY.value = 50;
    qrWidth.value = 100;
    qrHeight.value = 100;

    // 배경 이미지 위치 및 크기 초기화
    backgroundPositionX.value = 0;
    backgroundPositionY.value = 0;
    backgroundWidth.value = 300;
    backgroundHeight.value = 200;

    // A4 캔버스 크기 초기화
    a4CanvasWidth.value = 793;
    a4CanvasHeight.value = 1123;
    a4CanvasX.value = 0;
    a4CanvasY.value = 0;
  };

  // A4 캔버스 사용 여부 변경 시 처리
  watch(useBackgroundImage, (newValue) => {
    if (!newValue) {
      // 배경 이미지 비활성화 시 관련 데이터 초기화
      backgroundImagePreview.value = null;
      backgroundImageFile.value = null;
      backgroundFitMode.value = 'fit';
      if (backgroundInput.value) {
        backgroundInput.value.value = '';
      }
    }
  });

  // A4 캔버스 초기 위치 설정 함수
  const initializeA4CanvasPosition = () => {
    nextTick(() => {
      const parentContainer = document.querySelector('[data-a4-canvas-parent-container]');
      if (parentContainer) {
        const parentRect = parentContainer.getBoundingClientRect();
        const padding = 20; // 부모 컨테이너 패딩

        // 부모 컨테이너 내부에서 중앙 정렬
        const availableWidth = parentRect.width - (padding * 2);
        const availableHeight = parentRect.height - (padding * 2);

        a4CanvasX.value = Math.max(0, (availableWidth - a4CanvasWidth.value) / 2);
        a4CanvasY.value = Math.max(0, (availableHeight - a4CanvasHeight.value) / 2);
      }
    });
  };

  // A4 캔버스 사용 여부 변경 감지
  watch(useA4Canvas, (newValue) => {
    if (newValue) {
      // A4 캔버스가 활성화되면 초기 위치 설정
      setTimeout(() => {
        initializeA4CanvasPosition();
      }, 100); // DOM 렌더링 대기
    }
  });



  return {
    // 상태
    qrColor,
    qrBgColor,
    qrEyeColor,
    qrEyeStyle,
    qrDotsStyle,
    qrErrorCorrectionLevel,
    qrVersion,
    scanReliability,
    logoPreview,
    logoSize,
    originalLogoSize,
    logoFile,
    qrCodeInstance,
    qrCodePreviewContainer,
    useA4Canvas,
    useBackgroundImage,
    a4CanvasX,
    a4CanvasY,
    a4CanvasWidth,
    a4CanvasHeight,
    backgroundInput,
    backgroundImageFile,
    backgroundImagePreview,
    backgroundFitMode,
    qrPositionX,
    qrPositionY,
    qrWidth,
    qrHeight,
    backgroundPositionX,
    backgroundPositionY,
    backgroundWidth,
    backgroundHeight,
    isQrCodeActive,
    isBackgroundImageActive,
    a4CanvasContainer,
    designOptions,

    // 계산된 속성
    cmA4CanvasWidth,
    cmA4CanvasHeight,
    mmPositionX,
    mmPositionY,
    mmWidth,
    mmHeight,
    mmA4CanvasWidth,
    mmA4CanvasHeight,
    mmBackgroundPositionX,
    mmBackgroundPositionY,
    mmBackgroundWidth,
    mmBackgroundHeight,

    // 메서드
    calculateQrVersion,
    calculateLogoSize,
    onA4CanvasDrag,
    onA4CanvasResize,
    handleLogoUpload,
    handleLogoRemove,
    handleBackgroundUpload,
    removeBackgroundImage,
    setBackgroundFitMode,
    resetDesignOptions,
    generateA4CanvasImage,
    initializeA4CanvasPosition
  };
}
