<template>
  <div v-if="isOpen" class="notification-list-container" ref="container">
    <div class="notification-list-header">
      <h3>문의 알림</h3>
      <div class="notification-actions">
        <button v-if="hasUnread" class="read-all-btn" @click="markAllAsRead">모두 읽음</button>
        <button class="close-btn" @click="close">닫기</button>
      </div>
    </div>
    
    <div v-if="isLoading" class="notification-loading">
      <div class="loading-spinner"></div>
      <p>알림을 불러오는 중...</p>
    </div>
    
    <div v-else-if="notifications.length === 0" class="empty-notification">
      <p>문의 관련 알림이 없습니다.</p>
    </div>
    
    <div v-else class="notification-list">
      <div
        v-for="notification in notifications"
        :key="notification.notificationId"
        class="notification-item"
        :class="{ 'unread': !notification.read }"
        @click="handleNotificationClick(notification)"
      >
        <div class="notification-content">
          <div class="notification-title">{{ getNotificationTitle(notification) }}</div>
          <div class="notification-body">{{ notification.message }}</div>
          <div class="notification-time">{{ formatDate(notification.createdAt) }}</div>
        </div>
        <div v-if="!notification.read" class="unread-indicator"></div>
      </div>
    </div>
    
    <div v-if="pagination.totalPages > 1" class="notification-pagination">
      <button 
        :disabled="pagination.currentPage === 0" 
        @click="changePage(pagination.currentPage - 1)"
        class="pagination-btn"
      >
        이전
      </button>
      <span class="page-info">{{ pagination.currentPage + 1 }} / {{ pagination.totalPages }}</span>
      <button 
        :disabled="pagination.currentPage === pagination.totalPages - 1" 
        @click="changePage(pagination.currentPage + 1)"
        class="pagination-btn"
      >
        다음
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useNotificationStore } from '@/stores/notificationStore';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

const notificationStore = useNotificationStore();
const router = useRouter();
const container = ref(null);
const isLoading = ref(false);

// 페이지네이션 상태
const pagination = ref({
  currentPage: 0,
  totalPages: 0,
  size: 10
});

// 읽지 않은 알림이 있는지 확인
const hasUnread = computed(() => {
  return notificationStore.unreadCount > 0;
});

// 알림 목록
const notifications = computed(() => {
  return notificationStore.notifications;
});

// 컴포넌트가 마운트될 때 알림 데이터 로드
onMounted(() => {
  if (props.isOpen) {
    loadNotifications();
  }
  
  // 클릭 이벤트 리스너 추가
  document.addEventListener('click', handleOutsideClick);
});

// 컴포넌트가 언마운트될 때 이벤트 리스너 제거
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// isOpen 상태 변경 감시
watch(() => props.isOpen, (newVal) => {
  if (newVal) {
    loadNotifications();
  }
});

// 페이지 변경
const changePage = (page) => {
  pagination.value.currentPage = page;
  loadNotifications();
};

// 알림 데이터 로드
const loadNotifications = async () => {
  isLoading.value = true;
  
  try {
    const params = {
      page: pagination.value.currentPage,
      size: pagination.value.size,
      sort: 'createDate,desc'
    };
    
    // API 호출 및 결과 처리
    await notificationStore.fetchNotificationsAction(params);
    
    // 스토어에서 가져온 데이터를 기반으로 페이지네이션 정보 업데이트
    // API 응답을 스토어에서 처리하도록 수정할 필요가 있습니다.
    if (notificationStore.pagination) {
      pagination.value.totalPages = notificationStore.pagination.totalPages;
      pagination.value.currentPage = notificationStore.pagination.currentPage;
    }
  } catch (error) {
    console.error('알림을 로드하는 중 오류가 발생했습니다:', error);
  }
  
  isLoading.value = false;
};

// 알림 제목 가져오기
const getNotificationTitle = (notification) => {
  // 알림 타입에 따라 타이틀 설정
  switch(notification.notificationType) {
    case 'NEW_INQUIRY_COMMENT':
      return '답변 등록';
    case 'INQUIRY_STATUS_UPDATED':
      return '문의 상태 변경';
    default:
      return '알림';
  }
};

// 알림 클릭 핸들러
const handleNotificationClick = async (notification) => {
  if (!notification.read) {
    try {
      await notificationStore.markNotificationAsReadAction(notification.notificationId);
    } catch (error) {
      console.error('알림 읽음 처리 중 오류가 발생했습니다:', error);
    }
  }
  
  // 알림 타입에 따라 해당 페이지로 이동 기능
  if (notification.inquiryId) {
    // 만약 router가 사용 가능하다면
    router.push(`/inquiries/${notification.inquiryId}`);
  }
};

// 모든 알림 읽음 처리
const markAllAsRead = async () => {
  try {
    await notificationStore.markAllAsReadAction();
  } catch (error) {
    console.error('모든 알림 읽음 처리 중 오류가 발생했습니다:', error);
  }
};

// 외부 클릭 감지하여 알림 목록 닫기
const handleOutsideClick = (event) => {
  if (container.value && !container.value.contains(event.target) && 
      !event.target.classList.contains('notification-bell') && 
      !event.target.closest('.notification-bell')) {
    close();
  }
};

// 닫기 버튼 클릭
const close = () => {
  emit('close');
};

// 날짜 포맷 함수
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const now = new Date();
  const date = new Date(dateString);
  
  const timeDiff = now.getTime() - date.getTime();
  const oneDay = 1000 * 60 * 60 * 24;
  
  // 24시간 이내인 경우 "N시간 전" 형식으로 표시
  if (timeDiff < oneDay) {
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    if (hours < 1) {
      const minutes = Math.floor(timeDiff / (1000 * 60));
      return minutes <= 0 ? '방금 전' : `${minutes}분 전`;
    }
    return `${hours}시간 전`;
  }
  
  // 하루 이상 차이가 나는 경우
  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, '0');
  const dd = String(date.getDate()).padStart(2, '0');
  
  return `${yyyy}-${mm}-${dd}`;
};
</script>

<style scoped>
.notification-list-container {
  position: absolute;
  top: 40px;
  right: 0;
  width: 350px;
  max-height: 500px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.notification-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f9fa;
}

.notification-list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.read-all-btn, .close-btn {
  border: none;
  background: none;
  padding: 5px 8px;
  font-size: 12px;
  color: #555;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.read-all-btn {
  color: #007bff;
}

.read-all-btn:hover, .close-btn:hover {
  background-color: #f0f0f0;
}

.notification-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: #666;
}

.loading-spinner {
  width: 25px;
  height: 25px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-notification {
  display: flex;
  justify-content: center;
  padding: 30px;
  color: #888;
  font-style: italic;
}

.notification-list {
  overflow-y: auto;
  max-height: 350px;
}

.notification-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-item:hover {
  background-color: #f5f5f5;
}

.notification-item.unread {
  background-color: #f0f7ff;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 5px;
  color: #333;
}

.notification-item.unread .notification-title {
  color: #007bff;
}

.notification-body {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #007bff;
  flex-shrink: 0;
}

.notification-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-top: 1px solid #f0f0f0;
  gap: 10px;
}

.pagination-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e0e0e0;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #666;
}
</style>
