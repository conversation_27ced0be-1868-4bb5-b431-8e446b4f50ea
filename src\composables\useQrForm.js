import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { formatDateForInput, formatDateForServer, pixelToMm } from '@/utils/qrFormUtils';

export function useQrForm() {
  const router = useRouter();
  const route = useRoute();
  const authStore = useAuthStore();

  // 기본 상태 관리
  const isLoading = ref(true);
  const error = ref(null);
  const isSubmitting = ref(false);
  const imageLoadError = ref(false);

  // 폼 데이터 초기화
  const formData = ref({
    qrName: '',
    qrType: '',
    targetContent: '',
    status: 'ACTIVE',
    linkedEventId: '',
    description: '',
    validFromDate: '',
    validToDate: '',
    quizInfo: null,
  });

  // QR 코드 설치 위치 관련 상태
  const locationData = ref({
    latitude: '',
    longitude: '',
    address: ''
  });
  const locationImageFile = ref(null);
  const locationImagePreview = ref('');

  // 엑셀 관련 상태
  const excelFile = ref(null);
  const excelSampleQrData = ref({
    isActive: false,
    qrType: '',
    targetContent: ''
  });

  // 모달 상태
  const showQuizSelectModal = ref(false);
  const showTargetLocationMapModal = ref(false);

  // 컴포넌트 참조
  const excelUploadRef = ref(null);
  const qrTypeInputsRef = ref(null);

  // QR 코드 생성 관련 상태
  const isQrCodeGenerated = ref(false);
  const initialTargetContent = ref('');
  const isDesignUpdateOnly = ref(false);



  // 현재 선택된 프로젝트
  const currentProject = computed(() => {
    return authStore.currentProject;
  });

  // 수정 모드 여부
  const isEditMode = computed(() => {
    return !!route.params.qrCodeId;
  });

  // 선택된 이벤트 ID (이벤트 타입 QR 코드용)
  const selectedEventId = computed(() => {
    return qrTypeInputsRef.value?.selectedEventId || '';
  });

  // 이벤트 목록 (이벤트 타입 QR 코드용)
  const events = computed(() => {
    return qrTypeInputsRef.value?.events || [];
  });

  // 프론트엔드 서버 주소 가져오기
  const getFrontendDomain = () => {
    const currentUrl = window.location.href;
    const url = new URL(currentUrl);
    return `${url.protocol}//${url.host}`;
  };

  // 엑셀 파일 변경 핸들러
  const handleExcelFileChange = (file) => {
    excelFile.value = file;
    if (!file) {
      // 파일이 제거된 경우 배치 관련 데이터도 초기화
      qrBatchCreationList.value = [];
      batchGlobalError.value = '';
      batchProgress.value = 0;
      parsedExcelRows.value = [];

      // QR 코드 타입과 타겟 콘텐츠 초기화
      formData.value.qrType = '';
      formData.value.targetContent = '';
    }
  };

  // 엑셀 샘플 데이터 변경 핸들러
  const handleExcelSampleDataChange = (sampleData) => {
    excelSampleQrData.value = sampleData;
  };

  // 랜딩 페이지 변경 핸들러
  const handleLandingPageChange = (landingPageId) => {
    if (qrTypeInputsRef.value) {
      qrTypeInputsRef.value.selectedLandingPageId = landingPageId;
    }

    if (landingPageId) {
      // 프론트엔드 서버 주소를 포함한 전체 URL 형식으로 설정
      const frontendDomain = getFrontendDomain();
      formData.value.targetContent = `${frontendDomain}/landing/${landingPageId}`;
    } else {
      formData.value.targetContent = '';
    }
  };

  // 이벤트 변경 핸들러
  const handleEventChange = (eventId) => {
    if (qrTypeInputsRef.value) {
      qrTypeInputsRef.value.selectedEventId = eventId;
    }

    if (eventId) {
      // 이벤트 참석 URL 생성
      const baseUrl = window.location.origin;
      formData.value.targetContent = `${baseUrl}/event/attendance/${eventId}`;
    } else {
      formData.value.targetContent = '';
    }
  };

  // 타겟 콘텐츠 업데이트 핸들러
  const handleTargetContentUpdate = (content) => {
    formData.value.targetContent = content;
  };

  // 타겟 위치 맵 열기
  const openTargetLocationMap = () => {
    showTargetLocationMapModal.value = true;
  };

  // 타겟 위치 선택 핸들러
  const handleTargetLocationSelect = (location) => {
    if (qrTypeInputsRef.value) {
      qrTypeInputsRef.value.targetLocationData = {
        latitude: location.lat.toString(),
        longitude: location.lng.toString(),
        address: location.address || '선택한 위치'
      };
    }

    // Google Maps URL 형식으로 타겟 콘텐츠 설정
    formData.value.targetContent = `https://maps.google.com/?q=${location.lat},${location.lng}`;
    showTargetLocationMapModal.value = false;
  };

  // QR 코드 타입 변경 핸들러
  const handleQrTypeChange = (newType) => {
    // 수정 모드에서는 타입 변경 시 초기화하지 않음 (서버에서 받은 데이터 유지)
    if (isEditMode.value) {
      // 랜딩 페이지나 이벤트 타입으로 변경된 경우에만 관련 데이터 로드
      if (newType === 'LANDING_PAGE' && qrTypeInputsRef.value) {
        qrTypeInputsRef.value.fetchLandingPages();
      } else if (newType === 'EVENT_ATTENDANCE' && qrTypeInputsRef.value) {
        qrTypeInputsRef.value.fetchEvents();
      }
      return;
    }

    // 생성 모드에서의 처리
    if (newType === 'LANDING_PAGE') {
      if (qrTypeInputsRef.value) {
        qrTypeInputsRef.value.fetchLandingPages();
        qrTypeInputsRef.value.selectedLandingPageId = '';
      }
      // 타겟 콘텐츠 초기화 (랜딩 페이지 선택 전)
      formData.value.targetContent = '';
    } else if (newType === 'EVENT_ATTENDANCE') {
      if (qrTypeInputsRef.value) {
        qrTypeInputsRef.value.fetchEvents();
        qrTypeInputsRef.value.selectedEventId = '';
      }
      // 타겟 콘텐츠 초기화 (이벤트 선택 전)
      formData.value.targetContent = '';
    } else if (newType === 'LOCATION') {
      // 타겟 위치 정보 초기화
      if (qrTypeInputsRef.value) {
        qrTypeInputsRef.value.targetLocationData = {
          latitude: '',
          longitude: '',
          address: ''
        };
      }
      // 타겟 콘텐츠 초기화
      formData.value.targetContent = '';
    } else if (newType === 'WIFI') {
      // Wi-Fi 정보 초기화
      if (qrTypeInputsRef.value) {
        qrTypeInputsRef.value.wifiData = {
          ssid: '',
          securityType: 'WPA',
          password: '',
          hidden: false
        };
      }
      // 타겟 콘텐츠 초기화
      formData.value.targetContent = '';
    }
  };

  // 문제 연결 성공 핸들러
  const handleLinkSuccess = () => {
    showQuizSelectModal.value = false;
    alert('문제가 성공적으로 연결되었습니다.');
    // fetchQrCodeData(); // 최신 정보로 업데이트 - 이 함수는 부모에서 호출
  };

  // 뒤로 가기
  const goBack = () => {
    router.push('/qr-management/list');
  };

  // QR 코드 타입이 변경되면 관련 데이터 처리
  watch(() => formData.value.qrType, handleQrTypeChange);

  // 타겟 콘텐츠 변경 감지 (QR 코드 생성용)
  watch(() => formData.value.targetContent, (newValue, oldValue) => {
    // 수정 모드에서는 타겟 콘텐츠가 변경되어도 QR 코드 미리보기 업데이트하지 않음
    if (isEditMode.value) {
        return; // 수정 모드에서는 타겟 콘텐츠 변경에 반응하지 않음
    }

    // 이벤트 선택으로 인한 타겟 콘텐츠 변경인지 확인
    const isEventUrlChange =
      newValue &&
      oldValue &&
      newValue.includes('/event/') &&
      oldValue.includes('/event/');

    // 이벤트 URL이 변경된 경우 QR 코드 미리보기 업데이트하지 않음
    if (isEventUrlChange) {
      return;
    }

    // 생성 모드에서 아직 QR코드가 생성되지 않았을 때
    if (newValue && newValue.trim() !== '') {
      // 내용이 있고, 이전 값이 비어있었거나 아직 생성 전일 때만 자동 생성
      if (!isQrCodeGenerated.value) {
        // generateClientSideQrCode(); // 이 함수는 부모에서 호출
      }
    } else {
      // 내용이 비었을 때 미리보기 클리어
      isQrCodeGenerated.value = false; // 재생성 필요 상태
    }
  });



  return {
    // 상태
    isLoading,
    error,
    isSubmitting,
    imageLoadError,
    formData,
    locationData,
    locationImageFile,
    locationImagePreview,
    excelFile,
    excelSampleQrData,
    showQuizSelectModal,
    showTargetLocationMapModal,
    excelUploadRef,
    qrTypeInputsRef,
    isQrCodeGenerated,
    initialTargetContent,
    isDesignUpdateOnly,

    // 계산된 속성
    currentProject,
    isEditMode,
    selectedEventId,
    events,

    // 메서드
    getFrontendDomain,
    handleExcelFileChange,
    handleExcelSampleDataChange,
    handleLandingPageChange,
    handleEventChange,
    handleTargetContentUpdate,
    openTargetLocationMap,
    handleTargetLocationSelect,
    handleQrTypeChange,
    handleLinkSuccess,
    goBack
  };
}
