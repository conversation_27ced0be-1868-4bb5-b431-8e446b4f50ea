import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 팀 목록을 가져옵니다.
 * @param {number} projectId - 프로젝트 ID (선택적)
 * @param {number} eventId - 이벤트 ID (선택적)
 * @param {object} paginationParams - 페이지네이션 파라미터 (선택적)
 * @returns {Promise<Object>} 팀 목록을 포함한 응답 객체 Promise
 */
export const getTeams = async (projectId = null, eventId = null, paginationParams = {}) => {
  try {
    // 기본 파라미터 설정
    const params = {
      page: 0,
      size: 100,
      sort: 'teamId,desc',
      ...paginationParams
    };

    // projectId가 있으면 파라미터에 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // eventId가 있으면 파라미터에 추가
    if (eventId) {
      params.eventId = eventId;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get('/teams/list', { params }),
      '팀 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '팀 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 팀 상세 정보를 가져옵니다.
 * @param {number} teamId - 팀 ID
 * @returns {Promise<Object>} 팀 상세 정보를 포함한 응답 객체 Promise
 */
export const getTeamById = async (teamId) => {
  try {
    const response = await executeApiCall(
      () => apiClient.get(`/teams/${teamId}`),
      '팀 상세 정보를 가져오는 데 실패했습니다.'
    );
    return response;
  } catch (error) {
    console.error(`팀 상세 정보 오류:`, error);
    const errorMessage = handleApiError(error, '팀 상세 정보를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 팀을 생성합니다.
 * @param {number} projectId - 프로젝트 ID
 * @param {object} teamData - 팀 데이터 ({ teamName, description })
 * @returns {Promise<object>} 성공 시 생성된 팀 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const createTeam = async (projectId, teamData) => {
  try {
    const response = await apiClient.post(`/projects/${projectId}/teams`, teamData);

    if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '팀 생성에 실패했습니다.');
    }
  } catch (error) {
    const errorMessage = handleApiError(error, '팀 생성 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 팀을 수정합니다.
 * @param {number} teamId - 수정할 팀 ID
 * @param {object} teamData - 팀 데이터 ({ teamName, description })
 * @returns {Promise<object>} 성공 시 수정된 팀 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const updateTeam = async (teamId, teamData) => {
  try {
    const response = await apiClient.put(`/teams/${teamId}`, teamData);

    if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '팀 수정에 실패했습니다.');
    }
  } catch (error) {
    const errorMessage = handleApiError(error, '팀 수정 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 팀을 삭제합니다.
 * @param {number} teamId - 삭제할 팀 ID
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const deleteTeam = async (teamId) => {
  try {
    const response = await apiClient.delete(`/teams/${teamId}`);

    if (response.data && response.data.success) {
      return true;
    } else {
      throw new Error(response.data?.message || response.data?.error?.message || '팀 삭제에 실패했습니다.');
    }
  } catch (error) {
    const errorMessage = handleApiError(error, '팀 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};