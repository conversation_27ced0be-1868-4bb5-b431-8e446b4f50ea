<template>
  <div class="menu-management">
    <div class="header">
      <h1>메뉴 관리</h1>
      <p class="description">시스템의 메뉴 구조를 관리하고 표시/숨김을 설정할 수 있습니다.</p>
    </div>

    <!-- 로딩 상태 -->
    <div v-if="isLoading" class="loading">
      <p>메뉴 데이터를 불러오는 중...</p>
    </div>

    <!-- 에러 상태 -->
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="loadMenuData" class="retry-btn">다시 시도</button>
    </div>

    <!-- 메뉴 관리 인터페이스 -->
    <div v-else class="menu-interface">
      <!-- 탭 네비게이션 -->
      <div class="tab-navigation">
        <button
          @click="activeTab = 'menu'"
          class="tab-btn"
          :class="{ active: activeTab === 'menu' }"
        >
          메뉴 관리
        </button>
        <button
          @click="activeTab = 'permission'"
          class="tab-btn"
          :class="{ active: activeTab === 'permission' }"
        >
          권한 관리
        </button>
      </div>

      <!-- 메뉴 관리 탭 -->
      <div v-if="activeTab === 'menu'" class="tab-content">
        <!-- 컨트롤 패널 -->
        <ControlPanel
          :total-menu-count="totalMenuCount"
          :visible-menu-count="visibleMenuCount"
          :hidden-menu-count="hiddenMenuCount"
          @expand-all="expandAll"
          @collapse-all="collapseAll"
          @create-menu="showCreateMenuModal"
          @refresh="refreshMenuData"
        />

        <!-- 메뉴 트리 -->
        <MenuTree
          v-model:menu-tree-data="menuTreeData"
          @menu-order-change="onMenuOrderChange"
          @sub-menu-order-change="onSubMenuOrderChange"
          @drag-start="onDragStart"
          @drag-end="onDragEnd"
          @toggle-node="toggleNode"
          @toggle-visibility="toggleVisibility"
          @edit-menu="showEditMenuModal"
          @delete-menu="showDeleteConfirmModal"
        />

      <!-- 선택된 메뉴 정보 -->
      <div v-if="selectedNode" class="selected-menu-info">
        <h3>선택된 메뉴 정보</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>메뉴명:</label>
            <span>{{ selectedNode.label }}</span>
          </div>
          <div class="info-item">
            <label>라우트명:</label>
            <span>{{ selectedNode.routeName }}</span>
          </div>
          <div class="info-item">
            <label>경로:</label>
            <span>{{ selectedNode.routePath }}</span>
          </div>
          <div class="info-item">
            <label>권한:</label>
            <span>{{ selectedNode.permissions?.join(', ') || '없음' }}</span>
          </div>
          <div class="info-item">
            <label>표시 상태:</label>
            <span :class="{ 'visible': selectedNode.visible, 'hidden': !selectedNode.visible }">
              {{ selectedNode.visible ? '표시' : '숨김' }}
            </span>
          </div>
          <div class="info-item">
            <label>순서:</label>
            <span>{{ selectedNode.order }}</span>
          </div>
        </div>
      </div>

        <!-- 순서 변경 저장 버튼 -->
        <div class="save-section">
          <div class="save-info">
            <p class="save-description">
              <strong>참고:</strong> 메뉴 표시/숨김은 즉시 저장됩니다.
              아래 버튼은 메뉴 순서 변경사항만 저장합니다.
            </p>
          </div>
          <div class="save-buttons">
            <button @click="saveChanges" class="save-btn" :disabled="!hasChanges">
              순서 변경사항 저장
            </button>
            <button @click="resetChanges" class="reset-btn" :disabled="!hasChanges">
              순서 변경사항 취소
            </button>
          </div>
        </div>
      </div>

      <!-- 권한 관리 탭 -->
      <div v-if="activeTab === 'permission'" class="tab-content">
        <PermissionManagement
          :flat-menu-list="flatMenuList"
          v-model:selected-menu-for-permission="selectedMenuForPermission"
          :role-permissions="rolePermissions"
          :user-permissions="userPermissions"
          :has-permission-changes="hasPermissionChanges"
          :user-permission-form="userPermissionForm"
          :available-roles="availableRoles"
          @load-permissions="loadMenuPermissions"
          @toggle-role-permission="toggleRolePermission"
          @save-role-permission="saveRolePermission"
          @save-user-permission="saveUserPermission"
          @remove-user-permission="removeUserPermission"
        />
      </div>
    </div>

    <!-- 메뉴 생성/수정 모달 -->
    <MenuFormModal
      :show="showMenuModal"
      :is-edit-mode="isEditMode"
      :editing-menu-id="editingMenu?.id"
      :initial-data="menuForm"
      :flat-menu-list="flatMenuList"
      :is-submitting="isSubmitting"
      @close="closeMenuModal"
      @submit="submitMenuForm"
    />

    <!-- 메뉴 삭제 확인 모달 -->
    <DeleteConfirmModal
      :show="showDeleteModal"
      :menu-to-delete="menuToDelete"
      :is-deleting="isDeletingMenu"
      @close="closeDeleteModal"
      @confirm="executeDeleteMenu"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useMenuManagement } from '@/composables/useMenuManagement';
import { useMenuDragDrop } from '@/composables/useMenuDragDrop';
import {
  setRoleMenuPermission,
  setUserMenuPermission,
  getMenuApiErrorMessage
} from '@/api/menu';

// 컴포넌트 import
import ControlPanel from '@/components/admin/ControlPanel.vue';
import MenuTree from '@/components/admin/MenuTree.vue';
import MenuFormModal from '@/components/admin/MenuFormModal.vue';
import DeleteConfirmModal from '@/components/admin/DeleteConfirmModal.vue';
import PermissionManagement from '@/components/admin/PermissionManagement.vue';

// 메뉴 관리 composable 사용
const {
  isLoading,
  error,
  menuTreeData,
  originalMenuData,
  selectedNode,
  hasChanges,
  totalMenuCount,
  visibleMenuCount,
  hiddenMenuCount,
  flatMenuList,
  loadMenuData,
  toggleVisibility,
  createNewMenu,
  updateExistingMenu,
  deleteExistingMenu,
  hasChildMenus,
  refreshMenuData,
  resetChanges
} = useMenuManagement();

// 드래그 앤 드롭 composable 사용
const {
  draggedItem,
  onDragStart,
  onDragEnd,
  onMenuOrderChange,
  onSubMenuOrderChange,
  saveChanges,
  toggleNode,
  expandAll,
  collapseAll
} = useMenuDragDrop(menuTreeData, hasChanges, originalMenuData);

// 탭 관리
const activeTab = ref('menu');

// 메뉴 모달 관련 상태
const showMenuModal = ref(false);
const isEditMode = ref(false);
const isSubmitting = ref(false);
const editingMenu = ref(null);
const menuForm = ref({
  menuName: '',
  menuCode: '',
  menuUrl: '',
  menuIcon: 'fas fa-circle',
  parentMenuId: null,
  menuLevel: 1,
  displayOrder: 1,
  status: 'ACTIVE'
});

// 삭제 모달 관련 상태
const showDeleteModal = ref(false);
const menuToDelete = ref(null);
const isDeletingMenu = ref(false);

// 권한 관리 관련 상태
const selectedMenuForPermission = ref(null);
const rolePermissions = ref({});
const userPermissions = ref([]);
const hasPermissionChanges = ref(false);
const userPermissionForm = ref({
  userEmail: '',
  isAccessible: 'Y',
  permissionNote: ''
});

// 사용 가능한 역할 목록
const availableRoles = ref([
  { id: 'PROJECT_ADMIN', name: '프로젝트 관리자' },
  { id: 'SUB_ADMIN', name: '서브 관리자' },
  { id: 'VIEWER', name: '뷰어' }
]);





// 메뉴 모달 관련 함수들
const showCreateMenuModal = () => {
  isEditMode.value = false;
  editingMenu.value = null;
  resetMenuForm();
  showMenuModal.value = true;
};

const showEditMenuModal = (node) => {
  isEditMode.value = true;
  editingMenu.value = node;
  fillMenuForm(node);
  showMenuModal.value = true;
};

const closeMenuModal = () => {
  showMenuModal.value = false;
  isEditMode.value = false;
  editingMenu.value = null;
  resetMenuForm();
};

const resetMenuForm = () => {
  menuForm.value = {
    menuName: '',
    menuCode: '',
    menuUrl: '',
    menuIcon: 'fas fa-circle',
    parentMenuId: null,
    menuLevel: 1,
    displayOrder: 1,
    status: 'ACTIVE'
  };
};

const fillMenuForm = (node) => {
  menuForm.value = {
    menuName: node.label,
    menuCode: node.menuCode || node.routeName,
    menuUrl: node.routePath,
    menuIcon: node.menuIcon || 'fas fa-circle',
    parentMenuId: node.parentMenuId,
    menuLevel: node.menuLevel || 1,
    displayOrder: node.displayOrder || 1,
    status: node.status || 'ACTIVE'
  };
};

const submitMenuForm = async (formData) => {
  try {
    isSubmitting.value = true;

    if (isEditMode.value) {
      // 메뉴 수정
      await updateExistingMenu(editingMenu.value.id, formData);
      alert('메뉴가 성공적으로 수정되었습니다.');
    } else {
      // 메뉴 생성
      await createNewMenu(formData);
      alert('메뉴가 성공적으로 생성되었습니다.');
    }

    closeMenuModal();

  } catch (err) {
    console.error('메뉴 저장 실패:', err);
    alert(`메뉴 저장 실패: ${err.message}`);
  } finally {
    isSubmitting.value = false;
  }
};

// 삭제 확인 모달 표시
const showDeleteConfirmModal = (node) => {
  menuToDelete.value = node;
  showDeleteModal.value = true;
};

// 삭제 모달 닫기
const closeDeleteModal = () => {
  showDeleteModal.value = false;
  menuToDelete.value = null;
  isDeletingMenu.value = false;
};

// 메뉴 삭제 실행
const executeDeleteMenu = async () => {
  if (!menuToDelete.value) return;

  // 하위 메뉴가 있는지 다시 한 번 확인
  if (hasChildMenus(menuToDelete.value)) {
    alert('하위 메뉴가 있는 메뉴는 삭제할 수 없습니다. 먼저 하위 메뉴를 삭제해주세요.');
    return;
  }

  try {
    isDeletingMenu.value = true;
    await deleteExistingMenu(menuToDelete.value.id);

    closeDeleteModal();
    alert('메뉴가 성공적으로 삭제되었습니다.');

  } catch (err) {
    console.error('메뉴 삭제 실패:', err);
    alert(`메뉴 삭제 실패: ${err.message}`);
  } finally {
    isDeletingMenu.value = false;
  }
};

// 권한 관리 관련 함수들
const loadMenuPermissions = async () => {
  if (!selectedMenuForPermission.value) return;

  try {
    // 실제 구현에서는 백엔드에서 현재 권한 설정을 가져와야 함
    // 여기서는 기본값으로 설정
    rolePermissions.value = {
      'PROJECT_ADMIN': false,
      'SUB_ADMIN': false,
      'VIEWER': false
    };

    userPermissions.value = [];
    hasPermissionChanges.value = false;
  } catch (err) {
    console.error('메뉴 권한 로드 실패:', err);
    const errorMessage = getMenuApiErrorMessage(err);
    alert(`메뉴 권한 로드 실패: ${errorMessage}`);
  }
};

const toggleRolePermission = (roleId, isAccessible) => {
  rolePermissions.value[roleId] = isAccessible;
  hasPermissionChanges.value = true;
};

const saveRolePermission = async (roleId) => {
  if (!selectedMenuForPermission.value) return;

  try {
    const isAccessible = rolePermissions.value[roleId];
    await setRoleMenuPermission(selectedMenuForPermission.value.id, roleId, isAccessible);

    hasPermissionChanges.value = false;
    alert(`${availableRoles.value.find(r => r.id === roleId)?.name} 역할의 권한이 저장되었습니다.`);
  } catch (err) {
    console.error('역할 권한 저장 실패:', err);
    const errorMessage = getMenuApiErrorMessage(err);
    alert(`역할 권한 저장 실패: ${errorMessage}`);
  }
};

const saveUserPermission = async () => {
  if (!selectedMenuForPermission.value || !userPermissionForm.value.userEmail) return;

  try {
    await setUserMenuPermission(
      selectedMenuForPermission.value.id,
      userPermissionForm.value.userEmail,
      {
        isAccessible: userPermissionForm.value.isAccessible,
        permissionNote: userPermissionForm.value.permissionNote
      }
    );

    // 목록에 추가
    userPermissions.value.push({
      userEmail: userPermissionForm.value.userEmail,
      isAccessible: userPermissionForm.value.isAccessible,
      permissionNote: userPermissionForm.value.permissionNote
    });

    // 폼 초기화
    userPermissionForm.value = {
      userEmail: '',
      isAccessible: 'Y',
      permissionNote: ''
    };

    alert('사용자 권한이 저장되었습니다.');
  } catch (err) {
    console.error('사용자 권한 저장 실패:', err);
    const errorMessage = getMenuApiErrorMessage(err);
    alert(`사용자 권한 저장 실패: ${errorMessage}`);
  }
};

const removeUserPermission = async (userEmail) => {
  if (!selectedMenuForPermission.value) return;

  if (confirm(`${userEmail} 사용자의 권한을 삭제하시겠습니까?`)) {
    try {
      // 실제 구현에서는 삭제 API 호출
      // await removeUserMenuPermission(selectedMenuForPermission.value.id, userEmail);

      // 목록에서 제거
      userPermissions.value = userPermissions.value.filter(p => p.userEmail !== userEmail);

      alert('사용자 권한이 삭제되었습니다.');
    } catch (err) {
      console.error('사용자 권한 삭제 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      alert(`사용자 권한 삭제 실패: ${errorMessage}`);
    }
  }
};

// 컴포넌트 마운트 시 데이터 로드
onMounted(() => {
  loadMenuData();
});
</script>

<style scoped>
.menu-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.description {
  color: #666;
  font-size: 16px;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.error {
  background: #fee;
  color: #c33;
}

.retry-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 탭 네비게이션 스타일 */
.tab-navigation {
  display: flex;
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: #007bff;
  background: #f8f9fa;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tab-content {
  min-height: 400px;
}

/* 선택된 메뉴 정보 */
.selected-menu-info {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.selected-menu-info h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item label {
  font-weight: bold;
  color: #495057;
  min-width: 80px;
}

.info-item span.visible {
  color: #28a745;
  font-weight: bold;
}

.info-item span.hidden {
  color: #dc3545;
  font-weight: bold;
}

/* 저장 섹션 */
.save-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
}

.save-info {
  margin-bottom: 15px;
}

.save-description {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
  padding: 10px;
  background: #e9ecef;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.save-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.save-btn, .reset-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover:not(:disabled) {
  background: #545b62;
}

.save-btn:disabled, .reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .tab-navigation {
    flex-direction: column;
  }

  .tab-btn {
    text-align: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}




</style>
