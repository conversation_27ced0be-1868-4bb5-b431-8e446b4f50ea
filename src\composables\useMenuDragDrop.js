import { ref } from 'vue';
import { updateMenu, convertTreeDataToBackendMenu, getMenuApiErrorMessage } from '@/api/menu';

/**
 * 메뉴 드래그 앤 드롭 기능을 위한 composable
 */
export function useMenuDragDrop(menuTreeData, hasChanges, originalMenuData) {
  // 드래그 상태
  const draggedItem = ref(null);

  // 드래그 시작 이벤트
  const onDragStart = (event) => {
    draggedItem.value = event.item;
    console.log('드래그 시작:', event);
  };

  // 드래그 종료 이벤트
  const onDragEnd = (event) => {
    console.log('드래그 종료:', event);
    draggedItem.value = null;
  };

  // 메인 메뉴 순서 변경 이벤트
  const onMenuOrderChange = (event) => {
    console.log('메뉴 순서 변경 이벤트:', event);
    console.log('이벤트 타입들:', {
      added: !!event.added,
      removed: !!event.removed,
      moved: !!event.moved
    });

    // 모든 변경사항에 대해 hasChanges를 true로 설정
    hasChanges.value = true;
    console.log('hasChanges 설정됨:', hasChanges.value);

    // 계층 구조 업데이트
    if (event.added) {
      // 다른 계층에서 이동해온 경우
      const addedItem = event.added.element;
      updateMenuLevel(addedItem, 1); // 최상위 레벨로 설정
      console.log('메뉴 레벨 업데이트:', addedItem.label, '-> 레벨 1');
    }

    if (event.moved) {
      console.log('메뉴 이동:', event.moved.element.label,
                  '위치:', event.moved.oldIndex, '->', event.moved.newIndex);
    }

    updateDisplayOrders(menuTreeData.value);
    console.log('displayOrder 업데이트 완료');
  };

  // 하위 메뉴 순서 변경 이벤트
  const onSubMenuOrderChange = (event) => {
    console.log('하위 메뉴 순서 변경 이벤트:', event);
    console.log('하위 메뉴 이벤트 타입들:', {
      added: !!event.added,
      removed: !!event.removed,
      moved: !!event.moved
    });

    // 모든 변경사항에 대해 hasChanges를 true로 설정
    hasChanges.value = true;
    console.log('하위 메뉴 hasChanges 설정됨:', hasChanges.value);

    // 계층 구조 업데이트
    if (event.added) {
      // 다른 계층에서 이동해온 경우
      const addedItem = event.added.element;
      updateMenuLevel(addedItem, 2); // 하위 레벨로 설정
      console.log('하위 메뉴 레벨 업데이트:', addedItem.label, '-> 레벨 2');
    }

    if (event.moved) {
      console.log('하위 메뉴 이동:', event.moved.element.label,
                  '위치:', event.moved.oldIndex, '->', event.moved.newIndex);
    }

    updateDisplayOrders(menuTreeData.value);
    console.log('하위 메뉴 displayOrder 업데이트 완료');
  };

  // 메뉴 레벨 업데이트 함수
  const updateMenuLevel = (menu, level) => {
    menu.menuLevel = level;
    if (menu.nodes && menu.nodes.length > 0) {
      menu.nodes.forEach(subMenu => {
        updateMenuLevel(subMenu, level + 1);
      });
    }
  };

  // displayOrder 업데이트 함수
  const updateDisplayOrders = (nodes) => {
    nodes.forEach((node, index) => {
      node.displayOrder = index;
      if (node.nodes && node.nodes.length > 0) {
        updateDisplayOrders(node.nodes);
      }
    });
  };

  // 변경사항 저장 (드래그 앤 드롭으로 변경된 순서 및 계층 구조)
  const saveChanges = async () => {
    try {
      // 변경된 메뉴들을 찾아서 백엔드에 저장
      const updatePromises = [];

      // hasChanges가 true라면 무조건 모든 메뉴의 순서를 업데이트
      const saveNodeChanges = (nodes, parentId = null, level = 1) => {
        nodes.forEach((node, index) => {
          // 현재 위치에 맞게 순서, 레벨, 부모 관계 업데이트
          const updateData = {
            ...convertTreeDataToBackendMenu(node),
            displayOrder: index,
            menuLevel: level,
            parentMenuId: parentId
          };

          // 실제 값 업데이트
          node.displayOrder = index;
          node.menuLevel = level;
          node.parentMenuId = parentId;

          updatePromises.push(updateMenu(node.id, updateData));
          console.log(`메뉴 "${node.label}" 업데이트:`, {
            displayOrder: index,
            menuLevel: level,
            parentMenuId: parentId
          });

          // 하위 메뉴도 재귀적으로 처리
          if (node.nodes && node.nodes.length > 0) {
            saveNodeChanges(node.nodes, node.id, level + 1);
          }
        });
      };

      // hasChanges가 true인 경우에만 저장 진행
      if (hasChanges.value) {
        saveNodeChanges(menuTreeData.value);

        if (updatePromises.length > 0) {
          await Promise.all(updatePromises);
          console.log(`${updatePromises.length}개 메뉴의 구조가 저장되었습니다.`);
          
          // 저장 성공 후 원본 데이터 업데이트
          originalMenuData.value = JSON.parse(JSON.stringify(menuTreeData.value));
          hasChanges.value = false;
          
          return `${updatePromises.length}개 메뉴의 구조가 성공적으로 저장되었습니다.`;
        } else {
          return '저장할 메뉴가 없습니다.';
        }
      } else {
        return '변경사항이 없습니다.';
      }

    } catch (err) {
      console.error('변경사항 저장 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      throw new Error(`변경사항 저장 실패: ${errorMessage}`);
    }
  };

  // 노드 펼치기/접기
  const toggleNode = (node) => {
    node.expanded = !node.expanded;
  };

  // 모든 노드 펼치기
  const expandAll = () => {
    const expandNodes = (nodes) => {
      nodes.forEach(node => {
        // 모든 메뉴를 펼치기 (하위 메뉴가 없어도 드롭 존 표시를 위해)
        node.expanded = true;
        if (node.nodes && node.nodes.length > 0) {
          expandNodes(node.nodes);
        }
      });
    };
    expandNodes(menuTreeData.value);
  };

  // 모든 노드 접기
  const collapseAll = () => {
    const collapseNodes = (nodes) => {
      nodes.forEach(node => {
        // 모든 메뉴를 접기
        node.expanded = false;
        if (node.nodes && node.nodes.length > 0) {
          collapseNodes(node.nodes);
        }
      });
    };
    collapseNodes(menuTreeData.value);
  };

  return {
    // 상태
    draggedItem,
    
    // 드래그 이벤트 핸들러
    onDragStart,
    onDragEnd,
    onMenuOrderChange,
    onSubMenuOrderChange,
    
    // 유틸리티 함수
    updateMenuLevel,
    updateDisplayOrders,
    saveChanges,
    toggleNode,
    expandAll,
    collapseAll
  };
}
