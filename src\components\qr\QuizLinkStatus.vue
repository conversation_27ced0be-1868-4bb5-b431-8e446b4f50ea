<template>
    <div>
      <div v-if="loading" class="text-center">
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
      <div v-else-if="error" class="alert alert-danger">{{ error }}</div>
      <div v-else>
        <h6 class="mb-2">연결된 문제 ({{ quizCount }})</h6>
        <ul v-if="quizzes.length > 0" class="list-group">
          <li v-for="quiz in quizzes" :key="quiz.qrCodeId" class="list-group-item d-flex justify-content-between align-items-center">
            <span :class="{ 'text-muted': quiz.quizStatus === 'INACTIVE' }" :style="{ 'text-decoration': quiz.quizStatus === 'INACTIVE' ? 'line-through' : 'none' }">
              {{ quiz.quizTitle }} ({{ quiz.quizType }})
            </span>
            <div>
              <span :class="['badge', 'rounded-pill', 'me-2', quiz.quizStatus === 'INACTIVE' ? 'bg-secondary' : 'bg-primary']"
                    :style="{ 'text-decoration': quiz.quizStatus === 'INACTIVE' ? 'line-through' : 'none' }">
                {{ quiz.categoryName }}
              </span>
              <p v-if="quiz.quizStatus === 'INACTIVE'" class="text-danger small me-2">(비활성 처리된 문제입니다.)</p>
              <button type="button" @click="unlinkQuiz(quiz.qrCodeId, quiz.quizId)" class="btn btn-sm btn-outline-danger">해제</button>
            </div>
          </li>
        </ul>
        <div v-else>
          <p class="text-muted">연결된 문제가 없습니다.</p>
          <button type="button" @click="$emit('open-quiz-select-modal')" class="btn-primary">문제 선택</button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue';
  import { getQrQuizMappingListByQrCodeIds, unlinkQuizFromQrCode } from '@/api/qrQuizMapping';
  
  const props = defineProps({
    qrCodeId: {
      type: String,
      required: false,
      default: '',
    },
  });
  
  const loading = ref(false);
  const error = ref(null);
  const quizzes = ref([]);
  const quizCount = ref(0);

  const emit = defineEmits(['quiz-unlinked', 'open-quiz-select-modal']);

  const unlinkQuiz = async (qrCodeId, quizId) => {
    if (!confirm('정말로 연결을 해제하시겠습니까?')) return;

    try {
        await unlinkQuizFromQrCode(qrCodeId, quizId);
        alert('성공적으로 연결 해제되었습니다.');
        emit('quiz-unlinked');
        // 목록 새로고침
        fetchQrQuizMappingList(qrCodeId);
    } catch (error) {
        console.error('연결 해제 실패:', error);
        alert(error.message);
    }
  };
  
  const fetchQrQuizMappingList = async (qrCodeId) => {
    if (!qrCodeId) {
      quizzes.value = [];
      quizCount.value = 0;
      return;
    }
    loading.value = true;
    error.value = null;
    try {
      const response = await getQrQuizMappingListByQrCodeIds([qrCodeId]);
      const apiData = response.data;

      if (apiData && apiData.success && apiData.data && apiData.data.length > 0) {
        const qrData = apiData.data.find(d => d.qrCodeId === Number(qrCodeId));
        if (qrData && qrData.quizzes.length > 0) {
          quizzes.value = qrData.quizzes;
          quizCount.value = qrData.quizCount || qrData.quizzes.length;
        } else {
          quizzes.value = [];
          quizCount.value = 0;
        }
      } else {
        quizzes.value = [];
        quizCount.value = 0;
      }
    } catch (err) {
      error.value = '데이터를 불러오는 데 실패했습니다.';
      console.error(err);
      quizzes.value = [];
      quizCount.value = 0;
    } finally {
      loading.value = false;
    }
  };
  
  onMounted(() => {
    fetchQrQuizMappingList(props.qrCodeId);
  });
  
  watch(() => props.qrCodeId, (newqrCodeId) => {
    fetchQrQuizMappingList(newqrCodeId);
  });
  </script>
  
  <style scoped>
  .list-group-item {
    font-size: 0.9rem;
  }
  .text-muted {
    font-size: 0.9rem;
  }
  </style>