import { ref } from 'vue';
import { createQrCode } from '@/api/qrcode';

export function useQrBatch() {
  // 배치 생성 관련 상태
  const qrBatchCreationList = ref([]);
  const isBatchProcessing = ref(false);
  const batchProgress = ref(0);
  const batchGlobalError = ref('');
  const parsedExcelRows = ref([]);

  // 단일 QR 코드 생성 함수 (배치용)
  const createSingleQrCode = async (qrListItem, index, totalItems, formData, locationData, locationImageFile, designOptions) => {
    try {
      // 상태 업데이트
      qrListItem.status = 'processing';
      
      // FormData 객체 생성
      const formDataObj = new FormData();
      
      // 기본 QR 코드 정보 추가
      formDataObj.append('qrName', qrListItem.finalQrName);
      formDataObj.append('qrType', qrListItem.qrType);
      formDataObj.append('targetContent', qrListItem.targetContent);
      formDataObj.append('status', 'ACTIVE');
      formDataObj.append('description', qrListItem.description || '');
      
      // 유효 기간 추가 (있는 경우)
      if (qrListItem.validFromDate) {
        formDataObj.append('validFromDate', qrListItem.validFromDate);
      }
      if (qrListItem.validToDate) {
        formDataObj.append('validToDate', qrListItem.validToDate);
      }
      
      // 위치 이미지가 있는 경우 추가
      if (locationImageFile.value) {
        formDataObj.append('qrInstalledImageFile', locationImageFile.value);
      }
      
      // 위치 정보가 있는 경우 추가
      if (locationData.value.latitude && locationData.value.longitude) {
        formDataObj.append('installationLocationLat', locationData.value.latitude);
        formDataObj.append('installationLocationLng', locationData.value.longitude);
        formDataObj.append('installationLocation', locationData.value.address || '');
      }
      
      // 디자인 옵션 추가 (JSON 문자열로 변환)
      let designOptionsJSON = '{}';
      if (designOptions && designOptions.value) {
        designOptionsJSON = JSON.stringify({
          dotsOptions: {
            color: designOptions.value.dotsOptions?.color || '#000000',
            type: 'square',
            cornersSquareOptions: {
              color: designOptions.value.dotsOptions?.cornersSquareOptions?.color || '#000000',
              type: designOptions.value.dotsOptions?.cornersSquareOptions?.type || 'square'
            },
            cornersDotOptions: {
              color: designOptions.value.dotsOptions?.cornersDotOptions?.color || '#000000',
              type: designOptions.value.dotsOptions?.cornersDotOptions?.type || 'square'
            }
          },
          backgroundOptions: {
            color: designOptions.value.backgroundOptions?.color || '#FFFFFF'
          },
          ...(designOptions.value.logoRatio && { logoRatio: designOptions.value.logoRatio }),
          errorCorrectionLevel: designOptions.value.errorCorrectionLevel || 'M',
          layoutOptions: null
        });
      }
      formDataObj.append('designOptions', designOptionsJSON);
      
      // QR 코드 생성 API 호출
      const response = await createQrCode(formDataObj);
      
      // 성공 처리
      qrListItem.status = 'success';
      qrListItem.generatedQrData = response.data;
    } catch (error) {
      // 오류 처리
      console.error(`QR 코드 생성 실패 (${qrListItem.finalQrName}):`, error);
      qrListItem.status = 'error';
      qrListItem.errorMessage = error.response?.data?.message || error.message || '알 수 없는 오류가 발생했습니다.';
    }
    
    // 진행률 업데이트
    batchProgress.value = ((index + 1) / totalItems) * 100;
  };

  // 연속 생성 시작 함수
  const startBatchCreation = async (excelFile, parseExcelFile, formData, locationData, locationImageFile, designOptions) => {
    if (!excelFile.value || isBatchProcessing.value) {
      return;
    }
    
    isBatchProcessing.value = true;
    batchProgress.value = 0;
    batchGlobalError.value = '';
    
    // 항상 배열로 초기화
    qrBatchCreationList.value = [];  // 기존 배열 초기화
    
    try {
      // 엑셀 파일 파싱
      const validRows = await parseExcelFile(excelFile.value);
      parsedExcelRows.value = validRows;
      
      // 파싱된 데이터에서 QR코드 생성 목록 초기화
      qrBatchCreationList.value = validRows.map((row, index) => ({
        id: `batch_${Date.now()}_${index}`,
        originalQrName: row.qrName,
        finalQrName: row.qrName,
        qrType: row.qrType,
        targetContent: row.targetContent,
        description: row.description || '',
        validFromDate: row.validFromDate || '',
        validToDate: row.validToDate || '',
        status: 'pending', // pending, processing, success, error
        errorMessage: '',
        generatedQrData: null
      }));
      
      // 순차적으로 QR 코드 생성
      for (let i = 0; i < qrBatchCreationList.value.length; i++) {
        if (!isBatchProcessing.value) {
          // 사용자가 중단한 경우
          break;
        }
        
        await createSingleQrCode(
          qrBatchCreationList.value[i], 
          i, 
          qrBatchCreationList.value.length,
          formData,
          locationData,
          locationImageFile,
          designOptions
        );
        
        // 각 생성 사이에 짧은 지연 (서버 부하 방지)
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // 완료 처리
      const successCount = qrBatchCreationList.value.filter(item => item.status === 'success').length;
      const errorCount = qrBatchCreationList.value.filter(item => item.status === 'error').length;
      
      if (errorCount === 0) {
        alert(`모든 QR 코드가 성공적으로 생성되었습니다. (총 ${successCount}개)`);
      } else {
        alert(`QR 코드 생성이 완료되었습니다.\n성공: ${successCount}개\n실패: ${errorCount}개`);
      }
      
    } catch (error) {
      console.error('배치 생성 중 전역 오류:', error);
      batchGlobalError.value = error.message || '배치 생성 중 오류가 발생했습니다.';
      alert(`배치 생성 중 오류가 발생했습니다: ${batchGlobalError.value}`);
    } finally {
      isBatchProcessing.value = false;
    }
  };

  // 배치 생성 중단
  const stopBatchCreation = () => {
    isBatchProcessing.value = false;
    alert('배치 생성이 중단되었습니다.');
  };

  return {
    // 상태
    qrBatchCreationList,
    isBatchProcessing,
    batchProgress,
    batchGlobalError,
    parsedExcelRows,
    
    // 메서드
    createSingleQrCode,
    startBatchCreation,
    stopBatchCreation
  };
}
