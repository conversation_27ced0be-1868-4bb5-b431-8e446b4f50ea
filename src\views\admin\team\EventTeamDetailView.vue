<template>
  <div class="team-detail">
    <div class="header-section">
      <h1>팀 상세 정보</h1>
    </div>

    <div v-if="isLoading" class="loading-message">
      <div class="loading-spinner"></div>
      <p>팀 정보를 불러오는 중입니다...</p>
    </div>

    <div v-else-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="loadTeamDetail" class="retry-btn">다시 시도</button>
    </div>

    <div v-else-if="team" class="team-detail-content">
      <div class="team-detail-header">
        <h2>{{ team.teamName || '팀 이름 없음' }}</h2>
        <div class="team-detail-actions">
          <button @click="goBack" class="back-btn">목록으로</button>
        </div>
      </div>

      <div class="detail-card">
        <h2>기본 정보</h2>
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">팀 이름:</span>
            <span class="value">{{ team.teamName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">팀 코드:</span>
            <span class="value">{{ team.teamCode || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">소속 이벤트:</span>
            <span class="value">{{ team.eventName || '-' }}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">팀장 이름:</span>
            <input 
              v-model="team.leaderName" 
              @input="markAsChanged"
              class="edit-input-direct"
              placeholder="팀장 이름을 입력하세요"
            />
          </div>
          <div class="detail-item">
            <span class="label">팀 상태:</span>
            <select 
              v-model="team.teamStatus" 
              @change="markAsChanged"
              class="edit-select-direct"
            >
              <option value="ACTIVE">활성</option>
              <option value="LOCKED">잠김</option>
              <option value="PENDING_APPROVAL">승인대기</option>
            </select>
          </div>
          <div class="detail-item">
            <span class="label">최대 가입 팀원 수:</span>
            <input 
              v-model="team.maxMembers" 
              @input="markAsChanged"
              class="edit-input-direct"
              type="number"
              min="0"
              placeholder="최대 가입 가능한 팀원 수를 입력하세요"
            />
          </div>
          <div class="detail-item">
            <span class="label">등록일시:</span>
            <span class="value">{{ formatDate(team.createDate) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">수정일시:</span>
            <span class="value">{{ formatDate(team.updateDate) }}</span>
          </div>
        </div>
        <div class="detail-item">
            <span class="label">프로필 이미지:</span>
            <div class="profile-image-container">
              <img 
                v-if="profileImagePreview || (!imageToDelete && team.profileImagePath)"
                :src="profileImagePreview || team.profileImagePath" 
                alt="팀 프로필 이미지" 
                class="profile-image"
                @error="handleImageError"
              />
              <div class="image-actions">
                <input
                  id="profileImageInput"
                  type="file"
                  @change="handleProfileImageUpload"
                  accept="image/*"
                  style="display: none;"
                />
                <button type="button" @click="selectImage" class="image-btn upload-btn">이미지 선택</button>
                <button type="button" @click="removeProfileImage" class="image-btn remove-btn" v-if="profileImageFile || team.profileImagePath">이미지 삭제</button>
              </div>
              <div v-if="profileImageFile" class="file-info">
                <div class="file-name">새 파일: {{ profileImageFile.name }}</div>
              </div>
            </div>
        </div>  
      </div>

      <div class="detail-card">
        <h2>팀 설명</h2>
        <textarea 
          v-model="team.description" 
          @input="markAsChanged"
          class="description-textarea"
          placeholder="팀 설명을 입력하세요"
          rows="4"
        ></textarea>
      </div>

      <div class="detail-card" v-if="teamMembers && teamMembers.length > 0">
        <h2>팀원 목록</h2>
        <div class="members-table-container">
          <table class="members-table">
            <thead>
              <tr>
                <th>번호</th>
                <th>이름</th>
                <th>역할</th>
                <th>이메일</th>
                <th>가입일</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(member, index) in teamMembers" :key="member.id">
                <td>{{ index + 1 }}</td>
                <td>{{ member.name || '-' }}</td>
                <td>
                  <span class="role-badge" :class="member.isLeader ? 'leader' : 'member'">
                    {{ member.isLeader ? '팀장' : '팀원' }}
                  </span>
                </td>
                <td>{{ member.email || '-' }}</td>
                <td>{{ formatDate(member.joinDate) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="action-buttons">
        <button @click="saveAllChanges" class="save-btn">수정하기</button>
        <button @click="confirmDelete" class="delete-btn">팀 삭제</button>
      </div>
    </div>

    <div v-else class="no-data-message">
      <p>팀 정보를 찾을 수 없습니다.</p>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay" @click="cancelDelete">
      <div class="modal-content delete-modal" @click.stop>
        <h2>팀 삭제</h2>
        <div class="modal-close" @click="cancelDelete">&times;</div>
        <p>다음 팀을 삭제하시겠습니까?</p>
        <p><strong>{{ team?.teamName }}</strong></p>
        <p class="warning-text">삭제된 팀 정보는 복구할 수 없습니다.</p>
        <div class="modal-actions">
          <button class="cancel-btn" @click="cancelDelete">취소</button>
          <button class="confirm-delete-btn" @click="deleteTeam">삭제</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useImageError } from '@/composables/useImageError';
import apiClient from '@/api/index';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// 이미지 에러 핸들링
const { handleImageError } = useImageError();

const isLoading = ref(false);
const error = ref(null);
const team = ref(null);
const teamMembers = ref([]);
const showDeleteModal = ref(false);

// 편집 모드 관련 변수
const originalLeaderName = ref('');
const originalMemberCount = ref(0);
const originalTeamStatus = ref('');
const hasUnsavedChanges = ref(false);

// 이미지 관련 변수들
const profileImageFile = ref(null);
const profileImagePreview = ref('');
const imageToDelete = ref(false);

const teamId = route.params.id;

onMounted(() => {
  if (teamId) {
    loadTeamDetail();
    loadTeamMembers();
  } else {
    error.value = '팀 ID가 제공되지 않았습니다.';
  }
});

const loadTeamDetail = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const response = await apiClient.get(`/teams/${teamId}`);
    
    if (response && response.data && response.data.success) {
      team.value = response.data.data;
      // 원본 값 저장
      originalLeaderName.value = team.value.leaderName || '';
      originalMemberCount.value = team.value.memberCount || 0;
      originalTeamStatus.value = team.value.teamStatus || '';
    } else {
      error.value = '팀 정보를 불러오는 데 실패했습니다.';
    }
  } catch (err) {
    console.error('팀 상세 정보 로드 실패:', err);
    error.value = '팀 정보를 불러오는 데 실패했습니다.';
  } finally {
    isLoading.value = false;
  }
};

const loadTeamMembers = async () => {
  // try {
  //   const response = await apiClient.get(`/teams/${teamId}/members`);
    
  //   if (response && response.data && response.data.success) {
  //     teamMembers.value = response.data.data || [];
  //   }
  // } catch (err) {
  //   console.error('팀원 목록 로드 실패:', err);
  //   // 팀원 정보는 선택사항이므로 에러를 표시하지 않음
  // }
};

// 변경사항 표시 함수
const markAsChanged = () => {
  hasUnsavedChanges.value = true;
};

// 변경사항 확인
const hasChanges = computed(() => {
  return team.value && (
    team.value.leaderName !== originalLeaderName.value ||
    team.value.memberCount !== originalMemberCount.value ||
    team.value.teamStatus !== originalTeamStatus.value
  ) || hasUnsavedChanges.value;
});

// 모든 변경사항 저장
const saveAllChanges = async () => {
  if (!hasChanges.value) return;
  
  isLoading.value = true;
  try {
    // FormData 생성 (이미지 업로드를 위해)
    const formDataToSend = new FormData();
    
    // 기본 팀 정보
    formDataToSend.append('projectId', team.value.projectId);
    formDataToSend.append('eventId', team.value.eventId);
    formDataToSend.append('teamName', team.value.teamName);
    formDataToSend.append('teamCode', team.value.teamCode || '');
    formDataToSend.append('description', team.value.description || '');
    formDataToSend.append('maxMembers', team.value.maxMembers ?? '');
    formDataToSend.append('teamStatus', team.value.teamStatus);
    formDataToSend.append('leaderName', team.value.leaderName);
    formDataToSend.append('leaderPhone', team.value.leaderPhone || '');
    formDataToSend.append('memberCount', team.value.memberCount);
    
    // 이미지 처리
    if (profileImageFile.value) {
      // 새 이미지 업로드
      formDataToSend.append('profileImageFile', profileImageFile.value);
    } else if (imageToDelete.value) {
      // 이미지 삭제
      formDataToSend.append('deleteImage', 'true');
    }

    const response = await apiClient.put(`/teams/${teamId}`, formDataToSend, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (response && response.data && response.data.success) {
      // 원본 값 업데이트
      originalLeaderName.value = team.value.leaderName;
      originalMemberCount.value = team.value.memberCount;
      originalTeamStatus.value = team.value.teamStatus;
      hasUnsavedChanges.value = false;
      // 이미지 관련 상태 초기화
      profileImageFile.value = null;
      profileImagePreview.value = '';
      imageToDelete.value = false;
      alert('팀 정보가 성공적으로 수정되었습니다.');
      // 팀 정보 다시 로드
      await loadTeamDetail();
    } else {
      const errorMessage = response?.data?.error?.message || '팀 정보 수정에 실패했습니다.';
      alert(errorMessage);
    }
  } catch (err) {
    console.error('팀 정보 수정 실패:', err);
    const errorMessage = err.response?.data?.error?.message || '팀 정보 수정 중 오류가 발생했습니다.';
    alert(errorMessage);
  } finally {
    isLoading.value = false;
  }
};

const goBack = () => {
  router.push('/events/teams');
};

const confirmDelete = () => {
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
};

const deleteTeam = async () => {
  try {
    const response = await apiClient.delete(`/teams/${teamId}`);
    
    if (response && response.data && response.data.success) {
      alert('팀이 성공적으로 삭제되었습니다.');
      router.push('/events/teams');
    } else {
      const errorMessage = response?.data?.error?.message || '팀 삭제에 실패했습니다.';
      alert(errorMessage);
    }
  } catch (err) {
    console.error('팀 삭제 실패:', err);
    const errorMessage = err.response?.data?.error?.message || '팀 삭제 중 오류가 발생했습니다.';
    alert(errorMessage);
  } finally {
    cancelDelete();
  }
};

const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getTeamStatusText = (status) => {
  const statusMap = {
    'ACTIVE': '활성',
    'LOCKED': '잠김',
    'PENDING_APPROVAL': '승인대기'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status) => {
  const classMap = {
    'ACTIVE': 'active',
    'LOCKED': 'locked',
    'PENDING_APPROVAL': 'pending'
  };
  return classMap[status] || '';
};

// 이미지 선택 버튼 클릭
const selectImage = () => {
  const fileInput = document.getElementById('profileImageInput');
  if (fileInput) {
    fileInput.click();
  }
};

// 프로필 이미지 업로드 처리
const handleProfileImageUpload = (e) => {
  const file = e.target.files && e.target.files[0];
  if (file) {
    profileImageFile.value = file;
    profileImagePreview.value = URL.createObjectURL(file);
    imageToDelete.value = false;
    hasUnsavedChanges.value = true;
  }
};

// 프로필 이미지 제거
const removeProfileImage = () => {
  profileImageFile.value = null;
  profileImagePreview.value = '';
  imageToDelete.value = true;
  hasUnsavedChanges.value = true;
  // 파일 input 초기화
  const fileInput = document.getElementById('profileImageInput');
  if (fileInput) {
    fileInput.value = '';
  }
};
</script>

<style scoped>
.team-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e7ef;
}

.back-btn {
  background-color: #607D8B;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
  text-decoration: none;
}

.back-btn:hover {
  background-color: #546E7A;
}

h1 {
  margin: 0;
  color: #333;
  font-size: 28px;
}

.loading-message,
.error-message,
.no-data-message {
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.team-detail-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e7ef;
}

.team-detail-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.team-detail-actions {
  display: flex;
  gap: 10px;
}

.detail-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e7ef;
}

.detail-card h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  border-bottom: 2px solid #f0f4f8;
  padding-bottom: 10px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.value {
  font-size: 16px;
  color: #333;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: background-color 0.2s ease;
}



.profile-image-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.profile-image {
  width: 300px;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #ddd;
  object-fit: contain;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  gap: 8px;
}

.image-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.upload-btn {
  background-color: #007bff;
  color: white;
}

.upload-btn:hover {
  background-color: #0056b3;
}

.remove-btn {
  background-color: #dc3545;
  color: white;
}

.remove-btn:hover {
  background-color: #c82333;
}

.file-info {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.file-name {
  font-weight: 500;
}



.status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

.status.active {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status.locked {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status.pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.description-content {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  line-height: 1.6;
  white-space: pre-wrap;
}

.members-table-container {
  overflow-x: auto;
}

.members-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.members-table th,
.members-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.members-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.members-table tr:hover {
  background-color: #f9f9f9;
}

.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.role-badge.leader {
  background-color: #e3f2fd;
  color: #1976d2;
}

.role-badge.member {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 20px 0;
}

.save-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.save-btn:hover {
  background-color: #218838;
}

.edit-input-direct {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  width: 100%;
  max-width: 300px;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.edit-input-direct:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.edit-select-direct {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  width: 100%;
  max-width: 300px;
  background-color: #fff;
  transition: border-color 0.3s ease;
  cursor: pointer;
}

.edit-select-direct:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.description-textarea {
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  width: 100%;
  background-color: #fff;
  transition: border-color 0.3s ease;
  resize: vertical;
  font-family: inherit;
  line-height: 1.5;
}

.description-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.edit-btn {
  background-color: #FFC107;
  color: black;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.edit-btn:hover {
  background-color: #e6af07;
}

.delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.delete-btn:hover {
  background-color: #c82333;
  transform: translateY(-1px);
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #333;
}

.delete-modal h2 {
  color: #dc3545;
  margin-bottom: 20px;
}

.warning-text {
  color: #dc3545;
  font-weight: 600;
  margin-top: 10px;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.cancel-btn:hover {
  background-color: #5a6268;
}

.confirm-delete-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.confirm-delete-btn:hover {
  background-color: #c82333;
}

@media (max-width: 768px) {
  .team-detail {
    padding: 15px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>