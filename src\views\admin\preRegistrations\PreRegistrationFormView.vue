<template>
  <div class="pre-registration-form">
    <h1>{{ isEditing ? '사전 신청서 수정' : '사전 신청서 생성' }}</h1>
    <div class="builder-container">
      <div data-config-panel>
        <h2>양식 설정</h2>
        <div class="field">
          <label>양식 이름</label>
          <input v-model="formSettings.name" type="text" placeholder="양식 이름" />
        </div>
        <div class="field">
          <label>설명</label>
          <textarea v-model="formSettings.description" placeholder="양식 설명"></textarea>
        </div>
        <div class="field">
          <label>완료 메시지</label>
          <input v-model="formSettings.completionMessage" type="text" placeholder="완료 메시지" />
        </div>
        <div class="field">
          <label><input type="checkbox" v-model="formSettings.requireConsent" /> 개인정보 동의 필수</label>
        </div>

        <!-- 개인정보 처리방침 동의 문구 입력 필드 (개인정보 동의 필수가 체크된 경우에만 표시) -->
        <div class="field" v-if="formSettings.requireConsent">
          <label>개인정보 처리방침 동의 문구</label>
          <textarea
            v-model="formSettings.privacyPolicyAgreementText"
            placeholder="개인정보 수집·이용에 동의합니다."
            rows="4"
          ></textarea>
          <div class="field-help-text">개인정보 처리방침 동의 체크박스 아래에 표시될 문구를 입력하세요.</div>
        </div>

        <!-- 자동 참가 처리 여부 처리 선택 필드 -->
        <div class="field">
          <label>자동 참가 처리 여부</label>
          <select v-model="formSettings.autoConfirmYn">
            <option value="Y">자동 처리</option>
            <option value="N">수동 처리</option>
          </select>
          <div class="field-help-text">참가자 등록 시 자동 참가 처리 방식을 선택해주세요.(수동 처리 시 관리자가 수동으로 처리해야 합니다.)</div>
        </div>

        <div class="section-divider"></div>

        <h2>항목 구성</h2>
        <div class="add-field">
          <select v-model="newFieldType">
            <option disabled value="">항목 추가</option>
            <option
              v-for="opt in fieldTypes"
              :key="opt.value"
              :value="opt.value"
              :disabled="(opt.value === 'name' && hasNameField) ||
                        (opt.value === 'tel' && hasTelField) ||
                        (opt.value === 'email' && hasEmailField)"
            >
              {{ opt.label }} {{ (opt.value === 'name' && hasNameField) ||
                               (opt.value === 'tel' && hasTelField) ||
                               (opt.value === 'email' && hasEmailField) ? '(이미 추가됨)' : '' }}
            </option>
          </select>
          <button @click="addField" :disabled="!newFieldType">추가</button>
        </div>

        <!-- 식별 필드 관련 주의 문구 표시 -->
        <div v-if="needsIdentificationWarning" class="warning-message">
          <div class="warning-icon">⚠️</div>
          <div class="warning-text">
            <strong>주의:</strong>
            <span v-if="hasNoIdentificationFields">
              이름, 연락처, 이메일 필드가 모두 없습니다.
            </span>
            <span v-else-if="hasNoRequiredIdentificationFields">
              이름, 연락처, 이메일 필드가 있지만 필수 입력으로 설정된 필드가 없습니다.
            </span>
            참석자 구분이 어려울 수 있습니다.
            최소한 하나의 식별 필드를 필수 입력으로 설정하는 것을 권장합니다.
          </div>
        </div>

        <div class="field-list">
          <TransitionGroup name="list" tag="div" class="field-list-draggable">
            <div v-for="(field, index) in addedFields" :key="field.id" class="field-item" data-field-item
                 draggable="true"
                 @dragstart="startDrag($event, index)"
                 @dragend="onDragEnd($event)"
                 @dragover.prevent
                 @dragenter="onDragEnter($event)"
                 @dragleave="onDragLeave($event)"
                 @drop="onDrop($event, index)">
              <div class="field-header">
                <div class="field-title">
                  <span class="drag-handle" title="끌어서 순서 변경">☰</span>
                  <span>항목 유형 : {{ fieldLabel(field.type) }}</span>
                </div>
                <div class="field-actions">
                  <button @click="removeField(field.id)" class="delete-btn" title="삭제">
                    <span>×</span>
                  </button>
                </div>
              </div>
              <div class="field">
                <label>항목 제목</label>
                <input v-model="field.label" type="text" />
              </div>
              <div class="field">
                <label>항목 설명</label>
                <input v-model="field.helpText" type="text" />
              </div>
              <div class="field">
                <label><input type="checkbox" v-model="field.required" />필수 입력 여부</label>
              </div>
              <div class="field" v-if="field.type==='select'">
                <label>선택 옵션</label>
                <div v-for="(opt,i) in field.options" :key="i" class="option-item">
                  <input v-model="field.options[i]" type="text" placeholder="옵션 텍스트" />
                  <button @click="removeOption(field.id,i)">-</button>
                </div>
                <button @click="addOption(field.id)">옵션 추가</button>
              </div>
            </div>
          </TransitionGroup>
        </div>
      </div>
      <div class="preview-panel">
        <h2>실시간 미리보기</h2>
        <form>
          <!-- 필드 미리보기 - 순서대로 표시 -->
          <div class="form-field" v-for="field in addedFields" :key="field.id">
            <label>{{ field.label }} <span v-if="field.required">*</span></label>
            <component :is="previewComponent(field)" :placeholder="field.helpText" v-bind="previewProps(field)">
              <option v-if="field.type==='select'" disabled value="">옵션 선택</option>
              <option v-if="field.type==='select'" v-for="(opt,i) in field.options" :key="i" :value="opt">{{ opt }}</option>
            </component>
          </div>
          <div class="form-field" v-if="formSettings.requireConsent">
            <label><input type="checkbox" /> 개인정보 수집·이용 동의</label>
            <div class="privacy-policy-text" v-if="formSettings.privacyPolicyAgreementText">
              {{ formSettings.privacyPolicyAgreementText }}
            </div>
            <div class="privacy-policy-text" v-else>
              개인정보 수집·이용에 동의합니다.
            </div>
          </div>

          <!-- 참가 여부 처리 필드 미리보기 -->
          <div class="form-field form-field-info">
            <label>참가 여부 처리</label>
            <div class="form-field-value">
              {{ formSettings.autoConfirmYn === 'Y' ? '자동 처리' : '수동 처리' }}
            </div>
            <div class="field-help-text">
              참가자 등록 시 참가 여부가 {{ formSettings.autoConfirmYn === 'Y' ? '자동으로 처리' : '수동으로 처리' }}됩니다.
            </div>
          </div>
          <div class="field">
            <button type="button" @click="showCompletionMessage" class="submit-btn">제출</button>
          </div>
        </form>
      </div>
    </div>
    <div class="actions">
      <button @click="onSave" class="save-btn">저장</button>
      <button @click="onCancel" class="cancel-btn">취소</button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { createPreRegistration, getPreRegistration, updatePreRegistration } from '@/api/preRegistrations';

function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

const route = useRoute();
const formId = route.params.formId;
const isEditing = computed(() => !!formId);

const router = useRouter();
const authStore = useAuthStore();
const currentProjectId = computed(() => authStore.currentProject?.projectId);

// 이름, 연락처, 이메일 필드 존재 여부 확인을 위한 계산된 속성
const hasNameField = computed(() => addedFields.value.some(f => f.type === 'name'));
const hasTelField = computed(() => addedFields.value.some(f => f.type === 'tel'));
const hasEmailField = computed(() => addedFields.value.some(f => f.type === 'email'));

// 필수 입력 필드 확인을 위한 계산된 속성
const hasRequiredNameField = computed(() => addedFields.value.some(f => f.type === 'name' && f.required));
const hasRequiredTelField = computed(() => addedFields.value.some(f => f.type === 'tel' && f.required));
const hasRequiredEmailField = computed(() => addedFields.value.some(f => f.type === 'email' && f.required));

// 이름, 연락처, 이메일 필드가 모두 없는지 확인하는 계산된 속성
const hasNoIdentificationFields = computed(() => !hasNameField.value && !hasTelField.value && !hasEmailField.value);

// 필드는 있지만 필수 입력이 하나도 없는지 확인하는 계산된 속성
const hasNoRequiredIdentificationFields = computed(() =>
  (hasNameField.value || hasTelField.value || hasEmailField.value) && // 필드가 하나 이상 있지만
  !hasRequiredNameField.value && !hasRequiredTelField.value && !hasRequiredEmailField.value // 필수 입력이 하나도 없음
);

// 식별 필드 관련 경고가 필요한지 확인하는 계산된 속성
const needsIdentificationWarning = computed(() =>
  hasNoIdentificationFields.value || hasNoRequiredIdentificationFields.value
);

const formSettings = reactive({
  name: '',
  description: '',
  completionMessage: '',
  requireConsent: true,
  privacyPolicyAgreementText: '',
  autoConfirmYn: 'N', // 기본값은 '수동 처리(N)'
});

const fieldTypes = [
  { label: '이름', value: 'name' },
  { label: '연락처', value: 'tel' },
  { label: '이메일', value: 'email' },
  { label: '텍스트', value: 'text' },
  { label: '숫자', value: 'number' },
  { label: '날짜', value: 'date' },
  { label: '시간', value: 'time' },
  { label: '선택박스형', value: 'select' },
];

const FIELD_TYPE_MAP = {
  text: 'TEXT',
  textarea: 'TEXTAREA',
  number: 'NUMBER',
  email: 'EMAIL',
  tel: 'TEL',
  name: 'NAME',
  date: 'DATE',
  time: 'TIME',
  select: 'SELECT',
  radio: 'RADIO',
  checkbox: 'CHECKBOX',
};

const newFieldType = ref('');
const addedFields = ref([]);
const draggedItem = ref(null); // 드래그 중인 항목 인덱스
const autoScrollActive = ref(false); // 자동 스크롤 활성화 여부
const scrollSpeed = ref(0); // 스크롤 속도
const scrollInterval = ref(null); // 스크롤 인터벌 참조

const addField = () => {
  if (!newFieldType.value) return;

  // 이미 존재하는 필드를 추가하려는 경우 경고 표시
  if ((newFieldType.value === 'name' && hasNameField.value) ||
      (newFieldType.value === 'tel' && hasTelField.value) ||
      (newFieldType.value === 'email' && hasEmailField.value)) {
    const fieldTypeLabel = fieldLabel(newFieldType.value);
    alert(`${fieldTypeLabel} 필드는 이미 존재합니다. 해당 필드는 하나만 추가할 수 있습니다.`);
    return;
  }

  const field = {
    id: generateId(),
    type: newFieldType.value,
    label: '',
    required: false,
    helpText: '',
    options: newFieldType.value === 'select' ? [''] : [],
  };

  // 이름, 연락처, 이메일 필드의 경우 기본 레이블 설정
  if (newFieldType.value === 'name') {
    field.label = '이름';
    field.helpText = '이름을 입력해주세요';
  } else if (newFieldType.value === 'tel') {
    field.label = '연락처';
    field.helpText = '연락 가능한 전화번호를 입력해주세요';
  } else if (newFieldType.value === 'email') {
    field.label = '이메일';
    field.helpText = '이메일 주소를 입력해주세요';
  }

  addedFields.value.push(field);
  newFieldType.value = '';
};

const removeField = (id) => {
  addedFields.value = addedFields.value.filter(f => f.id !== id);
};

// 드래그 앤 드롭으로 필드 순서 변경 기능 구현

// 자동 스크롤 시작
 const startAutoScroll = () => {
  if (scrollInterval.value) return; // 이미 스크롤 중이면 무시

  autoScrollActive.value = true;
  scrollInterval.value = setInterval(() => {
    if (scrollSpeed.value !== 0) {
      // 필드 리스트 컨테이너 찾기
      const container = document.querySelector('[data-config-panel]');
      if (container) {
        container.scrollTop += scrollSpeed.value;
      }
    }
  }, 16); // 60fps에 가까운 스크롤 업데이트
};

// 자동 스크롤 중지
const stopAutoScroll = () => {
  autoScrollActive.value = false;
  scrollSpeed.value = 0;

  if (scrollInterval.value) {
    clearInterval(scrollInterval.value);
    scrollInterval.value = null;
  }
};

// 드래그 중 마우스 위치에 따라 스크롤 속도 계산
const updateScrollSpeed = (event) => {
  const container = document.querySelector('[data-config-panel]');
  if (!container) return;

  const containerRect = container.getBoundingClientRect();
  const mouseY = event.clientY;

  // 상단 스크롤 영역 (20% 영역)
  const topScrollZone = containerRect.top + containerRect.height * 0.2;
  // 하단 스크롤 영역 (20% 영역)
  const bottomScrollZone = containerRect.bottom - containerRect.height * 0.2;

  if (mouseY < topScrollZone) {
    // 상단 스크롤 영역에 있을 때 - 위로 스크롤
    const distance = topScrollZone - mouseY;
    scrollSpeed.value = -Math.min(distance / 2, 10); // 최대 속도 제한
    startAutoScroll();
  } else if (mouseY > bottomScrollZone) {
    // 하단 스크롤 영역에 있을 때 - 아래로 스크롤
    const distance = mouseY - bottomScrollZone;
    scrollSpeed.value = Math.min(distance / 2, 10); // 최대 속도 제한
    startAutoScroll();
  } else {
    // 스크롤 영역 밖에 있을 때
    scrollSpeed.value = 0;
  }
};
const startDrag = (event, index) => {
  draggedItem.value = index;
  event.dataTransfer.effectAllowed = 'move';
  // Firefox에서는 데이터 전송이 필요함
  event.dataTransfer.setData('text/plain', index);
  // 드래그 중인 항목에 스타일 적용
  event.currentTarget.classList.add('dragging');

  // 드래그 이미지 투명도 설정 (선택적)
  if (event.dataTransfer.setDragImage) {
    const dragImage = event.currentTarget.cloneNode(true);
    dragImage.style.opacity = '0.5';
    dragImage.style.position = 'absolute';
    dragImage.style.top = '-1000px';
    document.body.appendChild(dragImage);
    event.dataTransfer.setDragImage(dragImage, 0, 0);
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  }

  // 자동 스크롤 시작
  startAutoScroll();

  // 스크롤 영역 표시
  const configPanel = document.querySelector('[data-config-panel]');
  if (configPanel) {
    configPanel.classList.add('dragging-active');
  }

  // 드래그 중 마우스 이동 이벤트 리스너 추가
  document.addEventListener('dragover', updateScrollSpeed);
};

// 드래그 종료 시 호출
const onDragEnd = (event) => {
  // 드래그 중인 항목 스타일 제거
  event.currentTarget.classList.remove('dragging');
  // 모든 드롭 대상 항목 스타일 제거
  document.querySelectorAll('[data-field-item]').forEach(item => {
    item.classList.remove('drop-target');
  });

  // 자동 스크롤 중지
  stopAutoScroll();

  // 스크롤 영역 표시 제거
  const configPanel = document.querySelector('[data-config-panel]');
  if (configPanel) {
    configPanel.classList.remove('dragging-active');
  }

  // 드래그오버 이벤트 리스너 제거
  document.removeEventListener('dragover', updateScrollSpeed);

  // 드래그 완료 후 초기화
  draggedItem.value = null;
};

// 드래그 요소가 드롭 대상 요소에 진입할 때 호출
const onDragEnter = (event) => {
  // 드래그 중인 항목은 드롭 대상에서 제외
  if (event.currentTarget.classList.contains('dragging')) {
    return;
  }

  // 드롭 대상 항목 스타일 추가
  event.currentTarget.classList.add('drop-target');

  // 이벤트 전파 방지
  event.stopPropagation();
};

// 드래그 요소가 드롭 대상 요소를 떠날 때 호출
const onDragLeave = (event) => {
  // 자식 요소로 이동하는 경우 무시
  if (event.currentTarget.contains(event.relatedTarget)) {
    return;
  }

  // 드롭 대상 항목 스타일 제거
  event.currentTarget.classList.remove('drop-target');
};

const onDrop = (event, index) => {
  // 드롭 대상 항목 스타일 제거
  event.currentTarget.classList.remove('drop-target');

  // 자동 스크롤 중지
  stopAutoScroll();

  // 스크롤 영역 표시 제거
  const configPanel = document.querySelector('[data-config-panel]');
  if (configPanel) {
    configPanel.classList.remove('dragging-active');
  }

  // 드래그오버 이벤트 리스너 제거
  document.removeEventListener('dragover', updateScrollSpeed);

  // 이벤트 전파 방지
  event.stopPropagation();

  // 드래그 중인 항목과 드롭 대상 항목이 다른 경우에만 처리
  if (draggedItem.value !== null && draggedItem.value !== index) {
    // 새로운 배열 생성
    const newFields = [...addedFields.value];
    // 드래그 중인 항목 저장
    const draggedField = newFields[draggedItem.value];
    // 드래그 중인 항목 제거
    newFields.splice(draggedItem.value, 1);
    // 드롭 위치에 항목 삽입
    newFields.splice(index, 0, draggedField);
    // 배열 갱신
    addedFields.value = newFields;
  }

  // 드래그 완료는 onDragEnd에서 처리
};

const addOption = (id) => {
  const field = addedFields.value.find(f => f.id === id);
  field.options.push('');
};

const removeOption = (id, index) => {
  const field = addedFields.value.find(f => f.id === id);
  field.options.splice(index, 1);
};

const fieldLabel = (type) => {
  const ft = fieldTypes.find(f => f.value === type);
  return ft ? ft.label : type;
};

const previewComponent = (field) => {
  switch (field.type) {
    case 'text':
    case 'email':
    case 'tel':
    case 'name':
    case 'number':
    case 'date':
    case 'time':
      return 'input';
    case 'select':
      return 'select';
  }
};

const previewProps = (field) => {
  if (['text', 'email', 'tel', 'number', 'date', 'time'].includes(field.type)) {
    return { type: field.type };
  } else if (field.type === 'name') {
    return { type: 'text' };
  } else if (field.type === 'select') {
    return {};
  }
  return {};
};

// Load existing form data when editing
const loadDetail = async () => {
  try {
    const data = await getPreRegistration(formId);
    formSettings.name = data.formName;
    formSettings.description = data.description;
    formSettings.completionMessage = data.completionMessage;
    formSettings.requireConsent = data.requireConsent;
    formSettings.privacyPolicyAgreementText = data.privacyPolicyAgreementText || '';
    formSettings.autoConfirmYn = data.autoConfirmYn || 'N';

    // 기본 필드를 일반 필드로 처리하도록 변경
    addedFields.value = (data.fields || []).map(f => {
      // 필드 타입 매핑 - 안전하게 처리
      let fieldType = 'text'; // 기본값으로 text 설정

      // fieldType이 존재하는 경우에만 처리
      if (f.fieldType) {
        const type = f.fieldType.toString().toUpperCase();
        if (type === 'NAME') fieldType = 'name';
        else if (type === 'TEL') fieldType = 'tel';
        else if (type === 'EMAIL') fieldType = 'email';
        else if (type === 'NUMBER') fieldType = 'number';
        else if (type === 'DATE') fieldType = 'date';
        else if (type === 'TIME') fieldType = 'time';
        else if (type === 'SELECT') fieldType = 'select';
        else fieldType = 'text';
      }

      // 레거시 데이터 처리를 위한 추가 로직
      if (f.type && !f.fieldType) {
        try {
          const legacyType = f.type.toString().toLowerCase();
          if (['text', 'email', 'tel', 'name', 'number', 'date', 'time', 'select'].includes(legacyType)) {
            fieldType = legacyType;
          }
        } catch (e) {
          console.error('Legacy field type processing error:', e);
        }
      }

      // 레이블 처리
      const label = f.fieldLabel || f.label || '';

      // 필수 여부 처리
      let required = false;
      if (f.isRequiredYn === 'Y') required = true;
      else if (f.required === true) required = true;

      return {
        id: generateId(),
        type: fieldType,
        label: label,
        required: required,
        helpText: f.helpText || '',
        options: f.options || []
      };
    });

  } catch (err) {
    alert(err.message || '상세정보 불러오기 중 오류가 발생했습니다.');
  }
};
// 기본 필드 추가 함수 (이름, 연락처, 이메일)
const addDefaultFields = () => {
  // 이름 필드 추가
  if (!hasNameField.value) {
    addedFields.value.push({
      id: generateId(),
      type: 'name',
      label: '이름',
      required: true,
      helpText: '이름을 입력해주세요',
      options: [],
    });
  }

  // 연락처 필드 추가
  if (!hasTelField.value) {
    addedFields.value.push({
      id: generateId(),
      type: 'tel',
      label: '연락처',
      required: true,
      helpText: '연락 가능한 전화번호를 입력해주세요',
      options: [],
    });
  }

  // 이메일 필드 추가
  if (!hasEmailField.value) {
    addedFields.value.push({
      id: generateId(),
      type: 'email',
      label: '이메일',
      required: true,
      helpText: '이메일 주소를 입력해주세요',
      options: [],
    });
  }
};

onMounted(() => {
  if (isEditing.value) {
    loadDetail();
  } else {
    // 새로운 폼을 생성하는 경우 기본 필드 추가
    addDefaultFields();
  }
});

const onSave = async () => {
  if (!currentProjectId.value) {
    alert('프로젝트를 선택해주세요.');
    return;
  }
  if (!formSettings.name) {
    alert('양식 이름을 입력해주세요.');
    return;
  }

  // 식별 필드 관련 경고가 필요한 경우 경고 메시지 표시
  if (needsIdentificationWarning.value) {
    let warningMessage = '주의: ';

    if (hasNoIdentificationFields.value) {
      warningMessage += '이름, 연락처, 이메일 필드가 모두 없습니다.';
    } else if (hasNoRequiredIdentificationFields.value) {
      warningMessage += '이름, 연락처, 이메일 필드가 있지만 필수 입력으로 설정된 필드가 없습니다.';
    }

    warningMessage += '\n\n참석자 구분이 어려울 수 있습니다. \n최소한 하나의 식별 필드를 필수 입력으로 설정하는 것을 권장합니다.\n\n그래도 계속하시겠습니까?';

    const confirmed = confirm(warningMessage);
    if (!confirmed) {
      return;
    }
  }
  const payload = {
    projectId: currentProjectId.value,
    formName: formSettings.name,
    description: formSettings.description,
    completionMessage: formSettings.completionMessage,
    requireConsent: formSettings.requireConsent,
    privacyPolicyAgreementText: formSettings.requireConsent ? formSettings.privacyPolicyAgreementText : null,
    autoConfirmYn: formSettings.autoConfirmYn,
    // 기본 필드를 일반 필드로 처리하도록 변경
    // 현재 필드 순서를 그대로 서버에 전송
    fields: addedFields.value.map((f, idx) => {
      // 필드 타입 안전하게 처리
      let fieldType = 'TEXT';
      try {
        if (f.type && FIELD_TYPE_MAP[f.type]) {
          fieldType = FIELD_TYPE_MAP[f.type];
        } else if (f.type) {
          fieldType = f.type.toUpperCase();
        }
      } catch (e) {
        console.error('Field type processing error:', e);
      }

      return {
        fieldLabel: f.label || '',
        fieldName: f.id,
        fieldType: fieldType,
        isRequiredYn: f.required ? 'Y' : 'N',
        helpText: f.helpText || null,
        options: f.type === 'select' ? f.options.filter(o => o) : [],
        displayOrder: idx,
        createUserEmail: authStore.user.userEmail,
        useYn: 'Y',
        deleteYn: 'N',
      };
    }),
  };
  try {
    if (isEditing.value) {
      await updatePreRegistration(formId, payload);
    } else {
      await createPreRegistration(payload);
    }
    router.push({ name: 'pre-registration-management' });
  } catch (err) {
    alert(err.message || '저장 중 오류가 발생했습니다.');
  }
};

const onCancel = () => {
  router.push({ name: 'pre-registration-management' });
};

// 미리보기 폼에서 제출 버튼 클릭 시 완료 메시지 표시
const showCompletionMessage = () => {
  // 완료 메시지가 있는 경우 해당 메시지 표시, 없는 경우 기본 메시지 표시
  const message = formSettings.completionMessage || '등록이 완료되었습니다.';
  alert(message);
};
</script>

<style scoped>
.pre-registration-form {
  padding: 20px;
}

h1 {
  margin-bottom: 24px;
  color: #333;
}

h2 {
  margin-bottom: 16px;
  color: #444;
  font-size: 1.2rem;
}

.builder-container {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

[data-config-panel],
.preview-panel {
  flex: 1;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 8px;
  max-height: 70vh;
  overflow: auto;
  position: relative;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 스크롤 영역 표시를 위한 가상 요소 */
[data-config-panel]::before,
[data-config-panel]::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 5;
}

[data-config-panel]::before {
  top: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
}

[data-config-panel]::after {
  bottom: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.05), transparent);
}

/* 드래그 중에만 스크롤 영역 표시 */
[data-config-panel].dragging-active::before,
[data-config-panel].dragging-active::after {
  opacity: 1;
}

.section-divider {
  height: 1px;
  background-color: #eee;
  margin: 20px 0;
}

.field {
  margin-bottom: 16px;
}

.field label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: #555;
}

.field input,
.field textarea,
.field select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.field input:focus,
.field textarea:focus,
.field select:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.add-field {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 16px;
}

.add-field select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.add-field button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.add-field button:hover:not(:disabled) {
  background-color: #45a049;
}

.add-field button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.field-list .field-item {
  border: 1px solid #eee;
  background-color: #f9f9f9;
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.field-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.field-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
}

.field-actions {
  display: flex;
  gap: 4px;
}

.drag-handle {
  cursor: move;
  font-size: 18px;
  color: #888;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.drag-handle:hover {
  background-color: #f0f0f0;
  color: #333;
}

.delete-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: none;
  background-color: #ffebee;
  color: #d32f2f;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

.field-list-draggable {
  width: 100%;
}

/* 드래그 앤 드롭 애니메이션 */
.list-move, /* 이동 애니메이션 적용 */
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 드래그 중인 항목 스타일 */
.field-item.dragging {
  opacity: 0.5;
  border: 2px dashed #1976d2;
  background-color: #e3f2fd;
}

/* 드롭 대상 항목 스타일 */
.field-item.drop-target {
  border: 2px dashed #388e3c;
  box-shadow: 0 0 10px rgba(56, 142, 60, 0.3);
  position: relative;
  z-index: 1;
  transition: all 0.2s ease;
}

/* 드롭 대상 항목 후반부 표시 */
.field-item.drop-target::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(56, 142, 60, 0.05);
  pointer-events: none;
  z-index: -1;
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.option-item input {
  flex: 1;
}

.option-item button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: none;
  background-color: #ffebee;
  color: #d32f2f;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.option-item button:hover {
  background-color: #ffcdd2;
}

.form-field {
  margin-bottom: 16px;
}

.form-field label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
  color: #555;
}

.form-field input,
.form-field select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #F44336;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s;
}

.cancel-btn:hover {
  background-color: #d32f2f;
}

/* 미리보기 폼 제출 버튼 스타일 */
.field .submit-btn {
  width: 100%;
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.field .submit-btn:hover {
  background-color: #45a049;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 개인정보 처리방침 관련 스타일 */
.field-help-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  font-style: italic;
}

.privacy-policy-text {
  margin-top: 8px;
  padding: 12px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  color: #555;
  line-height: 1.5;
}

/* 참가 여부 처리 필드 미리보기 스타일 */
.form-field-info {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  margin-bottom: 16px;
}

.form-field-value {
  font-weight: 500;
  color: #495057;
  padding: 8px 0;
}

/* 경고 메시지 스타일 */
.warning-message {
  display: flex;
  align-items: flex-start;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.warning-text {
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

/* 반응형 스타일 */
@media (max-width: 768px) {
  .builder-container {
    flex-direction: column;
  }

  [data-config-panel],
  .preview-panel {
    max-height: none;
    width: 100%;
  }

  .add-field {
    flex-direction: column;
    align-items: stretch;
  }

  .add-field select,
  .add-field button {
    width: 100%;
  }

  .actions {
    flex-direction: column;
  }

  .save-btn,
  .cancel-btn {
    width: 100%;
  }
}
</style>
