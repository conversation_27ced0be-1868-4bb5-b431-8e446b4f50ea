<template>
  <div class="tree-container">
    <!-- 메뉴 데이터 상태 표시 -->
    <div v-if="menuTreeData.length === 0" class="no-menu-data">
      <p>등록된 메뉴가 없습니다.</p>
      <p class="sub-text">새 메뉴를 추가하여 시작하세요.</p>
    </div>

    <!-- 메뉴 리스트 (드래그 앤 드롭 가능) -->
    <div v-else>
      <VueDraggableNext
        :model-value="menuTreeData"
        @update:model-value="handleMenuTreeUpdate"
        class="menu-list"
        :group="{ name: 'menu-items' }"
        :animation="200"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
        drag-class="drag-item"
        @change="$emit('menu-order-change', $event)"
        @start="$emit('drag-start', $event)"
        @end="$emit('drag-end', $event)"
      >
        <div
          v-for="element in menuTreeData"
          :key="element.id"
          class="menu-item-wrapper"
          :data-menu-id="element.id"
        >
          <!-- 최상위 메뉴 -->
          <MenuItem
            :menu-item="element"
            :is-sub-menu="false"
            @toggle-node="$emit('toggle-node', $event)"
            @toggle-visibility="$emit('toggle-visibility', $event)"
            @edit-menu="$emit('edit-menu', $event)"
            @delete-menu="$emit('delete-menu', $event)"
          />

          <!-- 하위 메뉴들 (펼쳐진 경우 항상 드롭 존 표시) -->
          <div v-if="element.expanded" class="sub-menu-list">
            <VueDraggableNext
              :model-value="element.nodes"
              @update:model-value="(newNodes) => handleSubMenuUpdate(element, newNodes)"
              class="sub-menu-container"
              :group="{ name: 'menu-items' }"
              :animation="200"
              ghost-class="ghost-item"
              chosen-class="chosen-item"
              drag-class="drag-item"
              @change="$emit('sub-menu-order-change', $event)"
              @start="$emit('drag-start', $event)"
              @end="$emit('drag-end', $event)"
            >
              <MenuItem
                v-for="subElement in element.nodes"
                :key="subElement.id"
                :menu-item="subElement"
                :is-sub-menu="true"
                :data-menu-id="subElement.id"
                @toggle-visibility="$emit('toggle-visibility', $event)"
                @edit-menu="$emit('edit-menu', $event)"
                @delete-menu="$emit('delete-menu', $event)"
              />
            </VueDraggableNext>
          </div>
        </div>
      </VueDraggableNext>
    </div>
  </div>
</template>

<script setup>
import { VueDraggableNext } from 'vue-draggable-next';
import MenuItem from './MenuItem.vue';

const props = defineProps({
  menuTreeData: {
    type: Array,
    required: true
  }
});

const emit = defineEmits([
  'menu-order-change',
  'sub-menu-order-change',
  'drag-start',
  'drag-end',
  'toggle-node',
  'toggle-visibility',
  'edit-menu',
  'delete-menu',
  'update:menuTreeData'
]);

// 메인 메뉴 트리 업데이트 핸들러
const handleMenuTreeUpdate = (newMenuTreeData) => {
  emit('update:menuTreeData', newMenuTreeData);
};

// 하위 메뉴 업데이트 핸들러
const handleSubMenuUpdate = (parentElement, newNodes) => {
  // 부모 요소의 nodes를 업데이트
  parentElement.nodes = newNodes;
  // 전체 메뉴 트리 데이터 업데이트를 부모에게 알림
  emit('update:menuTreeData', props.menuTreeData);
};
</script>

<style scoped>
.tree-container {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  min-height: 400px;
}

.no-menu-data {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 40px;
  margin-bottom: 20px;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.no-menu-data p {
  color: #666;
  font-size: 16px;
  margin-bottom: 10px;
}

.no-menu-data .sub-text {
  color: #999;
  font-size: 14px;
  margin-bottom: 0;
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 100px;
}

.menu-item-wrapper {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  cursor: move;
  transition: all 0.2s ease;
}

.menu-item-wrapper:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sub-menu-list {
  background: #f8f9fa;
}

.sub-menu-container {
  min-height: 50px;
}

/* 드래그 상태 스타일 */
.ghost-item {
  opacity: 0.5;
  background: #f8f9fa;
  border: 2px dashed #007bff;
  transform: rotate(2deg);
}

.chosen-item {
  background: #e3f2fd;
  border: 2px solid #2196f3;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.drag-item {
  transform: rotate(5deg);
  opacity: 0.8;
  z-index: 1000;
}

/* 드래그 핸들 스타일 */
.menu-item-wrapper::before {
  content: "⋮⋮";
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  font-size: 12px;
  line-height: 1;
  pointer-events: none;
  z-index: 1;
}

/* 드롭 존 하이라이트 */
.menu-list:empty::before,
.sub-menu-container:empty::before {
  content: "여기에 메뉴를 드롭하세요";
  display: block;
  padding: 20px;
  text-align: center;
  color: #999;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  background: #f8f9fa;
}

.menu-list.sortable-ghost::before,
.sub-menu-container.sortable-ghost::before {
  border-color: #007bff;
  color: #007bff;
  background: #e3f2fd;
}
</style>
