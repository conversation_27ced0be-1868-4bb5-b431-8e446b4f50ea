import { nextTick } from 'vue';
import QRCodeStyling from 'qr-code-styling';
import { calculateScanReliability } from '@/utils/qrFormUtils';

export function useQrGeneration() {
  // QR 코드 미리보기 생성 (qr-code-styling 사용)
  const generateClientSideQrCode = async (
    qrCodePreviewContainer,
    formData,
    excelSampleQrData,
    selectedEventId,
    isEditMode,
    isDesignUpdateOnly,
    initialTargetContent,
    getFrontendDomain,
    qrColor,
    qrBgColor,
    qrEyeColor,
    qrEyeStyle,
    qrDotsStyle,
    qrErrorCorrectionLevel,
    logoPreview,
    logoSize,
    qrCodeInstance,
    qrVersion,
    scanReliability,
    isQrCodeGenerated,
    useA4Canvas,
    calculateQrVersion,
    calculateLogoSize,
    forceQrContainerSize
  ) => {
    if (!qrCodePreviewContainer.value) {
      return;
    }

    // 엑셀 샘플 데이터가 있는 경우 사용하거나, 이벤트 타입일 때 full URL 사용, 기타 타입은 targetContent 사용
    let contentToUse = '';
    let qrTypeToUse = formData.value.qrType;
    
    // 엑셀 샘플 데이터가 활성화된 경우 (엑셀 등록 후)
    if (excelSampleQrData.value.isActive && excelSampleQrData.value.targetContent) {
      contentToUse = excelSampleQrData.value.targetContent;
      qrTypeToUse = excelSampleQrData.value.qrType;
    }
    // 엑셀 샘플 데이터가 없는 일반 경우
    else if (formData.value.qrType === 'EVENT_ATTENDANCE') {
      if (!selectedEventId.value) {
        // 이벤트 미선택 시 미리보기 제거
        if (qrCodeInstance.value) {
          while (qrCodePreviewContainer.value.firstChild) {
            qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
          }
          qrCodeInstance.value = null;
        }
        return;
      }
      contentToUse = `${getFrontendDomain()}/event/${selectedEventId.value}`;
    } else {
      if (!formData.value.targetContent) {
        if (qrCodeInstance.value) {
          while (qrCodePreviewContainer.value.firstChild) {
            qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
          }
          qrCodeInstance.value = null;
        }
        return;
      }
      contentToUse = (isEditMode.value && isDesignUpdateOnly.value && initialTargetContent.value)
        ? initialTargetContent.value
        : formData.value.targetContent;
    }

    // 오류 복원 수준에 따라 QR 코드 버전 계산
    const calculatedVersion = calculateQrVersion(contentToUse, qrErrorCorrectionLevel.value);
    qrVersion.value = calculatedVersion; // 계산된 버전 적용
    
    // QR 코드 크기 고정 (300x300)
    const qrSize = 300;

    const logoSizeForLibrary = calculateLogoSize();

    // QR 코드 스타일링 라이브러리용 옵션 구성
    const baseDesignOptions = {
      width: qrSize,
      height: qrSize,
      data: contentToUse, // 엑셀 샘플 데이터 또는 일반 데이터
      margin: 0,
      dotsOptions: {
        color: qrColor.value,
        type: qrDotsStyle.value === 0 ? 'square' : 'dots'
      },
      backgroundOptions: {
        color: qrBgColor.value,
      },
      cornersSquareOptions: {
          color: qrEyeColor.value,
          type: qrEyeStyle.value === 0 ? 'square' : 'dot', // 'square' | 'dot' | 'extra-rounded'
      },
      cornersDotOptions: {
          color: qrEyeColor.value,
          type: qrEyeStyle.value === 0 ? undefined : 'dot' // Only apply type if dots selected for eye frame
      },
      image: logoPreview.value ? logoPreview.value : null,
      imageOptions: logoPreview.value ? {
        // 로고 크기를 원본 이미지 크기 대비 %로 계산
        // 오류 복원 수준에 관계없이 동일한 크기로 표시
        imageSize: logoSizeForLibrary,
        margin: 2, // 로고 주변 여백은 유지
        hideBackgroundDots: true
      } : {},
      // 오류 복원 수준 적용
      qrOptions: {
        errorCorrectionLevel: qrErrorCorrectionLevel.value, // L, M, Q, H
        typeNumber: calculatedVersion > 0 ? calculatedVersion : 0, // 0이면 자동 계산
        mode: 'Byte',
      },
    };

    try {
      if (!qrCodeInstance.value) {
        // 컨테이너 비우기 (혹시 남아있을 수 있는 이전 요소 제거)
        while (qrCodePreviewContainer.value.firstChild) {
          qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
        }
        qrCodeInstance.value = new QRCodeStyling(baseDesignOptions);
        await qrCodeInstance.value.append(qrCodePreviewContainer.value);
      } else {
        await qrCodeInstance.value.update(baseDesignOptions);
      }
      
      // QR 코드 생성/업데이트 후 스캔율 계산
      scanReliability.value = calculateScanReliability(
        qrErrorCorrectionLevel.value,
        !!logoPreview.value,
        logoSize.value
      );
      isQrCodeGenerated.value = true; // 생성/업데이트 성공 시 플래그 설정

      // QR 코드 이미지를 A4 캔버스에 표시하기 위한 로직
      // 현재 미리보기에 생성된 QR 코드 이미지 생성
      nextTick(() => {
        // A4 캔버스 사용 시 QR 코드 컨테이너 크기 강제 설정 (초기화 시에만)
        if (useA4Canvas.value && !isDesignUpdateOnly.value) {
          forceQrContainerSize();
        }

        // generateClientSideQrCode에서 QR 코드가 생성되면, 50ms 뒤에 QR 코드 이미지를 가져옴
        setTimeout(() => {
          try {
            // QR 코드 이미지 엘리먼트 찾기
            const qrElement = qrCodePreviewContainer.value?.querySelector('canvas') || qrCodePreviewContainer.value?.querySelector('svg');

            if (qrElement) {
              // 캠버스가 있으면 그대로 dataURL로 변환
              if (qrElement.tagName === 'CANVAS') {
                formData.value.generatedQrDataUrl = qrElement.toDataURL('image/png');
              }
              // SVG의 경우
              else if (qrElement.tagName === 'SVG') {
                // SVG를 XML 문자열로 변환
                const serializer = new XMLSerializer();
                const svgStr = serializer.serializeToString(qrElement);
                // base64로 인코딩
                formData.value.generatedQrDataUrl = 'data:image/svg+xml;base64,' + btoa(svgStr);
              }

            } else {
              console.warn('QR 코드 이미지 엘리먼트를 찾을 수 없습니다.');
            }
          } catch (err) {
            console.error('QR 코드 이미지 처리 오류:', err);
          }
        }, 50); // 렌더링 시간을 위한 지연
      });

    } catch (error) {
       console.error("QR 코드 생성/업데이트 실패:", error);
       // 미리보기 영역에 오류 메시지 표시 등 처리 가능
       qrCodePreviewContainer.value.innerHTML = '<p style="color: red; text-align: center; padding: 10px;">QR 코드 미리보기 생성 중 오류가 발생했습니다.</p>';
       qrCodeInstance.value = null; // 오류 발생 시 인스턴스 초기화
    }
  };

  // 디바운스된 QR 코드 생성 함수 (디자인 변경 시 사용)
  const createGenerateQrCodeDebounced = (generateClientSideQrCodeFn) => {
    let timeoutId;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        generateClientSideQrCodeFn();
      }, 300); // 300ms 디바운스
    };
  };

  // QR 코드 컨테이너 크기 강제 설정 함수 (초기화 시에만 사용)
  const forceQrContainerSize = () => {
    // vue-draggable-resizable이 자체적으로 처리하도록 함
    // DOM 조작 제거
  };

  return {
    generateClientSideQrCode,
    createGenerateQrCodeDebounced,
    forceQrContainerSize
  };
}
