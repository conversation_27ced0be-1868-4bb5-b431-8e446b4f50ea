import axios from 'axios';
import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';

const apiQrQuizMappingClient = axios.create({
  baseURL: 'http://192.168.0.227:8082/api/public',
  headers: {
    'X-API-Key': 'WayQRConnect',
    'Content-Type': 'application/json',
  },
});

/**
 * QR ID 목록으로 연결된 과제 정보를 가져옵니다.
 * @param {string[]} qrCodeIds - QR ID 배열
 * @returns {Promise} - Axios 응답 Promise
 */
export const getQrQuizMappingListByQrCodeIds = (qrCodeIds) => {
  return apiQrQuizMappingClient.get('/qr-quiz-mapping-list', {
    params: {
      qrCodeIds: qrCodeIds.join(','),
      sortBy: 'displayOrder',
      activeOnly: true,
    },
  });
};

/**
 * QR 코드에 문제를 연결합니다.
 * @param {number} qrCodeId - QR 코드 ID
 * @param {number} quizId - 연결할 문제 ID
 * @returns {Promise<any>} 성공 시 응답 데이터, 실패 시 에러 발생
 */
export const linkQuizToQrCode = async (qrCodeId, quizId) => {
  try {
    if (!qrCodeId || !quizId) {
      throw new Error('QR 코드 ID와 문제 ID가 모두 필요합니다.');
    }
    
    const response = await apiClient.post(`/qr-codes/${qrCodeId}/link-quiz`, { quizId });

    if (response.data && response.data.success) {
      return response.data;
    } else {
      throw new Error(response.data?.message || '문제 연결에 실패했습니다.');
    }
  } catch (error) {
    const errorMessage = handleApiError(error, '문제 연결 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * QR 코드와 연결된 문제를 해제합니다.
 * @param {number} qrCodeId - QR 코드 ID
 * @returns {Promise<boolean>} 성공 시 true, 실패 시 에러 발생
 */
export const unlinkQuizFromQrCode = async (qrCodeId, quizId) => {
  try {
    if (!qrCodeId || !quizId) {
      throw new Error('QR 코드 ID와 퀴즈 ID가 모두 필요합니다.');
    }

    const payload = {
      qrCodeId,
      quizId,
    };

    const response = await apiClient.post('/qr-codes/unlink-quiz', payload);

    if (response.data && response.data.success) {
      return true;
    } else {
      throw new Error(response.data?.message || '문제 연결 해제에 실패했습니다.');
    }
  } catch (error) {
    const errorMessage = handleApiError(error, '문제 연결 해제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};