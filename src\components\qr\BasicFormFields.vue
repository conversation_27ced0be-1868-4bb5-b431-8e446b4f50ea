<template>
  <div class="basic-form-fields">
    <!-- QR 코드 이름 -->
    <div class="form-group">
      <label for="qrName">QR 코드 이름 <span class="required">*</span></label>
      <input
        type="text"
        id="qrName"
        v-model="formData.qrName"
        required
        placeholder="QR 코드 이름을 입력하세요"
        :disabled="isEditMode"
      />
      <div v-if="isEditMode" class="field-note">수정 모드에서는 QR 코드 미리보기만 가능합니다.</div>
    </div>

    <!-- QR 코드 타입 -->
    <div class="form-group">
      <label for="qrType">QR 코드 타입 <span class="required">*</span></label>
      <select 
        id="qrType" 
        v-model="formData.qrType" 
        :required="!excelFile" 
        :disabled="isEditMode || excelFile"
        @change="handleQrTypeChange"
      >
        <option value="">{{ excelFile ? '엑셀 파일에서 등록됩니다' : '선택하세요' }}</option>
        <option value="URL">URL</option>
        <option value="TEXT">텍스트</option>
        <option value="SNS_LINK">SNS</option>
        <option value="WIFI">Wi-Fi</option>
        <option value="LOCATION">위치</option>
        <option value="LANDING_PAGE">랜딩페이지</option>
        <option value="EVENT_ATTENDANCE">이벤트</option>
      </select>
      <div v-if="isEditMode" class="field-note">QR 코드 타입은 생성 후 변경할 수 없습니다.</div>
    </div>

    <!-- 타겟 콘텐츠 (특정 타입이 아닐 때만 표시) -->
    <div 
      class="form-group" 
      v-if="!isSpecialQrType"
    >
      <label for="targetContent">타겟 콘텐츠 <span class="required">*</span></label>
      <textarea
        id="targetContent"
        v-model="formData.targetContent"
        :required="!excelFile"
        :placeholder="excelFile ? '엑셀 파일에서 등록됩니다' : 'QR 코드에 포함될 내용을 입력하세요'"
        rows="4"
        :disabled="excelFile"
        :class="{ 'input-error': isUrlType && formData.targetContent && !isValidTargetContent }"
      ></textarea>
      <div v-if="isUrlType && formData.targetContent && !isValidTargetContent" class="error-message">
        유효한 URL 형식이 아닙니다. (예: https://example.com)
      </div>
      <div class="field-note" v-if="formData.qrType === 'URL'">
        http:// 또는 https:// 를 포함한 전체 URL을 입력해주세요.
      </div>
      <div class="field-note" v-if="formData.qrType === 'SNS_LINK'">
        연결할 SNS 페이지의 전체 URL을 입력해주세요. (예: https://www.instagram.com/username)
      </div>
      <div class="field-note" v-if="formData.qrType === 'TEXT'">
        QR 코드에 포함될 텍스트를 입력해주세요.
      </div>
    </div>

    <!-- 상태 -->
    <div class="form-group">
      <label for="status">상태 <span class="required">*</span></label>
      <select id="status" v-model="formData.status" required>
        <option value="">선택하세요</option>
        <option value="ACTIVE">활성</option>
        <option value="INACTIVE">비활성</option>
      </select>
    </div>

    <!-- 유효 시작일 -->
    <div class="form-group date-group">
      <label for="validFromDate">유효 시작일</label>
      <input
        type="datetime-local"
        id="validFromDate"
        v-model="formData.validFromDate"
      />
    </div>

    <!-- 유효 종료일 -->
    <div class="form-group date-group">
      <label for="validToDate">유효 종료일</label>
      <input
        type="datetime-local"
        id="validToDate"
        v-model="formData.validToDate"
      />
    </div>

    <!-- 설명 -->
    <div class="form-group">
      <label for="description">설명</label>
      <textarea
        id="description"
        v-model="formData.description"
        placeholder="QR 코드에 대한 설명을 입력하세요"
        rows="3"
      ></textarea>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props 정의
const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  excelFile: {
    type: Object,
    default: null
  }
});

// Emits 정의
const emit = defineEmits(['qr-type-change']);

// Computed 속성들
const isSpecialQrType = computed(() => {
  const specialTypes = ['LANDING_PAGE', 'EVENT_ATTENDANCE', 'LOCATION', 'WIFI'];
  return specialTypes.includes(props.formData.qrType);
});

const isUrlType = computed(() => {
  return props.formData.qrType === 'URL' || props.formData.qrType === 'SNS_LINK';
});

const isValidTargetContent = computed(() => {
  if (isUrlType.value) {
    return isValidUrl(props.formData.targetContent);
  }
  return true;
});

// 유틸리티 함수들
const isValidUrl = (url) => {
  return url && (url.startsWith('http://') || url.startsWith('https://'));
};

// 이벤트 핸들러
const handleQrTypeChange = () => {
  emit('qr-type-change', props.formData.qrType);
};
</script>

<style scoped>
.basic-form-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.required {
  color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group input:disabled,
.form-group select:disabled,
.form-group textarea:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.field-note {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.field-note.error {
  color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

.input-error {
  border-color: #e74c3c !important;
}

.date-group input {
  max-width: 300px;
}

textarea {
  resize: vertical;
  min-height: 80px;
}
</style>
