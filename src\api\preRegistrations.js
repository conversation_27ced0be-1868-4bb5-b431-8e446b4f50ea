import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 프로젝트의 사전 신청서 목록을 가져옵니다.
 * @param {number|string} projectId - 프로젝트 ID
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {object} searchParams - 검색 파라미터 (searchType, searchKeyword)
 * @returns {Promise<Object>} 사전 신청서 목록을 포함한 응답 객체 Promise
 */
export const getPreRegistrations = async (projectId, page = 0, size = 10, searchParams = {}) => {
  try {
    if (!projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/pre-registration-forms/list/${projectId}`, { params }),
      '사전 신청서 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사전 신청서 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN용 모든 사전 신청서 목록을 가져옵니다.
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {number} [projectId] - 선택적 프로젝트 ID (특정 프로젝트로 필터링하는 경우)
 * @param {object} searchParams - 검색 파라미터 (searchType, searchKeyword)
 * @returns {Promise<Object>} 사전 신청서 목록을 포함한 응답 객체 Promise
 */
export const getAllPreRegistrations = async (page = 0, size = 10, projectId = null, searchParams = {}) => {
  try {
    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 프로젝트 ID가 있는 경우에만 파라미터에 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/super/pre-registration-forms/all`, { params }),
      '사전 신청서 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '사전 신청서 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 사전 신청서를 생성합니다.
 * @param {object} data - 사전 신청서 데이터
 * @returns {Promise<object>} 생성된 사전 신청서 정보
 */
export const createPreRegistration = async (data) => {
  try {
    if (!data.projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }
    const response = await apiClient.post('/pre-registration-forms', data);
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data?.error?.message || '사전 신청서 생성에 실패했습니다.');
    }
  } catch (error) {
    const msg = handleApiError(error, '사전 신청서 생성 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

/**
 * 단일 사전 신청서 상세 조회
 * @param {string|number} formId - 양식 ID
 * @returns {Promise<object>} 사전 신청서 정보
 */
export const getPreRegistration = async (formId) => {
  try {
    if (!formId) {
      throw new Error('양식 ID가 필요합니다.');
    }
    const response = await apiClient.get(`/pre-registration-forms/${formId}`);
    const raw = response.data.data;
    // 서버 DDL 구조에 맞춰 필드 매핑
    return {
      ...raw,
      id: raw.formId,
      fields: (raw.fields || []).map(f => ({
        fieldId: f.fieldId,
        formId: f.formId,
        type: f.fieldType,
        label: f.fieldLabel,
        name: f.fieldName,
        required: f.isRequiredYn === 'Y',
        helpText: f.helpText,
        options: f.options ? JSON.parse(f.options) : [],
        displayOrder: f.displayOrder,
        createUserEmail: f.createUserEmail,
        createDate: f.createDate,
        updateUserEmail: f.updateUserEmail,
        lastUpdateDate: f.lastUpdateDate,
      })),
    };
  } catch (error) {
    const msg = handleApiError(error, '사전 신청서 상세 조회 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

/**
 * 사전 신청서 수정
 * @param {string|number} formId - 양식 ID
 * @param {object} data - 수정할 데이터
 * @returns {Promise<object>} 수정된 사전 신청서 정보
 */
export const updatePreRegistration = async (formId, data) => {
  try {
    if (!formId) {
      throw new Error('양식 ID가 필요합니다.');
    }
    const response = await apiClient.put(`/pre-registration-forms/${formId}`, data);
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data?.error?.message || '사전 신청서 수정에 실패했습니다.');
    }
  } catch (error) {
    const msg = handleApiError(error, '사전 신청서 수정 중 오류가 발생했습니다.');
    throw new Error(msg);
  }
};

/**
 * 사전 신청서 삭제
 * @param {string|number} formId - 양식 ID
 * @returns {Promise<void>}
 */
export const deletePreRegistration = async (formId) => {
  try {
    if (!formId) {
      throw new Error('양식 ID가 필요합니다.');
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.delete(`/pre-registration-forms/${formId}`),
      '사전 신청서 삭제에 실패했습니다.'
    );

    return response;
  } catch (error) {
    const errorMessage = handleApiError(error, '사전 신청서 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 사전 신청서를 복사합니다.
 * @param {number|string} landingPageId - 복사할 사전 신청서 ID
 * @returns {Promise<Object>} 복사된 사전 신청서 정보 또는 성공 여부
 */
export const copyPreRegistration = async (preRegistrationId) => {
  try {
    if (!preRegistrationId) {
      throw new Error('사전 신청서 ID가 필요합니다.');
    }

    const response = await apiClient.post(`/pre-registration-forms/${preRegistrationId}/copy`);
    if (response.data && response.data.success) {
      return response.data.data || response.data;
    }
    throw new Error(response.data?.error?.message || response.data?.message || '사전 신청서 복사에 실패했습니다.');
  } catch (error) {
    const errorMessage = handleApiError(error, '사전 신청서 복사에 실패했습니다.');
    throw new Error(errorMessage);
  }
};
