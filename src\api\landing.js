import apiClient from './index';
import { handleApiError } from '@/utils/errorHandler';
import { executeApiCall } from '@/utils/apiUtils';

/**
 * 프로젝트의 랜딩 페이지 목록을 가져옵니다.
 * @param {number} projectId - 프로젝트 ID
 * @returns {Promise<Object>} 랜딩 페이지 목록을 포함한 응답 객체 Promise
 */
export const getLandingPages = async (projectId, page = 0, size = 10, searchParams = {}) => {
  try {
    // 프로젝트 ID가 없는 경우 에러 발생
    if (!projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    // 새로운 서버 응답 구조: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    const response = await executeApiCall(
      () => apiClient.get(`/landing-pages/list/${projectId}`, { params }),
      '랜딩 페이지 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '랜딩 페이지 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * SUPER_ADMIN용 모든 랜딩 페이지 목록을 가져옵니다.
 * @param {number} page - 페이지 번호 (0부터 시작)
 * @param {number} size - 페이지당 항목 수
 * @param {number} [projectId] - 선택적 프로젝트 ID (특정 프로젝트로 필터링하는 경우)
 * @returns {Promise<Object>} 랜딩 페이지 목록을 포함한 응답 객체 Promise
 */
export const getAllLandingPages = async (page = 0, size = 10, projectId = null, searchParams = {}) => {
  try {

    // 페이지네이션 파라미터 추가
    const params = {
      page,
      size,
      sort: 'createDate,desc'
    };

    // 프로젝트 ID가 있는 경우에만 파라미터에 추가
    if (projectId) {
      params.projectId = projectId;
    }

    // 검색 파라미터 추가
    if (searchParams.searchType && searchParams.searchKeyword) {
      params.searchType = searchParams.searchType;
      params.searchKeyword = searchParams.searchKeyword;
    }

    // 공통 API 호출 함수를 사용하여 요청 처리
    const response = await executeApiCall(
      () => apiClient.get(`/super/landing-pages/all`, { params }),
      '랜딩 페이지 목록을 가져오는 데 실패했습니다.'
    );

    // 전체 응답 구조 반환 (success, data 필드 포함)
    return response;
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '랜딩 페이지 목록을 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 랜딩 페이지 상세 정보를 가져옵니다.
 * @param {number} landingPageId - 랜딩 페이지 ID
 * @returns {Promise<object>} 랜딩 페이지 상세 정보 Promise
 */
export const getLandingPageById = async (landingPageId) => {
  try {
    // 랜딩 페이지 ID가 없는 경우 에러 발생
    if (!landingPageId) {
      throw new Error('랜딩 페이지 ID가 필요합니다.');
    }

    // API 호출 및 응답 처리
    const response = await apiClient.get(`/landing-pages/${landingPageId}`);

    // 응답 구조 확인
    if (response.data && response.data.success === true) {
      // 전체 응답 구조 반환 (success, data 필드 포함)
      return response.data;
    } else {
      throw new Error('서버 응답 구조가 올바르지 않습니다.');
    }
  } catch (error) {
    console.error(`랜딩 페이지 상세 정보 오류:`, error);
    const errorMessage = handleApiError(error, '랜딩 페이지 상세 정보를 가져오는 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 새 랜딩 페이지를 생성합니다.
 * @param {object} landingPageData - 랜딩 페이지 데이터 (JSON 객체)
 * @returns {Promise<object>} 성공 시 생성된 랜딩 페이지 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const createLandingPage = async (landingPageData) => {
  try {
    // 프로젝트 ID가 없는 경우 에러 발생
    if (!landingPageData.projectId) {
      throw new Error('프로젝트 ID가 필요합니다.');
    }

    // 서버에서 요구하는 정확한 Content-Type 헤더 설정
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await apiClient.post('/landing-pages', landingPageData, config);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data.data; // 성공 응답의 data 필드 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.error?.message || '랜딩 페이지 생성에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '랜딩 페이지 생성 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 랜딩 페이지를 수정합니다.
 * @param {number} landingPageId - 수정할 랜딩 페이지 ID
 * @param {object} landingPageData - 랜딩 페이지 데이터 (JSON 객체)
 * @returns {Promise<object>} 성공 시 수정된 랜딩 페이지 정보 또는 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const updateLandingPage = async (landingPageId, landingPageData) => {
  try {
    // 랜딩 페이지 ID가 없는 경우 에러 발생
    if (!landingPageId) {
      throw new Error('랜딩 페이지 ID가 필요합니다.');
    }

    // 서버에서 요구하는 정확한 Content-Type 헤더 설정
    const config = {
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const response = await apiClient.put(`/landing-pages/${landingPageId}`, landingPageData, config);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data.data; // 성공 응답의 data 필드 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.error?.message || '랜딩 페이지 수정에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '랜딩 페이지 수정 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 랜딩 페이지를 삭제합니다.
 * @param {number} landingPageId - 삭제할 랜딩 페이지 ID
 * @returns {Promise<object>} 성공 시 성공 메시지 포함 객체, 실패 시 에러 발생
 */
export const deleteLandingPage = async (landingPageId) => {
  try {
    // 랜딩 페이지 ID가 없는 경우 에러 발생
    if (!landingPageId) {
      throw new Error('랜딩 페이지 ID가 필요합니다.');
    }

    const response = await apiClient.delete(`/landing-pages/${landingPageId}`);

    // API 응답 구조 확인 및 반환 (성공 여부 확인)
    if (response.data && response.data.success) {
      return response.data; // 성공 응답 전체 반환
    } else {
      // 백엔드가 success: false 또는 예상치 못한 구조로 응답한 경우
      throw new Error(response.data?.error?.message || '랜딩 페이지 삭제에 실패했습니다.');
    }
  } catch (error) {
    // 공통 에러 처리 유틸리티 사용
    const errorMessage = handleApiError(error, '랜딩 페이지 삭제 중 오류가 발생했습니다.');
    throw new Error(errorMessage);
  }
};

/**
 * 랜딩 페이지를 복사합니다.
 * @param {number|string} landingPageId - 복사할 랜딩 페이지 ID
 * @returns {Promise<Object>} 복사된 랜딩 페이지 정보 또는 성공 여부
 */
export const copyLandingPage = async (landingPageId) => {
  try {
    if (!landingPageId) {
      throw new Error('랜딩 페이지 ID가 필요합니다.');
    }

    const response = await apiClient.post(`/landing-pages/${landingPageId}/copy`);
    if (response.data && response.data.success) {
      return response.data.data || response.data;
    }
    throw new Error(response.data?.error?.message || response.data?.message || '랜딩 페이지 복사에 실패했습니다.');
  } catch (error) {
    const errorMessage = handleApiError(error, '랜딩 페이지 복사에 실패했습니다.');
    throw new Error(errorMessage);
  }
};
