import { ref, computed } from 'vue';
import {
  getAllMenuTree,
  updateMenu,
  deleteMenu,
  createMenu,
  convertBackendMenuToTreeData,
  convertTreeDataToBackendMenu,
  getMenuApiErrorMessage
} from '@/api/menu';

/**
 * 메뉴 관리를 위한 공통 상태 관리 composable
 */
export function useMenuManagement() {
  // 기본 상태
  const isLoading = ref(true);
  const error = ref(null);
  const menuTreeData = ref([]);
  const originalMenuData = ref([]);
  const selectedNode = ref(null);
  const hasChanges = ref(false);

  // 계산된 속성들
  const totalMenuCount = computed(() => {
    const countNodes = (nodes) => {
      let count = 0;
      nodes.forEach(node => {
        count++;
        if (node.nodes && node.nodes.length > 0) {
          count += countNodes(node.nodes);
        }
      });
      return count;
    };
    return countNodes(menuTreeData.value);
  });

  const visibleMenuCount = computed(() => {
    const countVisible = (nodes) => {
      let count = 0;
      nodes.forEach(node => {
        if (node.visible) count++;
        if (node.nodes && node.nodes.length > 0) {
          count += countVisible(node.nodes);
        }
      });
      return count;
    };
    return countVisible(menuTreeData.value);
  });

  const hiddenMenuCount = computed(() => totalMenuCount.value - visibleMenuCount.value);

  // 평면화된 메뉴 리스트 (상위 메뉴 선택용)
  const flatMenuList = computed(() => {
    const flatten = (nodes, level = 0) => {
      const result = [];
      nodes.forEach(node => {
        result.push({ ...node, level });
        if (node.nodes && node.nodes.length > 0) {
          result.push(...flatten(node.nodes, level + 1));
        }
      });
      return result;
    };
    return flatten(menuTreeData.value);
  });

  // 메뉴 데이터 로드
  const loadMenuData = async () => {
    try {
      isLoading.value = true;
      error.value = null;

      // 백엔드에서 전체 메뉴 트리 조회
      const response = await getAllMenuTree();
      const backendMenuData = response.data || response;

      // 백엔드 데이터를 프론트엔드 tree 구조로 변환
      const treeData = convertBackendMenuToTreeData(backendMenuData);

      // 모든 노드에 expanded 속성과 nodes 배열 보장
      const addExpandedProperty = (nodes) => {
        nodes.forEach(node => {
          if (node.expanded === undefined) {
            node.expanded = false;
          }
          // nodes 배열이 없으면 빈 배열로 초기화
          if (!node.nodes) {
            node.nodes = [];
          }
          if (node.nodes.length > 0) {
            addExpandedProperty(node.nodes);
          }
        });
      };

      addExpandedProperty(treeData);

      menuTreeData.value = treeData;
      originalMenuData.value = JSON.parse(JSON.stringify(treeData)); // 깊은 복사

    } catch (err) {
      console.error('메뉴 데이터 로드 실패:', err);
      error.value = getMenuApiErrorMessage(err);

      // API 실패 시 빈 배열로 설정
      menuTreeData.value = [];
      originalMenuData.value = [];
    } finally {
      isLoading.value = false;
    }
  };

  // 메뉴 표시/숨김 토글
  const toggleVisibility = async (node) => {
    try {
      const newStatus = node.visible ? 'INACTIVE' : 'ACTIVE';
      const updateData = {
        ...convertTreeDataToBackendMenu(node),
        status: newStatus
      };

      await updateMenu(node.id, updateData);
      node.visible = !node.visible;
      node.status = newStatus;

      // 성공 메시지 표시
      const statusText = node.visible ? '표시' : '숨김';
      console.log(`메뉴 "${node.label}"이(가) ${statusText} 상태로 변경되었습니다.`);

    } catch (err) {
      console.error('메뉴 표시/숨김 변경 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      throw new Error(`메뉴 상태 변경 실패: ${errorMessage}`);
    }
  };

  // 메뉴 생성
  const createNewMenu = async (menuData) => {
    try {
      await createMenu(menuData);
      await loadMenuData(); // 메뉴 데이터 다시 로드
      return true;
    } catch (err) {
      console.error('메뉴 생성 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      throw new Error(`메뉴 생성 실패: ${errorMessage}`);
    }
  };

  // 메뉴 수정
  const updateExistingMenu = async (menuId, menuData) => {
    try {
      await updateMenu(menuId, menuData);
      await loadMenuData(); // 메뉴 데이터 다시 로드
      return true;
    } catch (err) {
      console.error('메뉴 수정 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      throw new Error(`메뉴 수정 실패: ${errorMessage}`);
    }
  };

  // 메뉴 삭제
  const deleteExistingMenu = async (menuId) => {
    try {
      await deleteMenu(menuId);
      await loadMenuData(); // 메뉴 데이터 다시 로드
      return true;
    } catch (err) {
      console.error('메뉴 삭제 실패:', err);
      const errorMessage = getMenuApiErrorMessage(err);
      throw new Error(`메뉴 삭제 실패: ${errorMessage}`);
    }
  };

  // 하위 메뉴 존재 여부 확인
  const hasChildMenus = (node) => {
    return node && node.nodes && node.nodes.length > 0;
  };

  // 메뉴 데이터 새로고침
  const refreshMenuData = async () => {
    await loadMenuData();
  };

  // 변경사항 초기화
  const resetChanges = () => {
    menuTreeData.value = JSON.parse(JSON.stringify(originalMenuData.value));
    hasChanges.value = false;
    selectedNode.value = null;
  };

  return {
    // 상태
    isLoading,
    error,
    menuTreeData,
    originalMenuData,
    selectedNode,
    hasChanges,
    
    // 계산된 속성
    totalMenuCount,
    visibleMenuCount,
    hiddenMenuCount,
    flatMenuList,
    
    // 메서드
    loadMenuData,
    toggleVisibility,
    createNewMenu,
    updateExistingMenu,
    deleteExistingMenu,
    hasChildMenus,
    refreshMenuData,
    resetChanges
  };
}
