<template>
  <div class="user-crud-permission-management">
    <div class="header">
      <h4>{{ selectedMenu?.label }} - 사용자별 CRUD 권한 관리</h4>
      <p class="description">
        선택된 메뉴에 대한 모든 사용자의 읽기(R), 쓰기(C), 수정(U), 삭제(D) 권한을 개별적으로 설정할 수 있습니다.
      </p>
      <div class="auto-save-notice">
        <span class="notice-icon">💡</span>
        <span class="notice-text">권한 토글 시 자동으로 서버에 저장됩니다.</span>
      </div>
    </div>

    <!-- 검색 및 필터 -->
    <div class="search-section">
      <div class="search-controls">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="사용자 이메일로 검색..."
          class="search-input"
          @input="onSearchInput"
        />
        <button @click="refreshUserPermissions" class="refresh-btn">
          새로고침
        </button>
      </div>
    </div>

    <!-- 로딩 상태 -->
    <div v-if="isLoading" class="loading">
      <p>사용자 권한 데이터를 불러오는 중...</p>
    </div>

    <!-- 에러 상태 -->
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="refreshUserPermissions" class="retry-btn">다시 시도</button>
    </div>

    <!-- 사용자 권한 테이블 -->
    <div v-else-if="userPermissions.length > 0" class="permissions-table-container">
      <table class="permissions-table">
        <thead>
          <tr>
            <th>사용자 정보</th>
            <th>읽기(R)</th>
            <th>쓰기(C)</th>
            <th>수정(U)</th>
            <th>삭제(D)</th>
            <th>권한 출처</th>
            <th>메모</th>
            <th>작업</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="permission in userPermissions" :key="permission.userEmail">
            <td class="user-info">
              <div class="user-details">
                <div class="user-email">{{ permission.userEmail }}</div>
                <div class="user-name">{{ permission.userName || '이름 없음' }}</div>
                <div class="user-role">{{ permission.roleName || '역할 정보 없음' }}</div>
              </div>
            </td>
            <td>
              <PermissionToggle
                :value="permission.canRead"
                :disabled="permission.permissionSource === 'default'"
                @change="(value) => updatePermission(permission.userEmail, 'canRead', value)"
              />
            </td>
            <td>
              <PermissionToggle
                :value="permission.canWrite"
                :disabled="permission.permissionSource === 'default'"
                @change="(value) => updatePermission(permission.userEmail, 'canWrite', value)"
              />
            </td>
            <td>
              <PermissionToggle
                :value="permission.canUpdate"
                :disabled="permission.permissionSource === 'default'"
                @change="(value) => updatePermission(permission.userEmail, 'canUpdate', value)"
              />
            </td>
            <td>
              <PermissionToggle
                :value="permission.canDelete"
                :disabled="permission.permissionSource === 'default'"
                @change="(value) => updatePermission(permission.userEmail, 'canDelete', value)"
              />
            </td>
            <td>
              <span
                :class="['permission-source', permission.permissionSource || 'custom']"
                :title="(permission.permissionSource || 'custom') === 'default' ? '기본 권한 (역할 기반)' : '개별 설정된 권한'"
              >
                {{ (permission.permissionSource || 'custom') === 'default' ? '기본' : '개별' }}
              </span>
            </td>
            <td>
              <input
                v-if="(permission.permissionSource || 'custom') === 'custom'"
                v-model="permission.permissionNote"
                type="text"
                class="note-input"
                placeholder="권한 부여 사유"
                @blur="updatePermissionNote(permission.userEmail, permission.permissionNote)"
              />
              <span v-else class="default-note">{{ permission.permissionNote || '-' }}</span>
            </td>
            <td class="actions">
              <button
                v-if="(permission.permissionSource || 'custom') === 'custom'"
                @click="saveUserPermission(permission.userEmail)"
                class="save-btn"
                :disabled="isSaving"
              >
                저장
              </button>
              <button
                v-if="(permission.permissionSource || 'custom') === 'custom'"
                @click="resetToDefault(permission.userEmail)"
                class="reset-btn"
                :disabled="isSaving"
              >
                기본값으로
              </button>
              <button
                v-if="(permission.permissionSource || 'custom') === 'default'"
                @click="enableCustomPermission(permission.userEmail)"
                class="customize-btn"
              >
                개별 설정
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 페이지네이션 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button
          @click="goToPage(pagination.number - 1)"
          :disabled="pagination.number === 0"
          class="page-btn"
        >
          이전
        </button>
        <span class="page-info">
          {{ pagination.number + 1 }} / {{ pagination.totalPages }} 페이지
          (총 {{ pagination.totalElements }}명)
        </span>
        <button
          @click="goToPage(pagination.number + 1)"
          :disabled="pagination.number >= pagination.totalPages - 1"
          class="page-btn"
        >
          다음
        </button>
      </div>
    </div>

    <!-- 데이터 없음 -->
    <div v-else class="no-data">
      <p>권한 설정 가능한 사용자가 없습니다.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { 
  getMenuUserPermissions, 
  setUserCrudPermission, 
  updateUserCrudPermission, 
  deleteUserPermission,
  getMenuApiErrorMessage 
} from '@/api/menu';
import PermissionToggle from './PermissionToggle.vue';

const props = defineProps({
  selectedMenu: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['permission-updated']);

// 상태 관리
const isLoading = ref(false);
const error = ref('');
const isSaving = ref(false);
const searchQuery = ref('');
const userPermissions = ref([]);
const pagination = ref({
  number: 0,
  size: 10,
  totalElements: 0,
  totalPages: 0
});

// 검색 디바운스
let searchTimeout = null;
const onSearchInput = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    loadUserPermissions();
  }, 500);
};

// 사용자 권한 데이터 로드
const loadUserPermissions = async () => {
  if (!props.selectedMenu) return;

  try {
    isLoading.value = true;
    error.value = '';

    const params = {
      page: pagination.value.number,
      size: pagination.value.size,
      search: searchQuery.value
    };

    const response = await getMenuUserPermissions(props.selectedMenu.id, params);

    if (response.success) {
      // 백엔드에서 단순 배열로 반환하므로 직접 할당
      userPermissions.value = response.data || [];

      // 페이지네이션 정보가 없으므로 클라이언트에서 처리
      const totalElements = userPermissions.value.length;
      const pageSize = pagination.value.size;
      const totalPages = Math.ceil(totalElements / pageSize);

      pagination.value = {
        number: 0, // 현재는 페이지네이션 없이 전체 데이터 표시
        size: pageSize,
        totalElements: totalElements,
        totalPages: totalPages
      };
    } else {
      throw new Error(response.message || '권한 데이터 로드 실패');
    }
  } catch (err) {
    console.error('사용자 권한 로드 실패:', err);
    error.value = getMenuApiErrorMessage(err);
  } finally {
    isLoading.value = false;
  }
};

// 권한 업데이트 (즉시 저장)
const updatePermission = async (userEmail, permissionType, value) => {
  const user = userPermissions.value.find(u => u.userEmail === userEmail);
  if (user) {
    user[permissionType] = value;
    // 개별 설정으로 변경
    if (user.permissionSource === 'default') {
      user.permissionSource = 'custom';
    }

    // 즉시 서버에 저장
    await saveUserPermissionImmediately(userEmail);
  }
};

// 권한 메모 업데이트
const updatePermissionNote = (userEmail, note) => {
  const user = userPermissions.value.find(u => u.userEmail === userEmail);
  if (user) {
    user.permissionNote = note;
  }
};

// 사용자 권한 저장 (수동 저장용)
const saveUserPermission = async (userEmail) => {
  const user = userPermissions.value.find(u => u.userEmail === userEmail);
  if (!user) return;

  try {
    isSaving.value = true;

    const permissionData = {
      canRead: user.canRead,
      canWrite: user.canWrite,
      canUpdate: user.canUpdate,
      canDelete: user.canDelete,
      permissionNote: user.permissionNote || ''
    };

    const response = user.permissionSource === 'custom'
      ? await updateUserCrudPermission(props.selectedMenu.id, userEmail, permissionData)
      : await setUserCrudPermission(props.selectedMenu.id, userEmail, permissionData);

    if (response.success) {
      user.permissionSource = 'custom';
      emit('permission-updated');
      alert('권한이 성공적으로 저장되었습니다.');
    } else {
      throw new Error(response.message || '권한 저장 실패');
    }
  } catch (err) {
    console.error('권한 저장 실패:', err);
    alert(`권한 저장 실패: ${getMenuApiErrorMessage(err)}`);
  } finally {
    isSaving.value = false;
  }
};

// 즉시 저장 함수 (토글 시 자동 호출)
const saveUserPermissionImmediately = async (userEmail) => {
  const user = userPermissions.value.find(u => u.userEmail === userEmail);
  if (!user) return;

  try {
    const permissionData = {
      canRead: user.canRead,
      canWrite: user.canWrite,
      canUpdate: user.canUpdate,
      canDelete: user.canDelete,
      permissionNote: user.permissionNote || ''
    };

    const response = await setUserCrudPermission(props.selectedMenu.id, userEmail, permissionData);

    if (response.success) {
      user.permissionSource = 'custom';
      emit('permission-updated');
      // 즉시 저장이므로 알림 없음 (선택사항)
      console.log(`${userEmail} 사용자의 권한이 자동 저장되었습니다.`);
    } else {
      throw new Error(response.message || '권한 저장 실패');
    }
  } catch (err) {
    console.error('권한 즉시 저장 실패:', err);
    // 즉시 저장 실패 시에만 알림
    alert(`권한 저장 실패: ${getMenuApiErrorMessage(err)}`);
  }
};

// 기본값으로 복원
const resetToDefault = async (userEmail) => {
  if (!confirm('이 사용자의 권한을 기본값으로 복원하시겠습니까?')) return;

  try {
    isSaving.value = true;
    
    const response = await deleteUserPermission(props.selectedMenu.id, userEmail);
    
    if (response.success) {
      await loadUserPermissions(); // 데이터 다시 로드
      emit('permission-updated');
      alert('권한이 기본값으로 복원되었습니다.');
    } else {
      throw new Error(response.message || '권한 복원 실패');
    }
  } catch (err) {
    console.error('권한 복원 실패:', err);
    alert(`권한 복원 실패: ${getMenuApiErrorMessage(err)}`);
  } finally {
    isSaving.value = false;
  }
};

// 개별 설정 활성화
const enableCustomPermission = (userEmail) => {
  const user = userPermissions.value.find(u => u.userEmail === userEmail);
  if (user) {
    user.permissionSource = 'custom';
  }
};

// 페이지 이동
const goToPage = (page) => {
  if (page >= 0 && page < pagination.value.totalPages) {
    pagination.value.number = page;
    loadUserPermissions();
  }
};

// 새로고침
const refreshUserPermissions = () => {
  pagination.value.number = 0;
  loadUserPermissions();
};

// 선택된 메뉴 변경 감지
watch(() => props.selectedMenu, (newMenu) => {
  if (newMenu) {
    pagination.value.number = 0;
    searchQuery.value = '';
    loadUserPermissions();
  }
}, { immediate: true });

onMounted(() => {
  if (props.selectedMenu) {
    loadUserPermissions();
  }
});
</script>

<style scoped>
.user-crud-permission-management {
  padding: 20px 0;
}

.header {
  margin-bottom: 20px;
}

.header h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
}

.description {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.auto-save-notice {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
  padding: 8px 12px;
  background: #e8f5e8;
  border: 1px solid #c3e6c3;
  border-radius: 4px;
  font-size: 13px;
}

.notice-icon {
  font-size: 16px;
}

.notice-text {
  color: #2d5a2d;
  font-weight: 500;
}

.search-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.search-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-input {
  flex: 1;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.refresh-btn, .retry-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.refresh-btn:hover, .retry-btn:hover {
  background: #0056b3;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.error {
  background: #fee;
  color: #c33;
}

.permissions-table-container {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.permissions-table {
  width: 100%;
  border-collapse: collapse;
}

.permissions-table th,
.permissions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.permissions-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #495057;
  font-size: 14px;
}

.permissions-table tbody tr:hover {
  background: #f8f9fa;
}

.user-info {
  min-width: 200px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-email {
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.user-name {
  color: #666;
  font-size: 12px;
}

.user-role {
  color: #007bff;
  font-size: 11px;
  font-weight: bold;
}

.permission-source {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
}

.permission-source.default {
  background: #e9ecef;
  color: #6c757d;
}

.permission-source.custom {
  background: #d4edda;
  color: #155724;
}

.note-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 12px;
}

.default-note {
  color: #6c757d;
  font-size: 12px;
  font-style: italic;
}

.actions {
  min-width: 120px;
}

.save-btn, .reset-btn, .customize-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: bold;
  margin-right: 4px;
  margin-bottom: 2px;
  transition: all 0.2s;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover:not(:disabled) {
  background: #545b62;
}

.customize-btn {
  background: #007bff;
  color: white;
}

.customize-btn:hover {
  background: #0056b3;
}

.save-btn:disabled, .reset-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

.page-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #0056b3;
}

.page-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #495057;
  font-weight: bold;
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .permissions-table {
    font-size: 12px;
  }

  .permissions-table th,
  .permissions-table td {
    padding: 8px 4px;
  }

  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    max-width: none;
  }
}
</style>
