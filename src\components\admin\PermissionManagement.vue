<template>
  <div class="permission-management">
    <h3>메뉴 권한 관리</h3>
    <p class="description">각 메뉴에 대한 역할별 접근 권한 및 사용자별 CRUD 권한을 설정할 수 있습니다.</p>

    <!-- 메뉴 선택 -->
    <div class="menu-selector">
      <label for="selectedMenuForPermission">권한을 설정할 메뉴:</label>
      <select
        id="selectedMenuForPermission"
        :value="selectedMenuForPermission?.id || ''"
        @change="handleMenuChange"
      >
        <option value="">메뉴를 선택하세요</option>
        <option
          v-for="menu in flatMenuList"
          :key="menu.id"
          :value="menu.id"
        >
          {{ '　'.repeat(menu.level) }}{{ menu.label }}
        </option>
      </select>
    </div>

    <!-- 권한 관리 탭 네비게이션 -->
    <div v-if="selectedMenuForPermission" class="permission-tabs">
      <button
        @click="activePermissionTab = 'role'"
        class="tab-btn"
        :class="{ active: activePermissionTab === 'role' }"
      >
        역할별 권한
      </button>
      <button
        @click="activePermissionTab = 'user-crud'"
        class="tab-btn"
        :class="{ active: activePermissionTab === 'user-crud' }"
      >
        사용자별 CRUD 권한
      </button>
    </div>

    <!-- 권한 설정 컨텐츠 -->
    <div v-if="selectedMenuForPermission" class="permission-content">
      <!-- 역할별 권한 탭 -->
      <div v-if="activePermissionTab === 'role'" class="permission-table">
        <h4>{{ selectedMenuForPermission.label }} - 역할별 권한 설정</h4>

        <!-- 역할별 권한 -->
        <div class="role-permissions">
          <h5>역할별 접근 권한</h5>
          <table class="permissions-table">
            <thead>
              <tr>
                <th>역할</th>
                <th>접근 권한</th>
                <th>작업</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="role in availableRoles" :key="role.id">
                <td>{{ role.name }}</td>
                <td>
                  <label class="permission-toggle">
                    <input
                      type="checkbox"
                      :checked="rolePermissions[role.id]"
                      @change="$emit('toggle-role-permission', role.id, $event.target.checked)"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </td>
                <td>
                  <button
                    @click="$emit('save-role-permission', role.id)"
                    class="save-permission-btn"
                    :disabled="!hasPermissionChanges"
                  >
                    저장
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 개별 사용자 접근 권한 (기존 기능 유지) -->
        <div class="user-permissions">
          <h5>개별 사용자 접근 권한</h5>
          <div class="user-permission-form">
            <div class="form-row">
              <input
                v-model="userPermissionForm.userEmail"
                type="email"
                placeholder="사용자 이메일"
                class="user-email-input"
              />
              <select v-model="userPermissionForm.isAccessible" class="permission-select">
                <option value="Y">접근 허용</option>
                <option value="N">접근 거부</option>
              </select>
              <input
                v-model="userPermissionForm.permissionNote"
                type="text"
                placeholder="권한 부여 사유"
                class="permission-note-input"
              />
              <button
                @click="$emit('save-user-permission')"
                class="add-user-permission-btn"
                :disabled="!userPermissionForm.userEmail"
              >
                추가
              </button>
            </div>
          </div>

          <!-- 설정된 사용자 권한 목록 -->
          <div v-if="userPermissions.length > 0" class="user-permissions-list">
            <table class="permissions-table">
              <thead>
                <tr>
                  <th>사용자 이메일</th>
                  <th>접근 권한</th>
                  <th>권한 부여 사유</th>
                  <th>작업</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="permission in userPermissions" :key="permission.userEmail">
                  <td>{{ permission.userEmail }}</td>
                  <td>
                    <span :class="permission.isAccessible === 'Y' ? 'access-allowed' : 'access-denied'">
                      {{ permission.isAccessible === 'Y' ? '허용' : '거부' }}
                    </span>
                  </td>
                  <td>{{ permission.permissionNote }}</td>
                  <td>
                    <button
                      @click="$emit('remove-user-permission', permission.userEmail)"
                      class="remove-permission-btn"
                    >
                      삭제
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 사용자별 CRUD 권한 탭 -->
      <div v-if="activePermissionTab === 'user-crud'" class="permission-table">
        <UserCrudPermissionManagement
          :selected-menu="selectedMenuForPermission"
          @permission-updated="$emit('permission-updated')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import UserCrudPermissionManagement from './UserCrudPermissionManagement.vue';

const props = defineProps({
  flatMenuList: {
    type: Array,
    default: () => []
  },
  selectedMenuForPermission: {
    type: Object,
    default: null
  },
  rolePermissions: {
    type: Object,
    default: () => ({})
  },
  userPermissions: {
    type: Array,
    default: () => []
  },
  hasPermissionChanges: {
    type: Boolean,
    default: false
  },
  userPermissionForm: {
    type: Object,
    default: () => ({
      userEmail: '',
      isAccessible: 'Y',
      permissionNote: ''
    })
  },
  availableRoles: {
    type: Array,
    default: () => [
      { id: 'PROJECT_ADMIN', name: '프로젝트 관리자' },
      { id: 'SUB_ADMIN', name: '서브 관리자' },
      { id: 'VIEWER', name: '뷰어' }
    ]
  }
});

const emit = defineEmits([
  'load-permissions',
  'toggle-role-permission',
  'save-role-permission',
  'save-user-permission',
  'remove-user-permission',
  'update:selectedMenuForPermission',
  'permission-updated'
]);

// 권한 관리 탭 상태
const activePermissionTab = ref('role');

// 메뉴 선택 변경 핸들러
const handleMenuChange = (event) => {
  const selectedMenuId = event.target.value;

  // 선택된 메뉴 ID로 실제 메뉴 객체 찾기
  const selectedMenu = selectedMenuId ?
    props.flatMenuList.find(menu => menu.id === selectedMenuId) :
    null;

  // 부모 컴포넌트에 선택된 메뉴 업데이트 알림
  emit('update:selectedMenuForPermission', selectedMenu);
  emit('load-permissions');
};
</script>

<style scoped>
/* 권한 관리 스타일 */
.permission-management {
  padding: 20px 0;
}

.description {
  color: #666;
  margin-bottom: 30px;
}

.menu-selector {
  margin-bottom: 30px;
}

.menu-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.menu-selector select {
  width: 100%;
  max-width: 400px;
  padding: 10px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.permission-table {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.permission-table h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
}

.role-permissions,
.user-permissions {
  margin-bottom: 30px;
}

.role-permissions h5,
.user-permissions h5 {
  margin-bottom: 15px;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

.permissions-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.permissions-table th,
.permissions-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.permissions-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #495057;
}

.permission-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.permission-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.permission-toggle input:checked + .toggle-slider {
  background-color: #28a745;
}

.permission-toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.save-permission-btn,
.add-user-permission-btn,
.remove-permission-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: all 0.2s;
}

.save-permission-btn {
  background: #007bff;
  color: white;
}

.save-permission-btn:hover:not(:disabled) {
  background: #0056b3;
}

.save-permission-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-user-permission-btn {
  background: #28a745;
  color: white;
}

.add-user-permission-btn:hover:not(:disabled) {
  background: #218838;
}

.remove-permission-btn {
  background: #dc3545;
  color: white;
}

.remove-permission-btn:hover {
  background: #c82333;
}

.user-permission-form {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.user-email-input,
.permission-select,
.permission-note-input {
  padding: 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
}

.user-email-input {
  flex: 1;
  min-width: 200px;
}

.permission-select {
  min-width: 120px;
}

.permission-note-input {
  flex: 1;
  min-width: 150px;
}

.access-allowed {
  color: #28a745;
  font-weight: bold;
}

.access-denied {
  color: #dc3545;
  font-weight: bold;
}

/* 권한 관리 탭 스타일 */
.permission-tabs {
  display: flex;
  border-bottom: 2px solid #dee2e6;
  margin: 20px 0;
}

.permission-tabs .tab-btn {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.permission-tabs .tab-btn:hover {
  color: #007bff;
  background: #f8f9fa;
}

.permission-tabs .tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
}

.permission-content {
  margin-top: 20px;
}
</style>
