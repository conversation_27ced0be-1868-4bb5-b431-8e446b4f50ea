<template>
  <div class="design-options-section" v-if="!isEditMode">
    <label>QR 코드 디자인 옵션</label>
    <div class="design-options-container">
      <!-- 로고 업로드 섹션 -->
      <div class="logo-upload-section">
        <h4>로고 이미지 업로드</h4>
        <p class="field-note">QR 코드 중앙에 표시될 로고 이미지를 업로드하세요. (최대 크기: 1MB)</p>

        <div class="logo-preview-container">
          <!-- 로고 미리보기 -->
          <div v-if="logoPreview" class="logo-preview">
            <img :src="logoPreview" alt="Logo Preview" class="logo-image" @error="handleImageError" />
            <button type="button" @click="removeLogo" class="remove-logo-btn" :disabled="isEditMode">삭제</button>
          </div>

          <!-- 로고 업로드 버튼 -->
          <div v-if="!logoPreview" class="logo-upload">
            <input
              type="file"
              ref="logoInput"
              accept="image/*"
              @change="handleLogoUpload"
              style="display: none;"
            />
            <button
              type="button"
              @click="triggerLogoUpload"
              class="upload-btn"
              :disabled="isEditMode"
            >
              로고 업로드
            </button>
          </div>
        </div>

        <!-- 로고 크기 조절 -->
        <div v-if="logoPreview" class="logo-size-control">
          <label for="logoSize">로고 크기 (QR 코드 영역 대비 %)</label>
          <input
            type="range"
            id="logoSize"
            v-model.number="logoSize"
            min="10"
            max="40"
            step="5"
            :disabled="isEditMode"
          />
          <div class="field-note">최대 40%까지 적용됩니다 (스캔율 저하 방지).</div>
          <div class="field-note" style="margin-top: 2px; color: #888;">
            참고: 오류 복원 수준 변경 시 QR 코드 패턴 밀도가 달라져 로고의 시각적 크기가 다르게 보일 수 있습니다.
          </div>
        </div>
      </div>

      <!-- 고급 디자인 옵션 -->
      <div class="advanced-options">
        <h4>고급 디자인 옵션</h4>
        <div class="form-group design-group">
          <label for="qrColor">QR 코드 색상</label>
          <input type="color" id="qrColor" v-model="qrColor" :disabled="isEditMode" />
        </div>
        <div class="form-group design-group">
          <label for="qrBgColor">배경 색상</label>
          <input type="color" id="qrBgColor" v-model="qrBgColor" :disabled="isEditMode" />
        </div>
        <div class="form-group design-group">
          <label for="qrEyeColor">눈 색상</label>
          <input type="color" id="qrEyeColor" v-model="qrEyeColor" :disabled="isEditMode" />
        </div>
        <div class="form-group design-group slider-group">
          <label for="qrEyeStyle">눈 모양 (네모 → 동그라미)</label>
          <input type="range" id="qrEyeStyle" v-model.number="qrEyeStyle" min="0" max="1" step="1" :disabled="isEditMode" />
          <span>{{ qrEyeStyle === 0 ? '네모' : '동그라미' }}</span>
        </div>

        <!-- 오류 복원 수준 선택 -->
        <div class="form-group design-group">
          <label for="qrErrorCorrectionLevel">오류 복원 수준</label>
          <select id="qrErrorCorrectionLevel" v-model="qrErrorCorrectionLevel" class="error-correction-select" :disabled="isEditMode">
            <option value="L">낮음 (L: ~7% 복원)</option>
            <option value="M">중간 (M: ~15% 복원)</option>
            <option value="Q">높음 (Q: ~25% 복원)</option>
            <option value="H">매우 높음 (H: ~30% 복원)</option>
          </select>
          <div class="field-note">
            오류 복원 수준이 높을수록 QR 코드 스캔이 잘 됩니다.
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

// Props 정의
const props = defineProps({
  isEditMode: {
    type: Boolean,
    default: false
  },
  qrColor: {
    type: String,
    default: '#000000'
  },
  qrBgColor: {
    type: String,
    default: '#FFFFFF'
  },
  qrEyeColor: {
    type: String,
    default: '#000000'
  },
  qrEyeStyle: {
    type: Number,
    default: 0
  },
  qrErrorCorrectionLevel: {
    type: String,
    default: 'M'
  },
  logoPreview: {
    type: String,
    default: ''
  },
  logoSize: {
    type: Number,
    default: 40
  },
  logoFile: {
    type: File,
    default: null
  }
});

// Emits 정의
const emit = defineEmits([
  'update:qrColor',
  'update:qrBgColor',
  'update:qrEyeColor',
  'update:qrEyeStyle',
  'update:qrErrorCorrectionLevel',
  'update:logoPreview',
  'update:logoSize',
  'update:logoFile',
  'logo-upload',
  'logo-remove',
  'image-error'
]);

// 로컬 상태
const logoInput = ref(null);

// 양방향 바인딩을 위한 computed 속성들
const qrColor = ref(props.qrColor);
const qrBgColor = ref(props.qrBgColor);
const qrEyeColor = ref(props.qrEyeColor);
const qrEyeStyle = ref(props.qrEyeStyle);
const qrErrorCorrectionLevel = ref(props.qrErrorCorrectionLevel);
const logoPreview = ref(props.logoPreview);
const logoSize = ref(props.logoSize);

// Props 변경 감지
watch(() => props.qrColor, (newValue) => qrColor.value = newValue);
watch(() => props.qrBgColor, (newValue) => qrBgColor.value = newValue);
watch(() => props.qrEyeColor, (newValue) => qrEyeColor.value = newValue);
watch(() => props.qrEyeStyle, (newValue) => qrEyeStyle.value = newValue);
watch(() => props.qrErrorCorrectionLevel, (newValue) => qrErrorCorrectionLevel.value = newValue);
watch(() => props.logoPreview, (newValue) => logoPreview.value = newValue);
watch(() => props.logoSize, (newValue) => logoSize.value = newValue);

// 로컬 상태 변경을 부모로 전달
watch(qrColor, (newValue) => emit('update:qrColor', newValue));
watch(qrBgColor, (newValue) => emit('update:qrBgColor', newValue));
watch(qrEyeColor, (newValue) => emit('update:qrEyeColor', newValue));
watch(qrEyeStyle, (newValue) => emit('update:qrEyeStyle', newValue));
watch(qrErrorCorrectionLevel, (newValue) => emit('update:qrErrorCorrectionLevel', newValue));
watch(logoPreview, (newValue) => emit('update:logoPreview', newValue));
watch(logoSize, (newValue) => emit('update:logoSize', newValue));

// 로고 업로드 트리거
const triggerLogoUpload = () => {
  logoInput.value?.click();
};

// 로고 업로드 처리
const handleLogoUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 파일 크기 검사 (1MB)
  const maxSize = 1 * 1024 * 1024;
  if (file.size > maxSize) {
    alert('파일 크기가 1MB를 초과합니다. 더 작은 파일을 선택해주세요.');
    return;
  }

  // 이미지 파일 검사
  if (!file.type.startsWith('image/')) {
    alert('이미지 파일만 업로드할 수 있습니다.');
    return;
  }

  // 파일과 미리보기 URL 생성
  const reader = new FileReader();
  reader.onload = (e) => {
    logoPreview.value = e.target.result;
    emit('update:logoFile', file);
    emit('logo-upload', { file, preview: e.target.result });
  };
  reader.readAsDataURL(file);
};

// 로고 삭제
const removeLogo = () => {
  logoPreview.value = '';
  emit('update:logoFile', null);
  emit('logo-remove');
  
  // 파일 입력 요소 리셋
  if (logoInput.value) {
    logoInput.value.value = '';
  }
};

// 이미지 에러 처리
const handleImageError = (event) => {
  // no-image 이미지로 대체
  event.target.src = '/src/assets/image/no-image.png';
  emit('image-error', event);
};
</script>

<style scoped>
.design-options-section {
  margin-bottom: 20px;
}

.design-options-section > label {
  display: block;
  margin-bottom: 15px;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.design-options-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  background-color: #f9f9f9;
}

.logo-upload-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.logo-upload-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
}

.field-note {
  font-size: 12px;
  color: #666;
  margin-bottom: 15px;
}

.logo-preview-container {
  margin-bottom: 15px;
}

.logo-preview {
  position: relative;
  display: inline-block;
  margin-bottom: 10px;
}

.logo-image {
  max-width: 100px;
  max-height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.remove-logo-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  cursor: pointer;
  font-size: 12px;
}

.remove-logo-btn:hover {
  background-color: #c82333;
}

.upload-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.upload-btn:hover {
  background-color: #218838;
}

.logo-size-control {
  margin-top: 15px;
}

.logo-size-control label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.logo-size-control input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
}

.advanced-options h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #333;
}

.design-group {
  margin-bottom: 15px;
}

.design-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.design-group input[type="color"] {
  width: 50px;
  height: 35px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.design-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.slider-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.slider-group label {
  flex: 1;
  margin-bottom: 0;
}

.slider-group input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.slider-group span {
  min-width: 60px;
  font-size: 14px;
  color: #666;
}

.error-correction-select {
  background-color: white;
}

.form-group {
  margin-bottom: 15px;
}
</style>
