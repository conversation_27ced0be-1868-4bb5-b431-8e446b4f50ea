<template>
  <div class="landing-management">
    <h1>랜딩 페이지 관리</h1>

    <!-- 프로젝트가 없고 SUPER_ADMIN이 아닌 경우 -->
    <div v-if="!currentProject && authStore.user?.roleId !== 'SUPER_ADMIN'" class="no-projects-message">
      <p>선택된 프로젝트가 없습니다. 프로젝트를 선택해주세요.</p>
    </div>

    <!-- 프로젝트가 있거나 SUPER_ADMIN인 경우 랜딩 페이지 목록 표시 -->
    <div v-else class="landing-list-container">
      <div class="filters">
        <div class="filter-group">
          <button @click="navigateToCreateLanding" class="create-btn">새 랜딩 페이지 추가</button>
          
          <!-- 페이지당 항목 수 선택 UI -->
          <div class="items-per-page-selector">
            <span>페이지당 항목:</span>
            <div class="items-per-page-buttons">
              <button 
                @click="changeItemsPerPage(10)" 
                :class="['item-count-btn', itemsPerPage === 10 ? 'active' : '']">
                10개
              </button>
              <button 
                @click="changeItemsPerPage(30)" 
                :class="['item-count-btn', itemsPerPage === 30 ? 'active' : '']">
                30개
              </button>
              <button 
                @click="changeItemsPerPage(50)" 
                :class="['item-count-btn', itemsPerPage === 50 ? 'active' : '']">
                50개
              </button>
            </div>
          </div>
        </div>
        <div class="filter-group">
          <label for="searchTypeSelect">검색 유형:</label>
          <select id="searchTypeSelect" v-model="searchType">
            <option v-for="type in availableSearchTypes" :key="type.value" :value="type.value">
              {{ type.label }}
            </option>
          </select>

          <label for="searchInput">검색어:</label>
          <input
            id="searchInput"
            type="text"
            v-model="searchQuery"
            placeholder="검색어 입력"
            @input="handleSearch"
            @keyup.enter="searchLandingPages"
          />

          <button @click="searchLandingPages" class="search-btn">검색</button>
          <button @click="resetSearch" class="reset-btn">초기화</button>
        </div>
      </div>

      <div class="landing-table-container">
        <table v-if="filteredLandingPages.length > 0" class="landing-table">
          <thead>
            <tr>
              <th>번호</th>
              <th @click="sortBy('projectName')">프로젝트 이름 <span v-if="sortKey === 'projectName'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('landingPageName')">랜딩 페이지 이름 <span v-if="sortKey === 'landingPageName'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>URL</th>
              <th @click="sortBy('status')">상태 <span v-if="sortKey === 'status'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('createDate')">생성일 <span v-if="sortKey === 'createDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('validFromDate')">유효 시작일 <span v-if="sortKey === 'validFromDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th @click="sortBy('validToDate')">유효 종료일 <span v-if="sortKey === 'validToDate'">{{ sortOrder === 'asc' ? '▲' : '▼' }}</span></th>
              <th>기능</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(landingPage, index) in filteredLandingPages" :key="landingPage.landingPageId">
              <td>{{ calculateIndex(index) }}</td>
              <td>{{ landingPage.projectName }}</td>
              <td>{{ landingPage.pageTitle }}</td>
              <td class="url-content">{{ truncateText(landingPage.url, 30) }}</td>
              <td>{{ formatStatus(landingPage.status) }}</td>
              <td>{{ formatDate(landingPage.createDate) }}</td>
              <td>{{ formatDate(landingPage.validFromDate) }}</td>
              <td>{{ formatDate(landingPage.validToDate) }}</td>
              <td>
                <button @click="viewLandingPage(landingPage.landingPageId)" class="action-btn view-btn">보기</button>
                <button @click="editLandingPage(landingPage.landingPageId)" class="action-btn edit-btn">수정</button>
                <button @click="confirmDeleteLandingPage(landingPage)" class="action-btn delete-btn">삭제</button>
                <button @click="copyLandingPageById(landingPage.landingPageId)" class="action-btn">복사</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div v-else-if="isLoading" class="loading-message">
          <div class="loading-spinner"></div>
          <p>랜딩 페이지 정보를 불러오는 중입니다...</p>
        </div>

        <div v-else-if="error" class="error-message">
          <p>{{ error }}</p>
        </div>

        <div v-else class="no-data-message">
          <p>랜딩 페이지 데이터가 없습니다.</p>
        </div>
      </div>

      <!-- 페이지네이션 -->
      <div class="pagination" v-if="landingPages.length > 0">
        <button
          @click="goToPage(0)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &laquo;
        </button>
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 0"
          class="pagination-btn"
        >
          &lt;
        </button>

        <span class="page-info">{{ displayPage }} / {{ totalPages || 1 }}</span>

        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &gt;
        </button>
        <button
          @click="goToPage(totalPages - 1)"
          :disabled="currentPage >= (totalPages - 1) || totalPages <= 1"
          class="pagination-btn"
        >
          &raquo;
        </button>
      </div>
    </div>

    <!-- 삭제 확인 모달 -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-content">
        <h3>랜딩 페이지 삭제 확인</h3>
        <p><strong>{{ selectedLandingPage?.landingPageName }}</strong> 랜딩 페이지를 삭제하시겠습니까?</p>
        <p class="warning">이 작업은 되돌릴 수 없습니다.</p>
        <div class="modal-actions">
          <button @click="handleDeleteLandingPage" class="confirm-btn" :disabled="isDeleting">
            {{ isDeleting ? '삭제 중...' : '삭제' }}
          </button>
          <button @click="cancelDelete" class="cancel-btn">취소</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getLandingPages, deleteLandingPage, getAllLandingPages, copyLandingPage } from '@/api/landing';
import { handleApiError } from '@/utils/errorHandler';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const authStore = useAuthStore();
const landingPages = ref([]);
const isLoading = ref(true);
const error = ref(null);
const searchQuery = ref('');
const appliedSearchQuery = ref(''); // 실제 검색에 적용된 검색어
const searchType = ref('landingPageName'); // 기본 검색 타입 설정
const appliedSearchType = ref('landingPageName'); // 실제 검색에 적용된 검색 타입
const showDeleteModal = ref(false);
const selectedLandingPage = ref(null);
const isDeleting = ref(false);
const availableSearchTypes = ref([]); // 서버에서 제공하는 검색 유형 목록

// 페이지네이션 관련 상태
const currentPage = ref(0); // 서버 페이지네이션은 0부터 시작
const itemsPerPage = ref(10); // 페이지당 항목 수
const totalElements = ref(0); // 전체 항목 수
const totalPages = ref(0); // 전체 페이지 수

// 페이지 번호 표시용 (1부터 시작하는 페이지 번호)
const displayPage = computed(() => {
  return currentPage.value + 1;
});

// 인덱스 계산 함수
const calculateIndex = (index) => {
  // 마지막 번호부터 시작하도록 변경
  return totalElements.value - (currentPage.value * itemsPerPage.value + index);
};

// 페이지당 항목 수 변경 함수
const changeItemsPerPage = (count) => {
  itemsPerPage.value = count;
  currentPage.value = 0; // 첫 페이지로 이동
  fetchLandingPages(); // 랜딩 페이지 목록 다시 로드
};

// 정렬 관련 상태
const sortKey = ref('landingPageId');
const sortOrder = ref('desc');

// 현재 선택된 프로젝트
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 프로젝트 ID 변경 감지
watch(() => currentProject.value?.projectId, (newProjectId, oldProjectId) => {
  if (newProjectId && newProjectId !== oldProjectId) {
    fetchLandingPages();
  }
});

// 정렬된 랜딩 페이지 목록 (서버에서 검색된 결과)
const filteredLandingPages = computed(() => {
  return sortedLandingPages.value;
});

// 정렬된 랜딩 페이지 목록
const sortedLandingPages = computed(() => {
  const sortedArray = [...landingPages.value];

  sortedArray.sort((a, b) => {
    let aValue = a[sortKey.value];
    let bValue = b[sortKey.value];

    // 날짜 형식 처리
    if (sortKey.value.includes('Date') && aValue && bValue) {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // 문자열 처리
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    // null 또는 undefined 처리
    if (aValue === null || aValue === undefined) return sortOrder.value === 'asc' ? -1 : 1;
    if (bValue === null || bValue === undefined) return sortOrder.value === 'asc' ? 1 : -1;

    // 정렬 순서에 따라 비교
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  return sortedArray;
});

// 정렬 함수
const sortBy = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortKey.value = key;
    sortOrder.value = 'asc';
  }
};

// 상태 포맷팅
const formatStatus = (status) => {
  const statusMap = {
    'PUBLISHED': '활성',
    'DRAFT': '비활성',
    'EXPIRED': '만료됨',
    'DELETED': '삭제됨'
  };
  return statusMap[status] || status;
};

// 날짜 포맷팅 - 초 단위까지만 표시
const formatDate = (dateString) => {
  if (!dateString) return '-';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return new Intl.DateTimeFormat('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// 텍스트 길이 제한 함수
const truncateText = (text, maxLength) => {
  if (!text) return '-';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

// 랜딩 페이지 생성 페이지로 이동
const navigateToCreateLanding = () => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 랜딩 페이지 생성 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'landing-form',
    query
  });
};

// 랜딩 페이지 상세 보기
const viewLandingPage = (landingPageId) => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 랜딩 페이지 상세 보기 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'landing-view',
    params: { landingPageId: landingPageId.toString() },
    query
  });
};

// 랜딩 페이지 수정 페이지로 이동
const editLandingPage = (landingPageId) => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 랜딩 페이지 수정 가능
  const query = {};

  // 프로젝트 ID가 있는 경우에만 query에 추가
  if (currentProject.value?.projectId) {
    query.projectId = currentProject.value.projectId;
  }

  router.push({
    name: 'landing-form',
    params: { landingPageId: landingPageId.toString() },
    query
  });
};

// 삭제 확인 모달 표시
const confirmDeleteLandingPage = (landingPage) => {
  selectedLandingPage.value = landingPage;
  showDeleteModal.value = true;
};

// 삭제 취소
const cancelDelete = () => {
  showDeleteModal.value = false;
  selectedLandingPage.value = null;
};

// 랜딩 페이지 삭제 처리
const handleDeleteLandingPage = async () => {
  if (!selectedLandingPage.value) return;

  isDeleting.value = true;

  try {
    // 삭제 API 호출
    await deleteLandingPage(selectedLandingPage.value.landingPageId);

    // 성공 시 목록에서 제거
    landingPages.value = landingPages.value.filter(landing => landing.landingPageId !== selectedLandingPage.value.landingPageId);

    // 모달 닫기
    showDeleteModal.value = false;
    selectedLandingPage.value = null;

    // 성공 메시지
    alert('랜딩 페이지가 성공적으로 삭제되었습니다.');
  } catch (err) {
    error.value = handleApiError(err, '랜딩 페이지 삭제 중 오류가 발생했습니다.');
  } finally {
    isDeleting.value = false;
  }
};

// 날짜 관련 검색 타입인지 확인하는 함수
const isDateSearchType = (type) => {
  // 날짜 관련 필드명 목록
  const dateFields = ['startDate', 'endDate'];
  return dateFields.includes(type);
};

// 날짜 형식 변환 함수 (2025. 05. 17. -> 2025-05-17)
const formatDateForSearch = (dateString) => {
  if (!dateString) return '';

  // 정규식을 사용하여 날짜 형식 변환
  // 2025. 05. 17. 또는 2025.05.17. 또는 2025.5.17. 등의 형식을 처리
  const dateMatch = dateString.match(/(\d{4})[.\s-]*(\d{1,2})[.\s-]*(\d{1,2})/);

  if (dateMatch) {
    const year = dateMatch[1];
    // 월과 일이 한 자리 수인 경우 앞에 0을 추가
    const month = dateMatch[2].padStart(2, '0');
    const day = dateMatch[3].padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 매칭되지 않으면 원래 문자열 반환
  return dateString;
};

// 검색 버튼 클릭 시 실행되는 함수
const searchLandingPages = () => {
  currentPage.value = 0; // 검색 시 첫 페이지로 이동

  // 날짜 관련 검색 타입인 경우 날짜 형식 변환
  if (searchType.value && isDateSearchType(searchType.value) && searchQuery.value) {
    // 원본 검색어 저장
    const originalQuery = searchQuery.value;
    // 변환된 날짜 형식으로 검색어 업데이트
    const formattedDate = formatDateForSearch(originalQuery);

    if (formattedDate !== originalQuery) {
      searchQuery.value = formattedDate;
    }
  }

  // 검색 쿼리와 타입을 적용
  appliedSearchQuery.value = searchQuery.value;
  appliedSearchType.value = searchType.value;

  // 서버에서 검색 결과 가져오기
  fetchLandingPages();
};

// 검색 처리 (디바운스 제거)
const handleSearch = () => {
  // 검색어 입력 중에는 아무 동작도 하지 않음
  // 사용자가 직접 검색 버튼을 클릭하거나 Enter 키를 눌러야 서버에서 데이터를 가져옴
};

// 페이지 이동 함수
const goToPage = (page) => {

  // 페이지 범위 검사 (totalPages가 0이면 최소 1페이지로 간주)
  const maxPage = Math.max(totalPages.value - 1, 0);
  if (page < 0 || page > maxPage) {
    console.warn(`페이지 범위 초과: ${page}, 최대 페이지: ${maxPage}`);
    return;
  }

  // 페이지 변경 및 데이터 로드
  currentPage.value = page;
  fetchLandingPages();

  // 상단으로 스크롤
  window.scrollTo(0, 0);
};

// 페이지 변경 시 상단으로 스크롤
watch(currentPage, () => {
  window.scrollTo(0, 0);
});

// 정렬 또는 페이지당 항목 수 변경 시 데이터 다시 로드
watch([sortKey, sortOrder, itemsPerPage], () => {
  // 정렬 또는 페이지당 항목 수 변경 시 첫 페이지로 이동
  currentPage.value = 0;
  fetchLandingPages();
});

// 랜딩 페이지 목록 불러오기
const fetchLandingPages = async () => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 모든 랜딩 페이지를 가져올 수 있음
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

  // SUPER_ADMIN이 아니고 프로젝트 ID가 없는 경우 데이터를 가져오지 않음
  if (!isSuperAdmin && !currentProject.value?.projectId) {
    landingPages.value = [];
    return;
  }

  isLoading.value = true;
  error.value = null;

  try {
    let response;

    // 검색 파라미터 준비
    const searchParams = {};
    if (appliedSearchQuery.value && appliedSearchType.value) {
      searchParams.searchType = appliedSearchType.value;
      searchParams.searchKeyword = appliedSearchQuery.value;
    }

    // SUPER_ADMIN인 경우 모든 랜딩 페이지를 가져오는 API 호출
    if (isSuperAdmin) {
      // 현재 선택된 프로젝트 ID가 있으면 파라미터로 전달
      const projectId = currentProject.value?.projectId || null;

      if (projectId) {
        response = await getAllLandingPages(currentPage.value, itemsPerPage.value, projectId, searchParams);
      } else {
        response = await getAllLandingPages(currentPage.value, itemsPerPage.value, null, searchParams);
      }
    } else {
      // 일반 사용자는 프로젝트별 랜딩 페이지 목록 요청
      response = await getLandingPages(currentProject.value.projectId, currentPage.value, itemsPerPage.value, searchParams);
    }

    // 새로운 API 응답 구조 처리: { success: true, data: { content: [...], totalElements, totalPages, ... } }
    if (response && response.success && response.data) {
      const responseData = response.data;

      // 페이지네이션 정보 업데이트
      if (responseData.totalPages !== undefined) {
        totalPages.value = responseData.totalPages;
      } else {
        // 서버에서 totalPages가 없는 경우 계산
        const total = responseData.totalElements || (responseData.content?.length || 0);
        totalPages.value = Math.ceil(total / itemsPerPage.value) || 1;
      }

      totalElements.value = responseData.totalElements || 0;

      // 검색 유형 목록 업데이트
      if (responseData.availableSearchTypes && Array.isArray(responseData.availableSearchTypes)) {
        availableSearchTypes.value = responseData.availableSearchTypes;

        // 검색 유형 기본값 설정 (searchType이 비어있거나 availableSearchTypes에 없을 때)
        if (availableSearchTypes.value.length > 0 && 
            (!searchType.value || !availableSearchTypes.value.some(type => type.value === searchType.value))) {
          searchType.value = availableSearchTypes.value[0].value;
          appliedSearchType.value = availableSearchTypes.value[0].value;
        }
      }

      // 랜딩 페이지 데이터 업데이트
      if (responseData.content && Array.isArray(responseData.content)) {
        landingPages.value = responseData.content;

        // 데이터가 있지만 totalPages가 0인 경우 최소 1로 설정
        if (landingPages.value.length > 0 && totalPages.value === 0) {
          totalPages.value = 1;
        }
      } else if (Array.isArray(responseData)) {
        // 이전 응답 구조 처리 (호환성 유지)
        landingPages.value = responseData;
        totalPages.value = 1;
        totalElements.value = responseData.length;
      } else {
        console.warn('랜딩 페이지 데이터가 없거나 배열이 아닙니다.');
        landingPages.value = [];
      }
    } else {
      console.warn('서버 응답에 데이터가 없습니다.');
      landingPages.value = [];
      totalPages.value = 0;
      totalElements.value = 0;
    }
  } catch (err) {
    error.value = handleApiError(err, '랜딩 페이지 목록을 불러오는 중 오류가 발생했습니다.');
    console.error('랜딩 페이지 목록 조회 실패:', err);
    landingPages.value = [];
    totalPages.value = 0;
    totalElements.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 프로젝트 변경 이벤트 핸들러
const handleProjectChange = () => {
  // 프로젝트 변경 시 검색 초기화
  resetSearch();
};

// 검색 초기화 함수
const resetSearch = () => {
  searchQuery.value = '';
  appliedSearchQuery.value = '';
  // 서버에서 제공하는 검색 유형이 있으면 첫 번째 값을 사용, 없으면 기본값 사용
  if (availableSearchTypes.value.length > 0) {
    searchType.value = availableSearchTypes.value[0].value;
    appliedSearchType.value = availableSearchTypes.value[0].value;
  } else {
    searchType.value = 'landingPageName';
    appliedSearchType.value = 'landingPageName';
  }
  currentPage.value = 0;
  fetchLandingPages();
};

// 랜딩 페이지 복사 함수
const copyLandingPageById = async (landingPageId) => {
  if (!landingPageId) {
    alert('유효하지 않은 랜딩 페이지 ID입니다.');
    return;
  }

  if (confirm('이 랜딩 페이지를 복사하시겠습니까?')) {
    try {
      // 랜딩 페이지 복사 API 함수 호출
      await copyLandingPage(landingPageId);
      alert('랜딩 페이지가 성공적으로 복사되었습니다.');
      // 랜딩 페이지 목록 새로고침
      await fetchLandingPages();
    } catch (error) {
      console.error('랜딩 페이지 복사 중 오류 발생:', error);
      // 오류 메시지 처리
      alert(error.message || '랜딩 페이지 복사 중 오류가 발생했습니다.');
    }
  }
};

// 컴포넌트 마운트 시 랜딩 페이지 목록 로드 및 이벤트 리스너 등록
onMounted(() => {
  // SUPER_ADMIN인 경우 프로젝트 ID 없이도 랜딩 페이지 목록을 로드
  const isSuperAdmin = authStore.user?.roleId === 'SUPER_ADMIN';

  if (isSuperAdmin || currentProject.value?.projectId) {
    fetchLandingPages();
  }

  // 프로젝트 변경 이벤트 리스너 등록
  window.addEventListener('project-changed', handleProjectChange);
});

// 컴포넌트 언마운트 시 이벤트 리스너 제거
onUnmounted(() => {
  window.removeEventListener('project-changed', handleProjectChange);
});
</script>

<style scoped>
.landing-management {
  padding: 20px;
}

h1 {
  margin-bottom: 20px;
  color: #333;
}

.landing-list-container {
  margin-top: 20px;
}

/* 필터 스타일 */
.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
  color: #555;
}

.filter-group input, .filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 200px;
}

.create-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.create-btn:hover {
  background-color: #45a049;
}

.search-btn {
  background-color: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: #0b7dda;
}

.reset-btn {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.reset-btn:hover {
  background-color: #d32f2f;
}

/* 테이블 스타일 */
.landing-table-container {
  margin-top: 20px;
  overflow-x: auto;
}

.landing-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.landing-table th {
  background-color: #f2f2f2;
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid #ddd;
  font-weight: 600;
  color: #333;
  cursor: pointer;
  user-select: none;
}

.landing-table th:hover {
  background-color: #e6e6e6;
}

.landing-table td {
  padding: 12px 8px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.landing-table tr:hover {
  background-color: #f9f9f9;
}

.url-content {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 액션 버튼 스타일 */
.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-right: 6px;
  transition: background-color 0.2s;
}

.view-btn {
  background-color: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background-color: #bbdefb;
}

.edit-btn {
  background-color: #fff8e1;
  color: #ffa000;
}

.edit-btn:hover {
  background-color: #ffecb3;
}

.delete-btn {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-btn:hover {
  background-color: #ffcdd2;
}

/* 로딩 및 에러 메시지 스타일 */
.loading-message,
.no-data-message,
.no-projects-message {
  padding: 40px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin: 20px 0;
}

/* 페이지네이션 스타일 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  border-color: #aaa;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  padding: 0 12px;
  color: #555;
}

/* 디버깅 정보 스타일 */
.debug-info {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.debug-info p {
  margin: 5px 0;
}

/* 반응형 스타일 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }

  .filter-group {
    width: 100%;
  }

  .landing-table th,
  .landing-table td {
    padding: 8px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  color: #333;
}

.warning {
  color: #f44336;
  font-weight: bold;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-btn {
  background-color: #F44336;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background-color: #da190b;
}

.confirm-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-btn:hover {
  background-color: #d0d0d0;
}
/* 페이지당 항목 수 선택 UI 스타일 */
.landing-management .items-per-page-selector {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.landing-management .items-per-page-buttons {
  display: flex;
  margin-left: 10px;
}

.landing-management .item-count-btn {
  padding: 6px 10px;
  margin: 0 4px;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.landing-management .item-count-btn:hover {
  background-color: #e0e0e0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.landing-management .item-count-btn.active {
  background-color: #4CAF50 !important;
  color: white !important;
  border-color: #45a049 !important;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
</style>
